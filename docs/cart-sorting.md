# Сортировка корзины

## Описание

Реализована функциональность сортировки товаров в корзине с использованием Prisma ORM для эффективной работы с базой данных.

## Возможности сортировки

### 1. Сортировка по дате добавления (по умолчанию)
- По умолчанию товары сортируются по дате добавления в корзину (новые сначала)
- Выполняется на уровне базы данных через Prisma

### 2. Сортировка по полям корзины
Поля, которые сортируются на уровне базы данных:
- `addedAt` / `date_added` - дата добавления в корзину
- `qty` - количество товара в корзине
- `created_at` - дата создания записи
- `updated_at` - дата обновления записи

### 3. Сортировка по полям товаров
Поля, которые сортируются после получения данных:
- `prod_price` - цена товара
- `prod_name` - название товара
- `prod_count` - количество на складе
- `prod_size` - размер товара (с поддержкой сложной сортировки)
- Любые другие поля товаров

## Использование через tRPC

```typescript
// Получение товаров корзины с сортировкой
const cartProducts = await trpc.getCartProducts.query({
  page: 1,
  sorting: [
    { column: 'addedAt', direction: 'desc' }, // новые сначала
    { column: 'prod_price', direction: 'asc' } // затем по цене по возрастанию
  ]
})
```

## Примеры сортировки

### Сортировка по дате добавления
```typescript
sorting: [
  { column: 'addedAt', direction: 'desc' } // новые сначала
]
```

### Сортировка по цене
```typescript
sorting: [
  { column: 'prod_price', direction: 'asc' } // от дешевых к дорогим
]
```

### Сортировка по количеству в корзине
```typescript
sorting: [
  { column: 'qty', direction: 'desc' } // больше товаров сначала
]
```

### Сортировка по названию товара
```typescript
sorting: [
  { column: 'prod_name', direction: 'asc' } // по алфавиту
]
```

### Множественная сортировка
```typescript
sorting: [
  { column: 'prod_price', direction: 'asc' }, // сначала по цене
  { column: 'qty', direction: 'desc' },       // затем по количеству
  { column: 'addedAt', direction: 'desc' }    // затем по дате добавления
]
```

## Техническая реализация

### Архитектура
1. **Уровень базы данных**: Сортировка полей `cart_items` выполняется через Prisma orderBy
2. **Уровень приложения**: Сортировка полей товаров выполняется после получения данных
3. **Fallback**: Если критерии сортировки равны, используется дата добавления

### Оптимизация
- Сортировка на уровне БД для полей корзины обеспечивает высокую производительность
- Минимальное количество запросов к базе данных
- Сохранение порядка сортировки при группировке по категориям

### Поддержка сложных случаев
- **Размеры товаров**: Специальная логика для парсинга и сравнения размеров (например, "10x20x5")
- **Русская локализация**: Корректная сортировка русских названий
- **Числовые поля**: Правильное преобразование и сравнение числовых значений

## Структура ответа

```typescript
{
  categories: [
    {
      cat_id: number,
      cat_title: string,
      products: [
        {
          prod_id: number,
          prod_name: string,
          prod_price: number,
          qty: number,           // количество в корзине
          addedAt: Date,         // дата добавления в корзину
          updatedAt: Date,       // дата обновления в корзине
          // ... другие поля товара
        }
      ]
    }
  ],
  meta: {
    totalCount: number
  }
}
```

## Тестирование

Созданы unit-тесты для проверки:
- Построения правильных orderBy запросов для Prisma
- Сортировки товаров по различным критериям
- Парсинга размеров товаров
- Множественной сортировки

Запуск тестов:
```bash
npm test -- tests/cart-sorting.test.ts
```
