import Order from 'App/Models/Order'
import OrderSnapshot from 'App/Models/OrderSnapshot'

export default {
  async init() {
    await this.getList()
  },

  async getList(limit = 5) {
    // TODO: Пропускать заказы если снапшотов меньше 3.
    const orders = await Order.query()
      //.where() // Добавить выборку по датам
      // .where('order_datetime', '<', '2023-05-01 00:00:00')
      .andWhereRaw('order_datetime < DATE_SUB(CURDATE(), INTERVAL 30 DAY)')
      // .andWhere('order_datetime', '>', '2021-01-01 00:00:00')
      .preload('snapshots', (query) => query.select('ID').orderBy('ID', 'desc'))
      //.limit(limit)
      .orderBy('order_id', 'desc')

    console.log('orders length: ', orders.length)

    await Promise.all(orders.map(this.cleaner))

    console.log('allDone')
  },

  async cleaner(order: Order) {
    if (order.snapshots?.length < 2) {
      return false
    }

    //console.log('continur');

    let IDs = order.snapshots.map((snapshot) => snapshot.ID)

    IDs.shift()
    // IDs.pop()

    const r = await OrderSnapshot.query().whereIn('ID', IDs).delete()
    console.log(order.order_id + ':, removes length: ', r)
  }
}
