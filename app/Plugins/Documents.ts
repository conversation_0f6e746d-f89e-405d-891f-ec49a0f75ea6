import { Template<PERSON><PERSON><PERSON>, TemplateHandlerOptions } from 'easy-template-x'
import Application from '@ioc:Adonis/Core/Application'

import * as path from 'path'
import * as fs from 'fs'

import Env from '@ioc:Adonis/Core/Env'

import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'

import { DateTime } from 'luxon'
import { splitSum } from 'App/Helpers/splitSum'
import { numToText } from 'App/Helpers/numToText'

import ExcelJS from 'exceljs'
import { findValInObject, flattenObject, findValInObjByKeysArray } from 'App/Helpers/utils'

import Client from 'App/Models/Client'
import Org from 'App/Models/Org'
import Product from 'App/Models/Product'
import Order from 'App/Models/Order'
import Cart from 'App/Models/Cart'
import { PochtaRU } from 'App/Plugins/PochtaRU'
import LangDict from 'App/Models/LangDict'
import loadSettings from 'App/Helpers/loadSettings'

const XlsxTemplate = require('xlsx-template')
const util = require('util')
const readdir = util.promisify(fs.readdir)
const writeFilePromisified = util.promisify(fs.writeFile)

const Range = require('exceljs/lib/doc/range.js')
const DOCS_UPLOAD_PATH = process.env.NODE_ENV === 'production' ? path.resolve(Env.get('UPLOAD_PATH') + '/docs') : Application.publicPath('data/docs')

const PizZip = require('pizzip')
const Docxtemplater = require('docxtemplater')

const getFormattedPrice = (value: number) => new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(Number(value))

interface InsertColumn {
  pos?: string
  startCell: {
    row: number
    col: number
  }
  dataKey?: string
  data: any
  mergeCells?: boolean
  mergeRange?: Array<number>
}

interface InsertData {
  pos?: string
  startRowPos: number
  column: InsertColumn
}

interface OfferItem {
  id: number
  qty: number
}

export class Document {
  type: string
  data: object
  document: {
    data: any
    type: string
  }

  constructor(type: 'xlsx' | 'docx') {
    this.document = {}
    this.document.type = type
  }

  async xlsx(filename) {
    //let file = fs.readFileSync(DOCS_UPLOAD_PATH + '/' + filename)
    // console.log("imageRootPath:", DOCS_UPLOAD_PATH + '/templateImages')

    const documentData = this.document.data

    const filepath = DOCS_UPLOAD_PATH + '/' + filename
    const workbook = new ExcelJS.Workbook()
    await workbook.xlsx.readFile(filepath)
    const worksheet = workbook.worksheets[0]

    const _customMergeCell = (...cells) => {
      const dimensions = new Range(cells)
      const master = worksheet.getCell(dimensions.top, dimensions.left)

      for (let i = dimensions.top; i <= dimensions.bottom; i++) {
        for (let j = dimensions.left; j <= dimensions.right; j++) {
          // merge all but the master cell
          if (i > dimensions.top || j > dimensions.left) {
            worksheet.getCell(i, j).merge(master, false)
          }
        }
      }
      // index merge
      worksheet['_merges'][master.address] = dimensions
    }

    const duplicateRowWithMergedCells = (sheet: any, row: number, count: number) => {
      sheet.duplicateRow(row, count, true)

      const merges: string[] = sheet.model.merges
      // Find all merges inside initial row
      const rowMerges = merges.filter((range) => range.match(`\\w+${row}:\\w+${row}`))

      Array.from({ length: count }, (_, index) => {
        const newRow = row + index + 1
        // Unmerge everything in a newRow so we dont run into conflicts
        merges.filter((range) => range.match(`\\w+${newRow}:\\w+${newRow}`)).map((range) => sheet.unMergeCells(range))
        // Merge the same cells as in the initial row
        rowMerges.map((range) => range.replace(new RegExp(`${row}`, 'g'), `${newRow}`)).map((range) => _customMergeCell(range) /* sheet.mergeCells(range) */)
      })
    }

    let insertData: InsertData[] = []
    let pageBreaks: number[] = []

    worksheet.eachRow((row, currentRowNumber) => {
      row.eachCell((cell, currentColNumber) => {
        const repFields = cell.value?.toString().match(/(?<=\${)(.*?)(?=})/gm)

        if (repFields) {
          repFields.map((repField) => {
            if (repField.startsWith('table:')) {
              const keys = repField.split(':')[1].split('.')
              const itemKey = keys.pop() || ''

              let _pos = cell.$col$row

              if (!insertData.find((x) => x.column?.dataKey == itemKey && x.pos == _pos)) {
                insertData.push({
                  pos: _pos,
                  startRowPos: currentRowNumber,
                  column: {
                    dataKey: itemKey,
                    data: findValInObjByKeysArray(documentData, keys).map((item) => item[itemKey]),
                    mergeCells: true,
                    startCell: {
                      col: Number(cell.col),
                      row: Number(cell.row)
                    }
                  }
                })
              }
            } else {
              if (cell.value?.toString()?.match(/\${(.*?)}/m)?.[1] == 'pageBreak') {
                pageBreaks.push(row.number)
                cell.value = ''
              }

              const repFieldVal = findValInObjByKeysArray(documentData, repField) //documentData[repField]
              cell.value = cell.value?.toString().replace(/\${(.*?)}/m, repFieldVal)
              //const prevCell = row.getCell(Number(cell.col) - 1)
            }
          })
        }
      })
    })

    // Подсчет макс. длина данных
    let InsertRowsLength = Math.max(...insertData.map((item) => Number(item.column.data?.length || 1)))
    // Предварительно вставить строки со скопированной стилизацией. Кол-во строк рассчитывается из макс. длины их всех вставляемых колонок

    if (!insertData?.length) {
      return await workbook.xlsx.writeBuffer()
    }

    // (InsertRowsLength - 1) для того, что бы перезаписать строку с плейсхолдером.

    // METHOD-1
    // worksheet.insertRows((insertData[0].startRowPos + 1), Array.from({ length: InsertRowsLength - 1 }, i => []), 'i+')
    // METHOD-2
    // worksheet.duplicateRow((insertData[0].startRowPos + 1), InsertRowsLength - 1, true)
    // METHOD-3
    duplicateRowWithMergedCells(worksheet, insertData[0].startRowPos, InsertRowsLength - 1)

    // Вставляем данные
    insertData.map((item) => {
      item.column.data.map((di, index) => {
        // Вычисляем текущую строчку. (Стартовая позиция плейсхолдера + текущая итерация)
        let rowPos = item.column.startCell.row + index

        // Берем строку из ранее дублированных выше.
        let workRow = worksheet.getRow(rowPos)
        let workCell = workRow.getCell(item.column.startCell.col)

        workCell.value = di
      })
    })

    // сдвигаем изображения которые были ниже созданных строк.
    const images = worksheet.getImages()
    const imageOffset = InsertRowsLength - 1

    images.map((image) => {
      if (image.range.tl.nativeRow > insertData[0]?.startRowPos) {
        image.range.tl.nativeRow += imageOffset
        image.range.br.nativeRow += imageOffset
      }
    })

    // добавляем разрывы страниц
    pageBreaks.map((rowIndex) => {
      const index = rowIndex > insertData[0].startRowPos ? rowIndex + (InsertRowsLength - 1) : rowIndex
      let row = worksheet.getRow(index)
      row.addPageBreak()
    })

    return await workbook.xlsx.writeBuffer({
      zip: {
        compression: 'DEFLATE' // 'STORE',
      },
      useStyles: true
    })
  }

  async docx(filename) {
    const documentData = this.document.data

    const filepath = path.resolve(DOCS_UPLOAD_PATH, filename) //DOCS_UPLOAD_PATH + '/' + filename
    const content = fs.readFileSync(filepath, 'binary')

    const zip = new PizZip(content)
    const doc = new Docxtemplater(zip, {
      paragraphLoop: true,
      linebreaks: true
    })

    // Render the document (Replace {first_name} by John, {last_name} by Doe, ...)
    const data = flattenObject(documentData)
    // console.dir("🚀 ~ file: Documents.ts:234 ~ Document ~ docx ~ data:", data)
    console.log(JSON.stringify(data, null, 4))

    doc.render(data)

    const buf = doc.getZip().generate({
      type: 'nodebuffer',
      // compression: DEFLATE adds a compression step.
      // For a 50MB output document, expect 500ms additional CPU time
      compression: 'DEFLATE'
    })

    // buf is a nodejs Buffer, you can either write it to a
    // file or res.send it with express for example.
    // fs.writeFileSync(path.resolve(__dirname, 'output.docx'), buf)

    return buf
  }
}

export class OfferDocument extends Document {
  Client: Client
  Org: Org
  Order: Order
  Products: Product[]
  ProcessedProducts: any[]
  WhosaleMode: boolean | undefined
  ProductData: OfferItem[]
  OfferDefSum: number
  OfferSum: number
  ShippingIndex: number
  ShippingType: 'standard' | 'express'
  CountryId: number
  OfferWeight: number
  Filename: string
  OfferExp: number
  ShippingPrice: number

  constructor({
    client,
    org,
    WhosaleMode = undefined,
    productData,
    shippingIndex,
    shippingType = 'standard',
    shippingPrice,
    countryId = 643,
    filename,
    offerExp = 3
  }: {
    client: Client
    org: Org
    WhosaleMode: boolean | undefined
    productData: OfferItem[]
    shippingIndex: number
    shippingType: 'standard' | 'express'
    shippingPrice: number | undefined
    countryId: number
    filename: string
    offerExp: number
  }) {
    super('xlsx')

    this.Client = client
    this.Org = org
    this.WhosaleMode = WhosaleMode
    this.ProductData = productData
    this.ShippingIndex = shippingIndex
    this.ShippingType = shippingType
    this.CountryId = countryId
    this.Filename = filename
    this.OfferExp = offerExp
    this.ShippingPrice = shippingPrice

    if (this.ShippingPrice) {
      this.ShippingPrice = Number(this.ShippingPrice)
    }

    if (!this.Filename) {
      throw new Error('filename is not defined')
    }
  }

  async makeProducts() {
    if (!this.ProductData?.length) {
      throw new Error('IDs list is empty')
    }

    this.Products = await Product.findMany(this.ProductData.map((i) => i.id))

    if (typeof this.WhosaleMode == 'undefined') {
      this.OfferDefSum = this.Products.reduce((acc, product) => {
        let qty = this.ProductData.find((x) => x.id == product.prod_id)?.qty || 0
        return (acc += Number(product.prod_price) * qty)
      }, 0)

      if (this.OfferDefSum >= Number(Env.get('DISCOUNT_START_SUM'))) {
        this.WhosaleMode = true
      }
    }

    this.ProcessedProducts = this.Products.map((product, index) => {
      let item = {}

      item['index'] = index + 1
      item['prod_analogsku'] = product.prod_analogsku
      item['qty'] = this.ProductData.find((x) => x.id == product.prod_id)?.qty || 0
      item['price'] = this.WhosaleMode ? product.whosaleprice || product.prod_discount : product.prod_price
      item['_price'] = Number(item['price'])
      item['sum'] = (Number(item['price']) * Number(item.qty)).toFixed(2).replace('.', ',')
      item['title'] = `${product.prod_purpose} ${product.prod_size} ${product.prod_manuf} ${product.prod_sku} (${product.prod_analogsku})`
      item['price'] = item['price'].toFixed(2).replace('.', ',')

      item['weight'] = Number(product.prod_weight || 100)

      return item
    })

    this.OfferSum = this.ProcessedProducts.reduce((sum, item) => (sum += item._price * Number(item.qty)), 0)
    this.OfferWeight = this.ProcessedProducts.reduce((sum, item) => (sum += item.weight), 0)

    if (isNaN(this.OfferWeight)) this.OfferWeight = 500
  }

  async makeOfferFile() {
    return await this.xlsx(this.Filename)
  }

  async init() {
    await this.makeProducts()

    const _pochta = new PochtaRU()
    let shippingprice

    try {
      shippingprice = this.ShippingPrice ?? (await (await _pochta.calculate(this.CountryId, this.ShippingIndex))[this.ShippingType](this.OfferWeight))
    } catch (error) {
      console.log('shippingprice: ', error)
      shippingprice = 901
    }

    const data = {
      products: this.ProcessedProducts,
      shipping: {
        price: getFormattedPrice(Number(shippingprice)),
        title: this.ShippingType == 'standard' ? 'Почта РФ' : 'Курьер'
      },
      client: this.Client,
      org: this.Org,
      receiver: `${this.Org?.org_name}, ИНН ${this.Org?.org_inn || ''}, КПП ${this.Org?.org_kpp || ''}, ${this.Org?.org_adress || ''}`,
      indexShipping: this.ProcessedProducts.length + 1,
      indexDiscount: this.ProcessedProducts.length + 2,
      discount: 0,
      offerExp: DateTime.now().plus({ days: this.OfferExp }).setLocale('ru').toFormat('DDD'),
      offerSum: getFormattedPrice(Number(this.OfferSum + shippingprice)),
      offerWeight: this.OfferWeight,
      priceMode: this.WhosaleMode ? 'Опт' : 'Розница',
      offerNumber: new Date().getTime().toString().slice(2, -5),
      date: DateTime.now().setLocale('ru').toFormat('dd.LL.y')
    }

    this.document.data = data
    return await this.makeOfferFile()
  }
}

export class OrderDocument extends Document {
  order: Order
  orderId: number
  document

  constructor({ orderId, type }: { orderId: number; type: 'xlsx' | 'docx' }) {
    super(type)

    this.document = {}
    this.document.type = type
    this.orderId = orderId
  }

  async findOrder(orderId) {
    this.order = await Order.findOrFail(orderId)
    //await this.order.load('client', query => query.preload('org'))
    await this.order.load('snapshots', (query) => query.orderBy('ID', 'desc') /* .first() */)
  }

  async makeFile(filename) {
    return await this[this.document.type](filename)
  }

  makeReceiver(client?) {
    if (!client) client = this.order.client

    client.org = client.org || client.client_company
    return `${client.org?.org_name}, ИНН ${client.org?.org_inn}, КПП ${client.org?.org_kpp}, ${client.org?.org_adress}`
  }

  makeReceiverFromSnapshot(client) {
    return `${client.client_company?.org_name}, ИНН ${client.client_company?.org_inn}, КПП ${client.client_company?.org_kpp}, ${client.client_company?.org_adress}`
  }

  makeEnAddressFromSnapshot(client) {
    return client.client_company?.org_name
      ? client.client_company?.org_adress
      : `${client.client_country} ${client.client_city} ${client.client_street} ${client.client_house} ${client.client_flat || ' '}`
  }

  makeEnNameFromSnapshot(client) {
    return client.client_company?.org_name ? client.client_company?.org_name : client.client_name
  }

  getDprice({ price = 0, discount = 0, isNumber = false }) {
    if (isNumber) return price - (price * (discount || 1)) / 100
    return new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(price - (price * (discount || 1)) / 100)
  }

  async init(locale?: string) {
    let orderTranslateFields = ['discount', 'totalSum', 'shippingprice', 'productsSum']
    let productItemTranslateFields = ['price', '_price', 'sum', '_sum']

    await this.findOrder(this.orderId)

    let snapshot: SnapshotBodyInterface = this.order.snapshots[0]?.body

    // console.log('this.order.snapshots[0]', Object.keys(this.order.snapshots[0]))

    // this.order.client = snapshot.client

    let productsFromSnapshot: Array<SnapshotBodyItemInterface> = this.order.snapshots[0]?.body?.items
    if (!productsFromSnapshot) throw new Error('snapshot items error')

    let CURRENCY = 0

    let settingsData = await loadSettings(['DISCOUNT_START_SUM', 'BIG_DISCOUNT_START_SUM', 'BIG_DISCOUNT_VALUE'])

    let defSum = productsFromSnapshot.map((item) => (item.orderCount || item.qty) * item.prod_price).reduce((acc, val) => acc + val, 0)
    const _whosalePrices = defSum > settingsData.DISCOUNT_START_SUM

    snapshot.whosalePrices = _whosalePrices

    if (this.order.order_locale != 'ru') {
      let setting = await loadSettings(['currency'])
      let currencies = setting.currency

      CURRENCY = currencies[this.order.order_locale == 'pl' ? (_whosalePrices ? 'zl' : 'r_zl') : _whosalePrices ? 'eur' : 'r_eur']
      await LangDict.prodsTranslate(productsFromSnapshot, 'en')

      //   productsFromSnapshot.forEach((item) => {
      //     try {
      //       item.prod_price = Number(item.prod_price / CURRENCY)
      //       item.whosaleprice ??= Number(item.whosaleprice / CURRENCY)
      //       item.prod_discount ??= Number(item.prod_discount / CURRENCY)
      //       console.log(item);
      //     } catch (error) {
      //       console.error('productsChangeCurrency: ' + item, error)
      //     }
      //   })

      snapshot.order_shippingprice = Number(Number(snapshot.order_shippingprice / CURRENCY).toFixed(2))
      snapshot.order_price = Number(Number(snapshot.order_price / CURRENCY).toFixed(2))
    }

    // if (typeof snapshot.whosalePrices === 'undefined') {
    // const ddd = await Order.getOrderNowPrice(snapshot.order_id)
    // console.log("🚀 ~ OrderDocument ~ init ~ ddd:", ddd)

    // const { whosalePrices: _whosalePrices, ...orderPriceMeta } = await Order.getOrderPrice(snapshot.order_id)

    // console.log("🚀 ~ OrderDocument ~ init ~ orderPriceMeta:", orderPriceMeta)
    // console.log("🚀 ~!!!! OrderDocument ~ init ~ snapshot.whosalePrices:", snapshot.whosalePrices)
    // }

    productsFromSnapshot.forEach((item, index) => {
      item['index'] = index + 1
      item.qty = item.orderCount || item.qty || item._qty

      item['price'] = snapshot.order_coupons?.discountVal > 0 || snapshot.whosalePrices ? item.whosaleprice || item.prod_discount : item.prod_price
      item['_price'] = item['price']
      item['sum'] = Number(item['price']) * Number(item.qty)
      item['dprice'] = this.getDprice({ price: Number(item['price']), discount: Number(snapshot.order_coupons.personal) })
      item['dsum'] = Number(this.getDprice({ price: Number(item['price']), discount: Number(snapshot.order_coupons.personal), isNumber: true })) * Number(item.qty)
      item['_sum'] = item['sum']
      item['title'] = `${item.prod_purpose} ${item.prod_size} ${item.prod_manuf} ${item.prod_sku} (${item.prod_analogsku})`

      if (this.order.order_locale != 'ru') {
        productItemTranslateFields.forEach((key) => {
          if (item[key]) {
            item[key] = Number(Number(item[key] / CURRENCY).toFixed(2))
          }
        })
      }
    })

    let productsSum = productsFromSnapshot.reduce((acc, current) => (acc += Number(current['_sum'])), 0)
    let discount = (productsSum / 100) * Number(snapshot.order_coupons.personal)
    let totalSum = productsSum + snapshot.order_shippingprice - discount

    let _org = snapshot.client.client_company || snapshot.client.org

    const _d = {
      order: this.order,
      client: snapshot.client,
      org: _org,
      vat: _org?.org_vat || '',
      receiver: this.makeReceiverFromSnapshot(snapshot.client),
      enAddress: this.makeEnAddressFromSnapshot(snapshot.client),
      enName: this.makeEnNameFromSnapshot(snapshot.client),
      products: productsFromSnapshot,
      discount: new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(Number(discount)), //Number(discount).toLocaleString(), //.toFixed(2).replace('.', ','),
      date: this.order.order_datetime.setLocale('ru').toFormat('d LLLL y'),
      dateNum: this.order.order_datetime.setLocale('en').toLocaleString(),
      dateFull: {
        month: this.order.order_datetime.setLocale('ru').month,
        monthText: this.order.order_datetime.setLocale('ru').toFormat('MMMM'), //.monthLong,
        year: this.order.order_datetime.setLocale('ru').year,
        day: this.order.order_datetime.setLocale('ru').day
      },
      dateLocale: this.order.order_datetime.setLocale('ru').toFormat('dd.LL.yyyy'),
      itemsLength: productsFromSnapshot.length + 2,
      indexShipping: productsFromSnapshot.length + 2,
      indexDiscount: productsFromSnapshot.length + 1,
      productsSum: new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(Number(productsSum)),
      currency: this.order.order_locale == 'pl' ? 'PLN' : 'EUR',
      sum: (Number(totalSum) + Number(discount)).toLocaleString(),
      totalSum: Number(totalSum).toLocaleString(),
      totalSumSplit: new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(Number(totalSum)), //Number(totalSum).toLocaleString(), //splitSum(totalSum),
      shippingprice: new Intl.NumberFormat('ru-RU', { style: 'decimal', minimumFractionDigits: 2 }).format(Number(snapshot.order_shippingprice)), //Number(snapshot.order_shippingprice).toLocaleString(), //.toFixed(2).replace('.', ','),
      sumText: numToText(totalSum),
      totalQty: productsFromSnapshot.reduce((acc, current) => (acc += current.qty || 0), 0) + 2,
      lengthToText: String(numToText(productsFromSnapshot.length + 2))
        .replace(/рубл[а-яА-Я]{0,2}/gm, '')
        .replace(/копе[а-яА-Я]{0,3}/gm, '')
        .replace(/\d*/gm, ''),
      pcs: this.order.order_locale == 'ru' ? 'шт.' : 'pcs.',
      whosalePrices: snapshot.order_coupons?.discountVal > 0 || snapshot.whosalePrices
    }

    // if (this.order.order_locale != 'ru') {
    //   orderTranslateFields.map((key) => {
    //     if (this.document.data?.[key]) {
    //       this.document.data[key] = Number((this.document.data[key] / CURRENCY).toFixed(2))
    //     }
    //   })
    // }

    this.document.data = _d

    return _d
    // return this.document
  }
}
