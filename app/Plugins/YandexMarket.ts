import { $prisma } from '../Services/Prisma'
import { DateTime } from 'luxon'

let PAGE_SIZE = 5000 // Товаров на страницу
const SHOP_URL = 'https://mirsalnikov.ru'

export async function generateYMLFeed({ page = 1, limit = 5000 }) {
  if (limit) {
    PAGE_SIZE = limit
  }

  const catsIds = ['19', '16', '38', '11', '20']
  // Получаем активные категории
  const activeCategories = await $prisma.cats.findMany({
    where: {
      cat_active: true,
      cat_id: {
        in: catsIds.map((i) => Number(i))
      }
    },
    select: { cat_id: true, cat_title: true }
  })

  // Пагинация товаров
  const [products, total] = await Promise.all([
    $prisma.products.findMany({
      where: {
        prod_cat: { in: activeCategories.map((c) => c.cat_id.toString()) },
        prod_count: {
          gt: 0
        }
      },
      select: {
        prod_id: true,
        prod_sku: true,
        prod_price: true,
        prod_count: true,
        prod_cat: true,
        prod_model: true,
        prod_manuf: true,
        prod_weight: true,
        prod_img: true,
        prod_purpose: true,
        prod_uses: true,
        prod_analogsku: true,
        prod_analogs: true,
        prod_size: true
      },
      take: PAGE_SIZE,
      skip: (page - 1) * PAGE_SIZE,
      orderBy: { prod_id: 'desc' }
    }),
    $prisma.products.count({
      where: {
        prod_cat: { in: activeCategories.map((c) => c.cat_id.toString()) },
        prod_count: {
          gt: 0
        }
      }
    })
  ])

  // Генерируем XML только для текущей страницы
  return `<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE yml_catalog SYSTEM "shops.dtd">
<yml_catalog date="${DateTime.now().toISO()}">
  <shop>
    <name>Мир Сальников</name>
    <company>ООО «РТИ-Балтика»</company>
    <url>${SHOP_URL}</url>
    
    <currencies>
      <currency id="RUR" rate="1"/>
    </currencies>

    <categories>
      ${activeCategories
        .map(
          (cat) => `
        <category id="${cat.cat_id}">${cat.cat_title}</category>
      `
        )
        .join('')}
    </categories>

    <offers>
      ${products
        .map((product) => {
          // Валидация обязательных полей
          if (!product.prod_price || Number(product.prod_price) <= 0) return null
          if (!product.prod_manuf || !product.prod_model) return null

          // Обработка названия товара
          const name = `${product.prod_purpose} ${product.prod_sku} ${product.prod_uses}`
            .replace(/[<>&"']/g, '') // Удаляем запрещенные символы
            .substring(0, 250) // Лимит длины названия

          // Формирование описания
          const rawDescription = `${product.prod_purpose} ${product.prod_uses} - ${product.prod_sku} (${product.prod_analogsku}, ${product.prod_analogs}), ${product.prod_manuf}, ${product.prod_size}`
          const description = rawDescription
            .substring(0, 3000) // Обрезаем до 3000 символов
            .replace(/[<>&]/g, '') // Экранируем спецсимволы

          // Форматирование цены
          const price = Number(product.prod_price).toFixed(2)

          return `
            <offer id="${product.prod_id}">
              <price>${price}</price>
              <currencyId>RUR</currencyId>
              <categoryId>${product.prod_cat}</categoryId>
              ${
                product.prod_img
                  ? `
              <picture>https://mirsalnikov.ru/data/rti/${product.prod_img}.jpg</picture>
              `
                  : ''
              }
              <name>${name}</name>
              <vendor>${product.prod_manuf.substring(0, 50)}</vendor>
              <barcode></barcode>
              <description><![CDATA[${description}]]></description>
              <count>${product.prod_count}</count>
              ${product.prod_size ? `<dimensions>${product.prod_size.substring(0, 50)}</dimensions>` : ''}
              ${product.prod_weight ? `<weight>${Number(product.prod_weight).toFixed(2)}</weight>` : ''}
              <url>${SHOP_URL}/catalog/product/${product.prod_id}</url>
              <delivery>true</delivery>
              <pickup>true</pickup>
              <vat>VAT_20</vat>
              
              <delivery-options>
               <option cost="300" days="3-5"/>
               <option cost="800" days="5-7"/>
             </delivery-options>

              <pickup-options>
                <option cost="0" days="1"/>
              </pickup-options>

            </offer>
          `
        })
        .filter(Boolean) // Фильтруем невалидные товары
        .join('')}
    </offers>
  </shop>
</yml_catalog>`
}

// Новая функция для проверки лимитов
export function getFeedMeta() {
  return {
    pageSize: PAGE_SIZE,
    shopUrl: SHOP_URL,
    maxFileSize: 15 * 1024 * 1024
  }
}

// Новая функция для генерации всех файлов
export async function generateAllFeeds() {
  let currentPage = 1
  let hasMore = true

  while (hasMore) {
    const { xml, totalPages } = await generateYMLFeed(currentPage)

    // Сохраняем в файл
    await saveToFile(xml, `yandex-market-feed-${currentPage}.xml`)

    // Проверяем размер файла (примерная реализация)
    if ((await getFileSize(`yandex-market-feed-${currentPage}.xml`)) > 15 * 1024 * 1024) {
      throw new Error('Размер файла превышает 15 МБ')
    }

    hasMore = currentPage < totalPages
    currentPage++
  }
}

// Вспомогательные функции
async function saveToFile(content: string, filename: string) {
  // Реализация сохранения в файл
}

async function getFileSize(filename: string) {
  // Реализация проверки размера файла
}
