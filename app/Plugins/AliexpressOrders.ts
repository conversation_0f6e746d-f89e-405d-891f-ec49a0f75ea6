import loadSettings from "App/Helpers/loadSettings"
import { randomInteger } from "App/Helpers/randomInteger"
import { rtiOrder } from "App/Interfaces/orders/RtiOrder";
import Order from "App/Models/Order";
import got from 'got'


    export interface PropertiesMap {
        'Название бренда': string;
        'Единица измерения': string;
        'Способ продажи (штука/упаковка)': string;
        'Каждая упаковка: string';
        'Вес логистики': string;
        'Размер логистики - длина (см)': string;
        'Размер логистики - высота (см)': string;
        'Размер логистики - ширина (см)': string;
    }

    export interface AliOrderItem {
        id: number;
        item_id: string;
        sku_id: string;
        sku_code: string;
        name: string;
        img_url: string;
        item_price: number;
        quantity: number;
        total_amount: number;
        properties: string[];
        properties_map: PropertiesMap;
        buyer_comment?: any;
        height: number;
        weight: number;
        width: number;
        length: number;
        issue_status: string;
        promotions: any[];
        order_line_fees?: any;
    }



interface AliOrder {
    id: number;
    created_at: Date;
    paid_at: Date;
    updated_at: Date;
    status: string;
    payment_status: string;
    delivery_status: string;
    delivery_address: string;
    antifraud_status: string;
    buyer_country_code: string;
    buyer_name: string;
    order_display_status: string;
    buyer_phone: string;
    order_lines: AliOrderItem[];
    total_amount: number;
    seller_comment?: any;
    fully_prepared: boolean;
    finish_reason?: any;
    cut_off_date?: any;
    cut_off_date_histories?: any;
    shipping_deadline?: any;
    next_cut_off_date?: any;
    pre_split_postings?: any;
    logistic_orders?: any;
    commission?: any;
}

export interface AliOrders {
    total_count: number,
    orders: Array<AliOrder>
}

interface toRTIorder {
    orderData: rtiOrder
    orderItems?: [{id: number, qty: number}]
}

export interface getOrdersPayload {
    date_start?: string
    date_end?: string
    order_statuses?: AliOrderStatuses[]
    payment_statuses?: AliOrderPaymentStatuses[]
    delivery_statuses?: AliOrderDeliveryStatuses[]
    antifraud_statuses?: string[]
    order_ids?: number[]
    sorting_order?: 'ASC' | 'DESC' | 'NONE'
    sorting_field?: string
    tracking_numbers?: string[]
    page_size?: number
    page?: number
    update_at_from?: string //YYYY-MM-DDThh:mm:ssZ
    updated_at_to?: string
    shipping_day_from?: string
    shipping_day_to?: string
    trade_order_info?: 'Common' | 'LogisticInfo'
}

type AliOrderDeliveryStatuses = 'Init' | 'PartialShipped' | 'Shipped' | 'Delivered' | 'Cancelled'
type AliOrderPaymentStatuses = 'NotPaid' | 'Hold' | 'Paid' | 'Cancelled' | 'Failed'
type AliOrderStatuses = 'Created' | 'InProgress' | 'Finished' |  'Cancelled'

export const aliOrderPrefix = 'Aliexpress:'

export class AliexpressOrders {
    Token: string 
    ap: string

    constructor() {
        this.ap = 'https://openapi.aliexpress.ru'
    }

    async init() {
        const { AliApiToken } = await loadSettings(['AliApiToken'])
        this.Token = AliApiToken
    }

    async sendTrack({orderId, tracking_number}) {

        const gotPayload = {
            headers: {
                'x-auth-token': this.Token,
                'accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                trade_order_id: orderId,
                tracking_number,
                provider_name: 'ПОЧТА РФ',
                tracking_url: 'https://www.pochta.ru/tracking#'+orderId
            }),
        }

        const transitRes = await got.post(this.ap + '/api/v1/offline-ship/to-in-transit', gotPayload).json()

        const pickupRes = await got.post(this.ap + '/api/v1/offline-ship/to-ready-for-pickup', {
                headers: {
                    'x-auth-token': this.Token,
                    'accept': 'application/json',
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    trade_order_id: orderId
                })
            }).json()

            
        return {transitRes, pickupRes}
    }

    async getOrders({toRTI = false, payload = {}}: {toRTI: boolean, payload?: getOrdersPayload}) {
        if (!this.Token) throw new Error('Token undefined, run .init() previously') 


        function translitName (name: string) {
            return name
        }

        const toRtiOrders: toRTIorder[] = []

        const { data }: {data: AliOrders} = await got.post(this.ap + '/seller-api/v1/order/get-order-list', {
            headers: {
                'x-auth-token': this.Token,
                'accept': 'application/json',
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                page_size: 50,
                order_statuses: ['Created', 'InProgress'],
                payment_statuses: ['Hold', 'Paid'],
                delivery_statuses: ['Init'],
                ...payload
            })
        }).json()


        const existOrders = await Order.query()
                                            .select('order_desc')
                                            .whereRaw('order_desc REGEXP ' + '?', [data.orders.map(o => aliOrderPrefix+o.id).join('|')])
        
                                            const existOrdersIds: string[] = existOrders.map(eorder => String(eorder.order_desc).replace(aliOrderPrefix, ''))        
        const newOrders = data.orders.filter(i => !existOrdersIds.includes(String(i.id)))

        newOrders.map((order) => {
            let [_country, _area, _location, _street, _house, _flat, _index ] = order.delivery_address.split(',')

            toRtiOrders.push({
                orderData: {
                    client: {
                        email: randomInteger(50000, 99000) + '@fakemail.aa',
                        name:  translitName(order.buyer_name), //order.buyer_name,
                        phone: order.buyer_phone
                    },
                    address: {
                        country: _country,
                        location: _area,
                        house: _house,
                        street: _street,
                        flat: _flat,
                        index: _index
                    },
                    notice: aliOrderPrefix + order.id,
                    payment: { method: 'Почта РФ' },
                    shipping: {
                        method: 'Почта РФ',
                        price: 250
                    },
                },
                orderItems: order.order_lines.map(item => ({ id: item.sku_code, qty: item.quantity })),
                sysnotice: order.delivery_address
            })
        })

        if (toRTI) {
            return toRtiOrders
        }

        return data
    }
}