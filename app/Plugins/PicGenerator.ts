import fs from 'fs'
import im from 'imagemagick'

type TextItem = {
  text: string
  fontSize?: number
}

type GenerateParams = {
  texts: TextItem[]
  outputFilename: string
  backgroundColor?: string
  backgroundImage?: string
  textColor?: string
  width?: number
  height?: number
  gravity?: 'NorthWest' | 'North' | 'NorthEast' | 'West' | 'Center' | 'East' | 'SouthWest' | 'South' | 'SouthEast'
  fontSize?: number
  logoPath?: string
  logoPosition?: 'top' | 'left' | 'bottom' | 'right'
  frameColor?: string
  frameWidth?: number
  framePadding?: number
  footer?: {
    footerText: string
    footerTextSize: string
  }
  mergeWithImage?:
    | {
        path: string
        orientation: 'vertical' | 'horizontal'
      }
    | undefined

  bodyPadding?: number
}

export async function checkFileExists(filePath: string): Promise<boolean> {
  try {
    await fs.promises.access(filePath, fs.constants.F_OK)
    return true
  } catch (err) {
    return false
  }
}

export function getImageDimensions(filePath: string): Promise<{ width: number; height: number }> {
  return new Promise((resolve, reject) => {
    im.identify(['-format', '%wx%h', filePath], (err, output) => {
      if (err) {
        reject(err)
      } else {
        const [width, height] = output.trim().split('x')
        resolve({ width: parseInt(width), height: parseInt(height) })
      }
    })
  })
}

export default async function generateImageWithTexts({
  texts,
  outputFilename,
  backgroundColor = 'transparent',
  backgroundImage,
  textColor = '#111827',
  width = 500,
  height = 300,
  gravity = 'North',
  fontSize = 38,
  logoPath,
  logoPosition = 'top',
  frameColor = 'black',
  frameWidth = 0,
  framePadding = 20,
  footer = undefined,
  bodyPadding = 20,
  mergeWithImage = undefined
}: GenerateParams) {
  const command = [
    //prettier
    '-size',
    `${width}x${height}`,
    `xc:${backgroundColor}`,
    '-gravity',
    gravity,
    '-fill',
    textColor
  ]

  if (backgroundImage) {
    const isExist = await checkFileExists(backgroundImage)
    if (isExist) {
      command.push(backgroundImage)
      command.push('-composite')
    }
  }

  texts.forEach((item, index) => {
    const yOffset = (index + 1) * ((item.fontSize || fontSize) + 15)
    command.push('-pointsize', String(item.fontSize || fontSize))
    command.push('-weight', 'bold')
    command.push('-annotate', `+0+${yOffset}`, item.text)
  })

  if (logoPath) {
    const logoDimensions = await getImageDimensions(logoPath)

    let logoX = framePadding
    let logoY = framePadding

    switch (logoPosition) {
      case 'top':
        logoY = framePadding
        break
      case 'left':
        logoX = framePadding
        break
      case 'bottom':
        logoY = height - framePadding - logoDimensions.height
        break
      case 'right':
        logoX = width - framePadding - logoDimensions.width
        break
      default:
        logoY = framePadding
        break
    }

    command.push(logoPath)
    command.push('-geometry', `+${logoX}+${logoY}`)
    command.push('-composite')
  }

  if (mergeWithImage && mergeWithImage?.path) {
    const isExist = await checkFileExists(mergeWithImage?.path)

    if (isExist) {
      command.push(mergeWithImage?.path)
      command.push('-resize', `${width}x${height}`)
      command.push('-background', 'white')
      command.push('-gravity', 'Center')
      command.push('-extent', `${width}x${height}`)
      command.push((mergeWithImage?.orientation == 'horizontal' ? '+' : '-') + 'append')
    } else {
      console.error('Could not find', mergeWithImage)
    }
  }

  if (frameWidth) {
    command.push('-bordercolor', backgroundColor)
    command.push('-border', `${frameWidth + bodyPadding}x${frameWidth + bodyPadding}`)

    command.push('-bordercolor', frameColor)
    command.push('-border', `${frameWidth}x${frameWidth}`)

    command.push('-bordercolor', backgroundColor || 'white')
    command.push('-border', `${framePadding}x${framePadding}`)
  }

  if (footer) {
    command.push('-stroke', 'black')
    command.push('-fill', 'white')
    command.push('-strokewidth', '2')
    command.push(
      '-draw',
      `rectangle ${framePadding + bodyPadding + 60},${height - framePadding - 50} ${width - (framePadding - bodyPadding) / 2},${height - framePadding + bodyPadding + 40}`
    )
    command.push('-stroke', 'transparent')
    command.push('-fill', 'black')
    command.push('-pointsize', footer.footerTextSize)
    command.push('-gravity', 'Center')
    command.push('-annotate', `+0+${height - framePadding - 280}`, footer.footerText)
  }

  command.push(outputFilename)

  // console.log('command.toString()', command.toString())

  im.convert(command, (err) => {
    if (err) {
      console.error(err)
    } else {
      // console.log('Изображение успешно создано!')
    }
  })
}

// const texts: TextItem[] = [
//   { text: 'Резиновое кольцо', fontSize: 36 },
//   { text: 'Арт. E233', fontSize: 36 },
//   { text: 'Размер: 35*52*7', fontSize: 36 },
//   { text: 'Тип: TC', fontSize: 36 }
// ]

// const outputFilename = 'output.jpg'
// const backgroundImage = './line_pattern.png'
// const logoPath = './rmbg_o-rings.png'

// generateImageWithTexts({
//   frameWidth: 5,
//   framePadding: 30,
//   frameColor: '#27272a',
//   texts,
//   gravity: 'North',
//   outputFilename,
//   textColor: '#27272a',
//   backgroundColor: 'white',
//   height: 450,
//   width: 520,
//   footer: {
//     footerText: 'Интернет-магазин\nМИР САЛЬНИКОВ',
//     footerTextSize: '32'
//   },
//   mergeWithImage: {
//     orientation: 'horizontal',
//     path: './E233.jpg'
//   }
//   // logoPath,
//   // logoPosition: 'left'
// })
