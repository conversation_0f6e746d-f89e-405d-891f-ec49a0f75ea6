import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'
import type { Snapshot } from 'App/Interfaces/orders/Snapshot'
import type { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import Order from 'App/Models/Order'
import OrderSnapshot from 'App/Models/OrderSnapshot'
import axios from 'axios'
// import Env from '@ioc:Adonis/Core/Env';
const EMAIL_FROM = Env.get('EMAIL_FROM')

interface YookassaPaymentAmount {
  value: string
  currency: string
}

interface YookassaPaymentItem {
  description: string
  quantity: string
  amount: YookassaPaymentAmount
  vat_code: string
  payment_mode: string
  payment_subject: string
}

interface YookassaPaymentRequest {
  amount: YookassaPaymentAmount
  confirmation: {
    type: string
  }
  capture: boolean
  description: string
  receipt?: {
    customer: {
      email: string
      full_name: string
    }
    items: YookassaPaymentItem[]
    tax_system_code: string
  }
}

interface YookassaPaymentResponse {
  id: string
  status: string
  amount: YookassaPaymentAmount
  description: string
  recipient: {
    account_id: string
    gateway_id: string
  }
  created_at: string
  confirmation: {
    type: string
    confirmation_token: string
  }
  test: boolean
  paid: boolean
  refundable: boolean
  metadata: Record<string, any>
}

interface YookassaConfig {
  shopId: string
  secretKey: string
}

interface CreatePaymentParams {
  amount: {
    value: string
    currency?: string // делаем необязательным
  }
  description: string
  orderId: string
  customerEmail: string
  customerName: string
}

export class Yookassa {
  private readonly apiUrl = 'https://api.yookassa.ru/v3'
  private readonly shopId: string
  private readonly secretKey: string

  constructor(config?: YookassaConfig) {
    this.shopId = config?.shopId ?? Env.get('YOOKASSA_SHOP_ID')
    this.secretKey = config?.secretKey ?? Env.get('YOOKASSA_SECRET_KEY')
  }

  private getDateHourString(): string {
    const now = new Date()
    return `${now.getDate()}-${now.getHours()}`
  }

  async afterSuccess({ orderId }) {
    console.log('Yookassa payment successful!: ', orderId)

    if (orderId) {
      const order = await Order.find(orderId)
      if (order) {

        if (order.order_status !== 'Не обработан') {
          console.log('Yookassa plugin: Order status is not "Не обработан", skipping...')
          return 'ok'
        }

        order.order_status = 'Автооплата'
        await order.save()

        const orderLastSnapshot = await Order.lastSnapshot(order.order_id)
        const orderState: SnapshotBodyInterface = orderLastSnapshot?.body

        orderState.order_status = 'Автооплата'

        const newSnapshot: Snapshot = {
          orderid: orderState.order_id,
          body: JSON.stringify(orderState),
          user: 'yookassa'
        }

        await OrderSnapshot.create(newSnapshot)

        await Mail.use('smtp').send((message) => {
          message
            .from(Env.get('EMAIL_FROM'))
            .to(Env.get('EMAIL_FROM'))
            .subject(`Вы получили оплату по счету № ${orderState.order_id}`)
            .html(`Заказ #${orderState.order_id} оплачен через форму Yookassa на сайте. Установлен статус 'Автооплата'`)
        })
      }
    }

    // отправляем письмо с уведомлением об оплате

    return 'ok'
  }

  formatAmount(value: number): string {
    return value.toFixed(2)
  }

  async createPayment({ amount, description, orderId, customerEmail, customerName }: CreatePaymentParams): Promise<YookassaPaymentResponse> {
    const idempotenceKey = `payment_${orderId}_${this.getDateHourString()}`
    const auth = Buffer.from(`${this.shopId}:${this.secretKey}`).toString('base64')

    const paymentData: YookassaPaymentRequest = {
      amount: {
        value: amount.value,
        currency: amount.currency || 'RUB'
      },
      confirmation: {
        type: 'embedded'
      },
      capture: true,
      description,
      receipt: {
        customer: {
          email: customerEmail,
          full_name: customerName
        },
        items: [
          {
            description: 'Автозапчасти',
            quantity: '1',
            amount: {
              value: amount.value,
              currency: amount.currency || 'RUB'
            },
            vat_code: '1',
            payment_mode: 'full_payment',
            payment_subject: 'commodity'
          }
        ],
        tax_system_code: '6'
      }
    }

    try {
      const response = await axios.post<YookassaPaymentResponse>(`${this.apiUrl}/payments`, paymentData, {
        headers: {
          'Authorization': `Basic ${auth}`,
          'Idempotence-Key': idempotenceKey,
          'Content-Type': 'application/json'
        }
      })

      return response.data
    } catch (error) {
      throw new Error(`Failed to create payment: ${error.message}`)
    }
  }
}
