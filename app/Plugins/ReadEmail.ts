// const { ImapFlow } = require('imapflow')

import { ImapFlow } from 'imapflow'
import { simplePars<PERSON>, MailParser } from 'mailparser'

const client = new ImapFlow({
  host: 'imap.yandex.ru',
  port: 993,
  secure: true,
  auth: {
    user: '<EMAIL>',
    pass: 'kniesbdbtjawsrls'
  }
})

const main = async () => {
  // Wait until client connects and authorizes
  await client.connect()
  const idle = await client.idle()

  // Select and lock a mailbox. Throws if mailbox does not exist
  let lock = await client.getMailboxLock('INBOX')

  // try {
  //   // fetch latest message source
  //   // client.mailbox includes information about currently selected mailbox
  //   // "exists" value is also the largest sequence number available in the mailbox
  //   let message = await client.fetchOne(client.mailbox.exists, { source: true })
  //   console.log(message.source.toString())

  //   // list subjects for all messages
  //   // uid value is always included in FETCH response, envelope strings are in unicode.
  //   for await (let message of client.fetch('1:*', { envelope: true })) {
  //     console.log(`${message.uid}: ${message.envelope.subject}`)
  //   }
  // } finally {
  //   // Make sure lock is released, otherwise next `getMailboxLock()` never returns
  //   lock.release()
  // }

  const messages: any[] = []

  const searchResults = await client.search({
    from: '<EMAIL>'
  })

  if (searchResults.length) {
    // let mailbox = await client.mailboxOpen('INBOX')
    let message = await client.fetchOne(searchResults[0], { uid: true, bodyParts: ['TEXT'], source: true, envelope: true })
    // console.log('messaga: ', message.source.toString())
    console.log({
      date: message.envelope.date,
      subject: message.envelope.subject,
      // content: message.bodyParts.get('text')?.toString('utf8')
      content: message.bodyParts.get('text')?.toString(),
      contentParsed: Buffer.from(message.bodyParts.get('text')?.toString(), 'base64').toString()
    })
  }
  // console.log("🚀 ~ file: ReadEmail.ts:45 ~ main ~ searchResults:", searchResults)

  // for (let uid of searchResults) {
  //   const message = await client.fetchOne(uid, {
  //     source: true,
  //     envelope: true
  //   })

  //   messages.push({
  //     date: message.envelope.date,
  //     subject: message.envelope.subject,
  //     content: message.source.toString()
  //   })
  // }

  // client.close()

  // log out and close connection
  await client.logout()
}

// main().catch((err) => console.error(err))

const emailBody = `'\r\n' +
    '----ALT--6aFd76Bc4d9FA6d9eBb6906b992C25481637149058\r\n' +
    'Content-Type: text/plain; charset=utf-8\r\n' +
    'Content-Transfer-Encoding: base64\r\n' +
    '\r\n' +
    'CtCX0LTRgNCw0LLRgdGC0LLRg9C50YLQtSEg0J/RgNC+0YjRgyDQktCw0YEg0L7RgtC/0YDQsNCy\r\n' +
    '0LjRgtGMINC/0YDQsNC50YEg0LvQuNGB0YIK0JzRiyDRgtC+0YDQs9GD0Y7RidCw0Y8g0L7RgNCz\r\n' +
    '0LDQvdC40LfQsNGG0LjRjyDQt9Cw0L/Rh9Cw0YHRgtGP0LzQuCDQuNC3INCzLiDQndC+0LLQvtGB\r\n' +
    '0LjQsdC40YDRgdC60LAK0KEg0YPQstCw0LbQtdC90LjQtdC8INCh0YLQtdC/0LDQvdC+0LIg0JjQ\r\n' +
    's9C+0YDRjCAgOC05MjMtNjA5LTA4LTU4IMKgCsKgCsKg\r\n' +
    '\r\n' +
    '----ALT--6aFd76Bc4d9FA6d9eBb6906b992C25481637149058\r\n' +
    'Content-Type: text/html; charset=utf-8\r\n' +
    'Content-Transfer-Encoding: base64\r\n' +
    '\r\n' +
    'CjxIVE1MPjxCT0RZPjxkaXY+0JfQtNGA0LDQstGB0YLQstGD0LnRgtC1ISDQn9GA0L7RiNGDINCS\r\n' +
    '0LDRgSDQvtGC0L/RgNCw0LLQuNGC0Ywg0L/RgNCw0LnRgSDQu9C40YHRgjxkaXYgY2xhc3M9Impz\r\n' +
    'LWhlbHBlciBqcy1yZWFkbXNnLW1zZyI+PGRpdj48ZGl2IGlkPSJzdHlsZV8xNjM3MTQ4MzkxMTU0\r\n' +
    'OTc4MzA2N19CT0RZIj48ZGl2IGNsYXNzPSJjbF8xNjIwMjMiPjxkaXY+PGRpdj7QnNGLINGC0L7R\r\n' +
    'gNCz0YPRjtGJ0LDRjyDQvtGA0LPQsNC90LjQt9Cw0YbQuNGPINC30LDQv9GH0LDRgdGC0Y/QvNC4\r\n' +
    'INC40Lcg0LMuINCd0L7QstC+0YHQuNCx0LjRgNGB0LrQsDwvZGl2PjxkaXY+0KEg0YPQstCw0LbQ\r\n' +
    'tdC90LjQtdC8INCh0YLQtdC/0LDQvdC+0LIg0JjQs9C+0YDRjCA8c3BhbiBjbGFzcz0ianMtcGhv\r\n' +
    'bmUtbnVtYmVyX21yX2Nzc19hdHRyIj48c3BhbiBjbGFzcz0ianMtcGhvbmUtbnVtYmVyIj44LTky\r\n' +
    'My02MDktMDgtNTg8L3NwYW4+PC9zcGFuPjwvZGl2PjwvZGl2PjwvZGl2PjwvZGl2PjwvZGl2Pjwv\r\n' +
    'ZGl2PiZuYnNwOzxkaXYgZGF0YS1zaWduYXR1cmUtd2lkZ2V0PSJjb250YWluZXIiPjxkaXYgZGF0\r\n' +
    'YS1zaWduYXR1cmUtd2lkZ2V0PSJjb250ZW50Ij48ZGl2PiZuYnNwOzwvZGl2PjwvZGl2PjwvZGl2\r\n' +
    'PjxkaXY+Jm5ic3A7PC9kaXY+PC9kaXY+PC9CT0RZPjwvSFRNTD4K\r\n' +
    '\r\n' +
    '----ALT--6aFd76Bc4d9FA6d9eBb6906b992C25481637149058--\r\n'`

// const m = Buffer.from(bs64, 'utf8').toString('utf8')
// console.log("🚀 ~ file: ReadEmail.ts:112 ~ m:", m)

// Пример использованиbs64y

async function ssname() {
const parts = emailBody.split(/^\-{5}.+?\-{5}$/gm)

const result = parts.map((part) => {
  // Разделяем заголовки и тело
  const [headers, body] = part.split('\r\n\r\n')

  // Парсим заголовки
  const headersObj = headers.split('\r\n').reduce((acc, line) => {
    const [key, value] = line.split(': ')
    acc[key] = value
    return acc
  }, {})

  // Декодируем тело из base64 если нужно
  let content = body
  if (headersObj['Content-Transfer-Encoding'] === 'base64') {
    content = Buffer.from(content, 'base64').toString()
  }

  const res = {
    headers: headersObj,
    content
  }

  console.log('res:', res);
  
})
}

ssname()
