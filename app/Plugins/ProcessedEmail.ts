import { $prisma } from 'App/Services/Prisma'

/**
 * Простой сервис для работы с обработанными письмами
 */
export default class ProcessedEmailService {

  /**
   * Проверка, было ли письмо уже обработано
   */
  public static async isProcessed(messageId: string): Promise<boolean> {
    const existing = await $prisma.processed_emails.findUnique({
      where: { message_id: messageId }
    })
    return !!existing
  }

  /**
   * Отметка письма как обработанного
   */
  public static async markAsProcessed(messageId: string, fromEmail: string, subject: string, sentToMastra: boolean = false) {
    return await $prisma.processed_emails.create({
      data: {
        message_id: messageId,
        from_email: fromEmail,
        subject: subject,
        sent_to_mastra: sentToMastra
      }
    })
  }

  /**
   * Получение статистики
   */
  public static async getStats() {
    const total = await $prisma.processed_emails.count()
    const sentToMastra = await $prisma.processed_emails.count({
      where: { sent_to_mastra: true }
    })

    return {
      total,
      sentToMastra,
      ignored: total - sentToMastra
    }
  }

  /**
   * Получение последних обработанных писем
   */
  public static async getRecent(limit = 50) {
    return await $prisma.processed_emails.findMany({
      orderBy: { processed_at: 'desc' },
      take: limit
    })
  }
}
