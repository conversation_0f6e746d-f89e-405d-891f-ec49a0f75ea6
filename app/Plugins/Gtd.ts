import Database from '@ioc:Adonis/Lucid/Database'
import { GtdSpecData } from 'App/Interfaces/orders/GtdSpecData'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import Order from 'App/Models/Order'
import Product from 'App/Models/Product'

interface GtdProduct {
  prod?: string
  sku?: string
  orderId: number
  prodId: number
  gtdQty: number
  qty?: number
  sumqty?: number
}

interface SpecListItem {
  spec_id: number
  orderId: number
  prod: string
  qty: number
  increment?: boolean
  transit?: boolean
}

interface AnalyzeItem {
  specId: number
  specQty: number
  prod: string
  gtdQty: number
  orderId: number
  prodId: number
  cnt?: number
}

interface IncSpecsListItem {
  specId: number
  prod: string
  gtdQty: number
  orderId: number
}

class Gtd {
  private analyze: AnalyzeItem[] = []
  private incSpecsList: IncSpecsListItem[] = []

  constructor(product?: GtdProduct) {
    // this.product = product
  }

  async checkFull(orderId: number) {
    const order = await Order.query()
      .where('order_id', orderId)
      .preload('items', (q) => q.preload('product'))
      .firstOrFail()

    // Группируем товары и суммируем количества
    const uniqueProducts = order.items.reduce((acc, item) => {
      const sku = item.product?.prod_analogsku
      if (!acc[sku]) {
        acc[sku] = { ...item, item_count: 0 }
      }
      acc[sku].item_count += item.item_count
      return acc
    }, {})

    const orderItems = Object.values(uniqueProducts).filter((i) => i)
    const pNums = orderItems.map((item) => item.product?.prod_analogsku).filter((i) => i)

    if (!pNums.length) {
      return undefined
    }


    // Получаем все активные спецификации с остатками
    const specList = await Database.from('spec_list')
      .leftJoin('specs', 'specs.id', 'spec_list.spec')
      .whereIn('spec_list.prod', pNums) // Указываем полное имя таблицы
      .andWhere('specs.active', 1)
      .orderBy('qty', 'desc')

    // Проверяем каждый товар
    const results = await Promise.all(
      orderItems.map(async (item) => {
        const productSpecs = specList.filter((spec) => spec.prod === item.product?.prod_analogsku)

        let remainingQty = item.item_count
        let availableQty = 0

        // Считаем доступное количество по всем спецификациям
        for (const spec of productSpecs) {
          if (remainingQty <= 0) break

          const qtyFromSpec = Math.min(spec.qty, remainingQty)
          availableQty += qtyFromSpec
          remainingQty -= qtyFromSpec
        }

        return availableQty >= item.item_count
      })
    )

    return results.every((result) => result)
  }

  async broken_checkFull(orderId: number) {
    interface SpecItem {
      id: number
      spec: number
      prod: string
      qty: number
      invoice: string
      np: string
      spec_num: number
      spec_title: string
      spec_date: Date
      active: number
    }

    const order = await Order.query()
      .where('order_id', orderId)
      .preload('items', (q) => q.preload('product'))
      .firstOrFail()

    // order.items[0].product?.prod_analogsku
    // order.items[0].item_count

    // Create a map to store unique products and their total counts
    const uniqueProducts = order.items.reduce((acc, item) => {
      const sku = item.product?.prod_analogsku
      if (!acc[sku]) {
        acc[sku] = { ...item, item_count: 0 }
      }
      acc[sku].item_count += item.item_count
      return acc
    }, {})

    // Convert back to array
    const orderItems = Object.values(uniqueProducts).filter((i) => i)

    const pNums = orderItems.map((item) => item.product?.prod_analogsku)

    const specList = await Database.from('spec_list').leftOuterJoin('specs', 'specs.id', 'spec_list.spec').whereIn('prod', pNums).andWhere('specs.active', 1)

    const spProd = specList.reduce((acc, item) => {
      if (!acc[item.prod]) {
        acc[item.prod] = item
      } else {
        acc[item.prod].qty += item.qty
      }
      return acc
    }, {})

    // console.log('🚀 ~ Gtd ~ spProd ~ spProd:', spProd)

    return orderItems.every((item) => {
      // console.log("🚀 ~ Gtd ~ returnorderItems.every ~ spProd[item.prod_analogsku]:", spProd[item.product.pod_analogsku])
      return spProd[item.product?.prod_analogsku]?.qty >= item.item_count
    })
  }

  groupBy<T extends { [key: string]: any }, K extends keyof T>(items: T[], key: K): { [key: string]: T[] } {
    return items.reduce(
      (result, item) => ({
        ...result,
        [item[key]]: [...(result[item[key]] || []), item]
      }),
      {}
    )
  }

  async gtd_reserveRkWrapper(item, action, order) {
    let __list = []

    const _process = async (rk) => {
      rk = rk.split('*')
      let prodID = rk[0]

      !item.rk_qty ? (item.rk_qty = item.orderCount) : ''
      let countInRk = item.rk_qty * rk[1]

      // single product @@ sample { sku: 'HM001', gtdQty: 10, orderId: 100 }
      try {
        let product = await Database.from('products').where('prod_id', prodID).firstOrFail()

        if (!!action) {
          __list.push({ sku: product?.prod_analogsku, gtdQty: countInRk, orderId: order, prodId: prodID })
        } else {
          __list.push({ prod: product?.prod_analogsku, order: order, qty: countInRk, prodId: prodID })
        }
      } catch (error) {
        console.log('order controller::reserveGtd error:', error)
      }
    }

    let compos = item.prod_rk
    compos = compos.split(',').filter((item) => item)

    await Promise.all(compos.map(_process))

    if (!!action) {
      const _res = await this.start(__list)
    } else {
      await Promise.all(__list.map((item) => this.productIncrement(item, this.journal)))
    }
  }

  async reserveGtd({ action, reserveList, orderId }: { action: boolean; reserveList: SnapshotBodyItemInterface[]; orderId: number }) {
    async function _reserveRk(item) {
      return await this.gtd_reserveRkWrapper(item, action, orderId)
    }

    const _reserve = async (orderProducts: SnapshotBodyItemInterface[]) => {
      const products: GtdProduct[] = orderProducts.map((product) => {
        return {
          sku: product?.prod_analogsku,
          prod: product?.prod_analogsku,
          gtdQty: product.orderCount,
          qty: product.orderCount,
          orderId,
          prodId: product.prod_id
        }
      })

      const _decrement = async () => {
        console.log('Запуск списания')

        try {
          const _res = await this.start(products)
        } catch (error) {
          console.log('order controller:: _decrement func error:', error)
        }
      }

      const _increment = async () => {
        try {
          // CANCEL ORDER
          console.log('Запуск возврата')

          const _res = await this.increment(orderId, this.journal)
        } catch (error) {
          console.log('order controller:: _increment func error:', error)
        }
      }

      action ? await _decrement() : await _increment()
    }

    _reserve(reserveList.filter((i) => !i.prod_rk))
    let rklist = reserveList.filter((i) => i.prod_rk)

    await Promise.all(rklist.map(_reserveRk))
  }

  setNotExportedMarker(items: GtdSpecData[]) {
    // {
    //     spec_id: 15,
    //     date: 2024-06-06T09:47:47.000Z,
    //     orderId: 32108,
    //     prod: 'NH1323',
    //     qty: 2,
    //     increment: 0,
    //     spec_num: 62,
    //     spec_title: 'Спецификация №72 на поставку товара к CMR №72 от 30.06.2021г.',
    //     spec_date: 2022-03-28T09:43:46.000Z,
    //     spec: 15,
    //     balance: 196,
    //     invoice: '62',
    //     np: '819',
    //     prod_size: '28*39*15',
    //     prod_manuf: 'NHK',
    //     prod_weight: '15.2',
    //     summweight: 30.4
    //     notExported: false
    //   }

    console.log('🚀 ~ Gtd ~ setNotExportedMarker ~ setNotExportedMarker:', items)
  }

  async filterGreenMarker(products: GtdProduct[]) {
    if (products.length === 0) {
      console.error('gtd helper error::"filterGreenMarker":: empty products array')
      return []
    }

    const _process = async (product: GtdProduct) => {
      try {
        const res = await Database.from('spec_list')
          .sum('qty as sumqty')
          .where('prod', product.prod || product.sku)
          .first()
        // .firstOrFail()

        product.sumqty = res.sumqty
        // console.log('🚀 ~ Gtd ~ const_process= ~ product.sumqty:', product.sumqty)

        // if (product.gtdQty > product.sumqty) {
        //   products.splice(
        //     products.findIndex((x) => x.prod == product.prod),
        //     1
        //   )
        // }
      } catch (error) {
        console.error('gtd helper error::"filterGreenMarker::_process"::', error)
      }
    }

    await Promise.all(products.map(_process))

    // return products

    return products.filter((item) => item.gtdQty < item.sumqty)
  }

  async filterCanceled(products: GtdProduct[]) {
    const idList = products.map((item) => item.prodId).filter((i) => i)
    const orderId = products[0].orderId

    if (idList.length > 0) {
      try {
        let res = await Database.from('order_items').whereIn('item_id', idList).andWhere('items_order_id', orderId)
        res = res.filter((item) => item.cancelgtd === 1)

        res.map((item) => {
          let __index = products.findIndex((x) => x.prodId == item.item_id)
          products.splice(__index, 1)
        })

        return products
      } catch (error) {
        console.log('gtd helper error::"filterCanceled":: ', error)
      }
    } else {
      return products
    }
  }

  async journal(d: SpecListItem | IncSpecsListItem, increment = false) {
    try {
      const checkItem = await Database.from('spec_journal').where({
        spec_id: d.specId || d.spec_id,
        orderId: d.orderId || d.order,
        prod: d.prod
      })

      if (increment) {
        const res = await Database.knexQuery()
          .from('spec_journal')
          .insert({
            spec_id: d.specId || d.spec_id,
            orderId: d.orderId || d.order || d.order_id,
            prod: d.prod,
            qty: d.qty,
            increment,
            transit: checkItem.length != 0
          })
      } else {
        const res = await Database.knexQuery()
          .from('spec_journal')
          .insert({
            spec_id: d.specId || d.spec_id,
            orderId: d.orderId || d.order || d.order_id,
            prod: d.prod,
            qty: d.gtdQty,
            increment,
            transit: checkItem.length != 0
          })
      }
    } catch (error) {
      console.log('write journal error::', error)
    }
  }

  async productIncrement(data: IncSpecsListItem, journal: (d: SpecListItem | IncSpecsListItem, increment?: boolean) => Promise<void>) {
    const list = await Database.from('spec_journal').where({ prod: data.prod, orderId: data.orderId, increment: false })

    let incrementList: IncSpecsListItem[] = []
    let errorsMsgs: string[] = []

    let _sorted = list.sort((a, b) => a.qty - b.qty).reverse()

    let _rmn = 0
    let _c = true

    _sorted.map((item) => {
      if (item.qty == data.gtdQty && _c) {
        incrementList.push({ orderId: data.orderId, prod: data.prod, spec_id: item.spec_id, gtdQty: data.gtdQty })
        _c = false
      }
    })

    _sorted.map((item, index) => {
      if (_c) {
        if (data.gtdQty > item.qty) {
          if (_rmn === 0) {
            _rmn = item.qty - data.gtdQty
            index + 1 == _sorted.length && _rmn < 0
              ? errorsMsgs.push(`Ошибка возврата, товар: ${data.prod}, возврат: ${data.gtdQty} | доступно: ${item.qty}`)
              : incrementList.push({ orderId: data.orderId, spec_id: item.spec_id, prod: data.prod, gtdQty: item.qty })
          } else {
            if (item.qty - _rmn > 0) {
              incrementList.push({ orderId: data.orderId, spec_id: item.spec_id, prod: data.prod, gtdQty: Math.abs(_rmn) })
              _c = false
            } else {
              _rmn += item.qty
              incrementList.push({ orderId: data.orderId, spec_id: item.spec_id, prod: data.prod, gtdQty: item.qty })
            }
          }
        } else {
          incrementList.push({ orderId: data.orderId, spec_id: item.spec_id, prod: data.prod, gtdQty: data.gtdQty })
          _c = false
        }
      }
    })

    const applyChanges = async (_data: IncSpecsListItem) => {
      try {
        const List = await Database.from('spec_list').where({ prod: _data.prod, spec: _data.spec_id }).increment('qty', _data.gtdQty)
      } catch (error) {
        console.error('update spec_list error::', error)
      }

      try {
        const Journal = await Database.from('spec_journal').where({ orderId: _data.orderId, prod: _data.prod, spec_id: _data.spec_id, transit: 0 }).decrement('qty', _data.gtdQty)
        const _writeJournal = await journal(_data, true)
      } catch (error) {
        console.error('update journal error::', error)
      }
    }

    await Promise.all(incrementList.map(applyChanges))

    console.log('errorsMsgs:', errorsMsgs)
  }

  async increment(orderId: number, journal: (d: SpecListItem | IncSpecsListItem, increment?: boolean) => Promise<void>) {
    const process = async (data: SpecListItem) => {
      try {
        const res = await Database.from('spec_list').where({ 'prod': data.prod, spec: data.spec_id }).increment('qty', data.qty)
        const Journal = await Database.from('spec_journal').where({ orderId: orderId, prod: data.prod, spec_id: data.spec_id, transit: 0 }).decrement('qty', data.qty)
        const _writeJournal = await journal(data, true)
      } catch (error) {
        console.log('gtd helper::increment: ', error)
      }
    }

    try {
      const list = await Database.from('spec_journal').where({ orderId: orderId, increment: 0, transit: 0 })

      await Promise.all(list.map(process))
    } catch (error) {
      console.log('gtd helper::"increment"::error:', error)
    }
  }

  async decrement(data: IncSpecsListItem, journal: (d: SpecListItem | IncSpecsListItem, increment?: boolean) => Promise<void>) {
    console.log(' decrement data', data)

    try {
      const res = await Database.from('spec_list').where({ 'prod': data.prod, spec: data.specId }).decrement('qty', data.gtdQty).debug(true)
      const Journal = await Database.from('spec_journal').where({ orderId: data.orderId, prod: data.prod, spec_id: data.specId, transit: 0 }).increment('qty', data.gtdQty)
      const _writeJournal = await journal(data)
    } catch (error) {
      console.error('GTD class "decrement": ' + data + ' :error:', error)
    }
  }

  async process(product: GtdProduct): Promise<void> {
    const specs = await Database.from('spec_list').select('*').leftOuterJoin('specs', 'specs.id', 'spec_list.spec').where('prod', product.sku).andWhere('specs.active', 1)

    specs.forEach((spec) => {
      let _c = spec.qty - product.gtdQty
      if (_c === 0) {
        this.incSpecsList.push({ spec_id: spec.id, specId: spec.id, prod: product.sku, gtdQty: product.gtdQty, orderId: product.orderId })
      } else {
        this.analyze.push({
          specId: spec.id,
          specQty: spec.qty,
          prod: product.sku,
          gtdQty: product.gtdQty,
          orderId: product.orderId,
          prodId: product.prodId,
          cnt: _c
        })
      }
    })
  }

  private async __broken_start(products: GtdProduct[]): Promise<{ speclist: IncSpecsListItem[] }> {
    !Array.isArray(products) ? (products = [products]) : false

    try {
      await this.filterCanceled(products)
      await this.filterGreenMarker(products)
    } catch (error) {
      console.log('gtd helper error::"start"::', error)
    }

    const positiveSort = (arr: AnalyzeItem[]) => arr.sort((a, b) => a.cnt - b.cnt)

    const positivePush = (item: AnalyzeItem) => {
      let pushItem = item.cnt ? item : positiveSort(item).slice(-1)[0]
      !this.incSpecsList.find((x) => String(pushItem.prod).toUpperCase() === String(x.prod).toUpperCase()) ? this.incSpecsList.push(pushItem) : false
    }

    const negativePush = (arr: AnalyzeItem[], item: AnalyzeItem) => {
      delete item.cnt
      this.incSpecsList.push(item)
    }

    // summ 'qty' values in duplicats products
    let _filteredList: GtdProduct[] = []

    products.forEach((a) => {
      if (!this[a.sku]) {
        this[a.sku] = { sku: a.sku, gtdQty: 0, orderId: a.orderId, prodId: a.prodId }
        _filteredList.push(this[a.sku])
      }
      this[a.sku].gtdQty += a.gtdQty
    }, Object.create(null))

    // console.log('🚀 ~ Gtd ~ products.forEach ~ products:', products)

    await Promise.all(_filteredList.map((item) => this.process(item)))

    let grpd = this.groupBy(this.analyze, 'prod')

    for (const key in grpd) {
      let element = grpd[key]
      // filter 'specs' for positive decrease
      let filtered = element.filter((i) => i.cnt > 0)

      if (filtered.length > 0) {
        filtered.map((i) => (this.incSpecsList.find((x) => i.specId === (x.spec_id || i.specId)) ? positivePush(i) : positivePush(element)))
      } else {
        element = element.sort((a, b) => a.cnt - b.cnt).reverse()

        let _rmn = 0
        let _c = true

        element.forEach((i, index) => {
          if (_c) {
            if (_rmn == 0) {
              _rmn = i.specQty - i.gtdQty
              i.gtdQty = i.specQty
              negativePush(element, i)
            } else if (_rmn != 0) {
              if (i.specQty < Math.abs(_rmn)) {
                _rmn += i.specQty
                index + 1 == element.length ? (i.gtdQty = i.specQty - _rmn) : (i.gtdQty = i.specQty)
                negativePush(element, i)
              } else {
                i.gtdQty = Math.abs(_rmn)
                _rmn = 0
                _c = false
                negativePush(element, i)
              }
            }
          }
        })
      }
    }

    await Promise.all(this.incSpecsList.map((x) => this.decrement(x, this.journal)))

    return {
      speclist: this.incSpecsList
    }
  }

  async start(products: GtdProduct[]): Promise<{ speclist: IncSpecsListItem[] }> {
    !Array.isArray(products) ? (products = [products]) : false

    try {
      await this.filterCanceled(products)
      await this.filterGreenMarker(products)
    } catch (error) {
      console.log('gtd helper error::"start"::', error)
    }

    const positiveSort = (arr: AnalyzeItem[]) => arr.sort((a, b) => a.cnt - b.cnt)

    const positivePush = (item: AnalyzeItem) => {
      let pushItem = item.cnt ? item : positiveSort(item).slice(-1)[0]
      if (!this.incSpecsList.find((x) => String(pushItem.prod).toUpperCase() === String(x.prod).toUpperCase())) {
        this.incSpecsList.push(pushItem)
      }
    }

    const negativePush = (arr: AnalyzeItem[], item: AnalyzeItem, requiredQty: number) => {
      // Проверяем, есть ли уже списания для этого товара
      const existingDeductions = this.incSpecsList.filter((x) => x.prod === item.prod)
      const alreadyDeducted = existingDeductions.reduce((sum, x) => sum + x.gtdQty, 0)

      // Если требуемое количество уже списано - пропускаем
      if (alreadyDeducted >= requiredQty) {
        return
      }

      // Если нужно списать остаток
      const remainingQty = requiredQty - alreadyDeducted
      if (remainingQty > 0) {
        delete item.cnt
        item.gtdQty = Math.min(remainingQty, item.specQty)
        this.incSpecsList.push(item)
      }
    }

    let _filteredList: GtdProduct[] = []

    products.forEach((a) => {
      if (!this[a.sku]) {
        this[a.sku] = { sku: a.sku, gtdQty: 0, orderId: a.orderId, prodId: a.prodId }
        _filteredList.push(this[a.sku])
      }
      this[a.sku].gtdQty += a.gtdQty
    }, Object.create(null))

    await Promise.all(_filteredList.map((item) => this.process(item)))

    let grpd = this.groupBy(this.analyze, 'prod')

    for (const key in grpd) {
      let element = grpd[key]
      let filtered = element.filter((i) => i.cnt > 0)
      const requiredQty = element[0].gtdQty

      if (filtered.length > 0) {
        filtered.map((i) => (this.incSpecsList.find((x) => i.specId === (x.spec_id || i.specId)) ? positivePush(i) : positivePush(element)))
      } else {
        element = element.sort((a, b) => a.cnt - b.cnt).reverse()
        let _rmn = 0
        let _c = true

        element.forEach((i, index) => {
          if (_c) {
            if (_rmn == 0) {
              _rmn = i.specQty - i.gtdQty
              i.gtdQty = i.specQty
              negativePush(element, i, requiredQty)
            } else if (_rmn != 0) {
              if (i.specQty < Math.abs(_rmn)) {
                _rmn += i.specQty
                index + 1 == element.length ? (i.gtdQty = i.specQty - _rmn) : (i.gtdQty = i.specQty)
                negativePush(element, i, requiredQty)
              } else {
                i.gtdQty = Math.abs(_rmn)
                _rmn = 0
                _c = false
                negativePush(element, i, requiredQty)
              }
            }
          }
        })
      }
    }

    await Promise.all(this.incSpecsList.map((x) => this.decrement(x, this.journal)))

    return {
      speclist: this.incSpecsList
    }
  }
}

export default Gtd
