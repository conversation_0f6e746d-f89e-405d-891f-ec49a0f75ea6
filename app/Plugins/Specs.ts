import Application from '@ioc:Adonis/Core/Application'
import Env from '@ioc:Adonis/Core/Env'
import Order from 'App/Models/Order'
import { exec } from 'child_process'
import dayjs from 'dayjs'
import { promises as fs } from 'fs'
import { PDFDocument, rgb } from 'pdf-lib'
import fontkit from '@pdf-lib/fontkit'

import { PDFExtract } from 'pdf.js-extract'

import path from 'path'

// const SPECS_PATH = process.env.NODE_ENV === 'production' ? path.resolve(Env.get('UPLOAD_PATH') + '/docs/specs') : Application.publicPath('data/docs/specs')
const SPECS_PATH = path.resolve(Env.get('UPLOAD_PATH') + '/docs/specs')

const tempDir = SPECS_PATH + '/temp'

type ExtractConfig = {
  inputXlsxPath: string
  outputPdfPath: string
  searchText: string
  tempDir: string
  label?: string
  // labelCB?: (data: SpecificationData) => string
}

type ExtractResult = {
  success: boolean
  pageNumber?: number
  error?: string
  html?: string
}

export type SpecificationItem = {
  spec_id: number
  date: string
  orderId: number
  prod: string
  qty: number
  increment: number
  spec_num: number
  spec_title: string
  spec_date: string
  filepath: string
  spec: number
  balance: number
  invoice: string
  np: string
  prod_size: string
  prod_manuf: string
  prod_weight: string
  prod_purpose: string
  prod_type: string
  prod_sku: string
  prod_analogsku: string
  summweight: number
}

export type SpecificationData = {
  [key: string]: SpecificationItem[]
}

export type FindPdfInvoiceResult = {
  [key: string]: {
    products: SpecificationItem[]
    pdfLinks: string[]
    mergePdfLink?: string
  }
}

export const concatSpecs = (data) => {
  const result: SpecificationData = {}

  data.forEach((order) => {
    Object.entries(order).forEach(([specId, specGroup]) => {
      const products = Array.isArray(specGroup) ? specGroup : [specGroup]

      products.forEach((product) => {
        const key = product.prod_analogsku
        const targetSpecId = specId

        if (!result[targetSpecId]) {
          result[targetSpecId] = []
        }

        const existingProduct = result[targetSpecId].find((p) => p.prod_analogsku === key)

        if (existingProduct) {
          existingProduct.qty += product.qty
          existingProduct.balance = Math.min(existingProduct.balance, product.balance)
          existingProduct.summweight = existingProduct.qty * parseFloat(existingProduct.prod_weight)
        } else {
          result[targetSpecId].push({ ...product })
        }
      })
    })
  })

  return result
}

export const findInvoicePageByOrders = async (ordersIds: number[]) => {
  const operatedOrders: Awaited<ReturnType<typeof Order.cpanOrderByID>>[] = []

  let ids = String(ordersIds)
    .split(',')
    .filter((i) => i)
    .map((i) => Number(i))

  await Promise.all(
    ids.map(async (i) => {
      let order = await Order.cpanOrderByID(Number(i))
      if (order) {
        order.data.formattedDate = dayjs(order.data.order_datetime).format('DD.MM.YYYY')
        operatedOrders.push(order)
      }
    })
  )

  // order.mergOrders = mergOrders
  // console.log('🚀 ~ PagesController ~ printForm ~ mergOrders:', mergOrders)
  const concatedSpecs = concatSpecs(operatedOrders.map((i) => i?.data.specs))

  return await findInvoicePageBySpecs(concatedSpecs, operatedOrders.map((i) => `#${i?.data.order_id}`).join(','))
}

export const findInvoicePageBySpecsOneFile = async (concatedSpecs: SpecificationData, outputPrefix = '') => {
  const individualResults = await findInvoicePageBySpecs(concatedSpecs, outputPrefix)
  const result: FindPdfInvoiceResult = {}

  console.log('Starting PDF merge process')
  console.log('Individual results:', individualResults)

  for (const specId of Object.keys(individualResults)) {
    const specData = individualResults[specId]
    const outputPdfPath = `${tempDir}/${outputPrefix}_spec_${specId}_merged.pdf`

    console.log(`Processing spec ${specId}:`)
    console.log('PDF links to merge:', specData.pdfLinks)
    console.log('Output path:', outputPdfPath)

    if (specData.pdfLinks.length > 0) {
      const newPdfDoc = await PDFDocument.create()
      let pageCount = 0

      for (const pdfLink of specData.pdfLinks.map((filepath) => path.join(tempDir, path.basename(filepath)))) {
        if (!pdfLink.includes('Ошибка')) {
          console.log('Reading PDF:', pdfLink)
          const pdfBytes = await fs.readFile(pdfLink)
          const pdfDoc = await PDFDocument.load(pdfBytes)
          const pages = await newPdfDoc.copyPages(pdfDoc, pdfDoc.getPageIndices())
          pages.forEach((page) => newPdfDoc.addPage(page))
          pageCount += pages.length
          console.log(`Added ${pages.length} pages from ${pdfLink}`)
        }
      }

      console.log(`Total pages in merged PDF: ${pageCount}`)
      await newPdfDoc.save().then((bytes) => fs.writeFile(outputPdfPath, bytes))
      console.log('Merged PDF saved:', outputPdfPath)

      result[specId] = {
        pdfLinks: specData.pdfLinks,
        mergePdfLink: path.normalize(outputPdfPath.replace('home', '')),
        products: [] //specData.products
      }
    }
  }

  return result
}

export const findInvoicePageBySpecs = async (concatedSpecs: SpecificationData, outputPrefix = '') => {
  const result: FindPdfInvoiceResult = {}

  for (const specId of Object.keys(concatedSpecs)) {
    const specItems = concatedSpecs[specId]
    const pdfLinks = []

    for (const item of specItems) {
      const searchText = String(item.prod_analogsku).trim()
      const filepaths = item.filepath.split(',').map((path) => path.trim())

      let foundInAnyFile = false

      for (const filepath of filepaths) {
        const inputXlsxPath = `${SPECS_PATH}/${filepath}`
        const outputPdfPath = `${tempDir}/${outputPrefix}_spec:${specId}_prod:${item.prod_analogsku}.pdf`

        const label = `${specItems[0]?.spec_title || ''} ${item.prod_analogsku}`

        console.log('🚀 ~ findInvoicePageBySpecs ~ label:', label)
        const pdfGenResult = await extractPdfPage({
          inputXlsxPath,
          outputPdfPath,
          searchText,
          tempDir,
          label
        })

        if (pdfGenResult.success) {
          let normalizeOutputPath = path.normalize(outputPdfPath.replace('home', ''))
          pdfLinks.push(normalizeOutputPath)
          item.invoiceFilePath = normalizeOutputPath
          foundInAnyFile = true
          break
        } else {
          console.error('error: ', pdfGenResult)
        }
      }

      if (!foundInAnyFile) {
        const erf = `Ошибка: ${searchText} - не найден`
        pdfLinks.push(erf)
        item.invoiceFileError = erf
      }
    }

    result[specId] = {
      pdfLinks,
      products: []
    }
  }

  return result
}

const killSofficeProcesses = (): Promise<void> => {
  return new Promise((resolve, reject) => {
    exec('killall -9 soffice.bin', (error, stdout, stderr) => {
      // console.log('killSofficeProcesses: ~ stdout:', stdout)
      // console.log('killSofficeProcesses: ~ stderr:', stderr)

      if (error) {
        // Игнорируем ошибку, если процессов для завершения не найдено
        if (stderr.includes('no process found')) {
          // console.log('killSofficeProcesses: No processes found to kill, continuing.')
          return resolve()
        }
        // Для других ошибок возвращаем reject
        return reject(error)
      }

      resolve()
    })
  })
}

const sanitizeFileName = (filename: string): string => {
  return filename
    .replace(/[^a-z0-9]/gi, '_')
    .toLowerCase()
    .replace(/_+/g, '_')
}

const convertXlsxToPdf = async (inputPath: string, tempDir: string): Promise<void> => {
  const command = `soffice --norestore --headless --convert-to pdf "${inputPath}" --outdir "${tempDir}"`
  return new Promise(async (resolve, reject) => {
    try {
      await killSofficeProcesses()
    } catch (error) {
      // console.log('🚀 ~ killSofficeProcesses ~ error:', error)
    }

    // console.log('🚀 ~ convertXlsxToPdf ~ init command:', command)
    exec(command, async (error, stdout, stderr) => {
      // console.log('convertXlsxToPdf: ~ exec ~ stdout:', stdout)
      // Игнорируем ошибку если это предупреждение о javaldx
      if (error && !stderr.includes('javaldx')) {
        reject(error)
      } else {
        try {
          await killSofficeProcesses()
        } catch (error) {}
        resolve()
      }
    })
  })
}

const findPageWithText = async (pdfPath: string, searchText: string): Promise<number> => {
  const pdfExtract = new PDFExtract()
  const data = await pdfExtract.extract(pdfPath)

  return data.pages.findIndex((page) => page.content.some((item) => item.str.includes(searchText)))
}

const extractPage = async (pdfPath: string, pageNumber: number, outputPath: string, label = ''): Promise<void> => {
  const pdfBytes = await fs.readFile(pdfPath)
  const pdfDoc = await PDFDocument.load(pdfBytes)

  const newPdfDoc = await PDFDocument.create()
  newPdfDoc.registerFontkit(fontkit) // Register fontkit on newPdfDoc

  const [page] = await newPdfDoc.copyPages(pdfDoc, [pageNumber])
  newPdfDoc.addPage(page)

  if (label) {
    const { width, height } = page.getSize()
    const fontBytes = await fs.readFile(path.join(SPECS_PATH, 'font.ttf'))
    const font = await newPdfDoc.embedFont(fontBytes)

    const fontSize = 8
    const textWidth = font.widthOfTextAtSize(label, fontSize)
    const margin = 20

    page.drawText(label, {
      x: width - textWidth - margin,
      y: height - 20,
      size: fontSize,
      font,
      color: rgb(0.6, 0.6, 0.6)
    })
  }

  const modifiedPdfBytes = await newPdfDoc.save()
  await fs.writeFile(outputPath, modifiedPdfBytes)
}

const old_extractPage = async (pdfPath: string, pageNumber: number, outputPath: string, label = ''): Promise<void> => {
  const pdfBytes = await fs.readFile(pdfPath)
  const pdfDoc = await PDFDocument.load(pdfBytes)
  pdfDoc.registerFontkit(fontkit)

  const newPdfDoc = await PDFDocument.create()
  const [page] = await newPdfDoc.copyPages(pdfDoc, [pageNumber])
  newPdfDoc.addPage(page)

  if (label) {
    console.log('🚀 ~ extractPage ~ label:', label)
    const { width, height } = page.getSize()

    const fontBytes = await fs.readFile(path.join(SPECS_PATH, 'font.ttf'))
    const font = await newPdfDoc.embedFont(fontBytes)

    await page.drawText(label, {
      x: width - 200,
      y: height - 20,
      size: 10,
      font
    })
  }

  const modifiedPdfBytes = await newPdfDoc.save()
  await fs.writeFile(outputPath, modifiedPdfBytes)
}

export const extractPdfPage = async (config: ExtractConfig): Promise<ExtractResult> => {
  const tempPdfPath = `${config.tempDir}/${config.inputXlsxPath.split('/').pop()?.replace('.xlsx', '')}.pdf`

  try {
    await convertXlsxToPdf(config.inputXlsxPath, config.tempDir)
    const pageNumber = await findPageWithText(tempPdfPath, config.searchText)
    console.log('🚀 ~ extractPdfPage ~ pageNumber:', pageNumber)

    if (pageNumber === -1) {
      await fs.unlink(tempPdfPath)
      return { success: false }
    }

    await extractPage(tempPdfPath, pageNumber, config.outputPdfPath, config.label)
    // const html = await convertPdfToHtml(config.outputPdfPath)
    await fs.unlink(tempPdfPath)

    return {
      success: true,
      pageNumber: pageNumber + 1
      // html
    }
  } catch (error) {
    console.log('🚀 ~ extractPdfPage ~ error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

const main = async () => {
  const searchText = 'A00987'
  const tempDir = '/home/<USER>/Dev/RTI/rti-api/_scripts/'
  const inputXlsxPath = '/home/<USER>/Dev/RTI/rti-api/_scripts/spec_test.xlsx'
  const outputPdfPath = '/home/<USER>/Dev/RTI/rti-api/_scripts/spec_test_res.pdf'

  const config: ExtractConfig = {
    inputXlsxPath,
    outputPdfPath,
    searchText,
    tempDir
  }

  const result = await extractPdfPage(config)

  if (result.success) {
    console.log(`Страница ${result.pageNumber} успешно извлечена`)
  } else if (result.error) {
    console.error('Ошибка:', result.error)
  } else {
    console.log('Текст не найден в документе')
  }
}

// main()
