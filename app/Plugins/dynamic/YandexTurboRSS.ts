import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import loadSettings from "App/Helpers/loadSettings"
import Product from "App/Models/Product"
const convert = require('xml-js')

function unesc (html) {
    return String(html)
      .replace(/&quot;/g, '"')
      .replace(/&#39;/g, "'")
      .replace(/&#x3A;/g, ':')
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&amp;/g, '&')
      .replace(/&(#(?:\d+)|(?:#x[0-9A-Fa-f]+)|(?:\w+));?/gi, function (a, n) {
        n = n.toLowerCase();
  
        if (n === 'colon') {
          return ':';
        }
  
        if (n.charAt(0) === '#') {
          return n.charAt(1) === 'x'
            ? String.fromCharCode(parseInt(n.substring(2), 16))
            : String.fromCharCode(+n.substring(1));
        }
  
        return a;
      });
  };

class YandexTurboRSS {

    catalog: Array<Product>
    limit: number
    page: number
    siteurl: string
    settings: object

    constructor() {
        this.siteurl = 'https://mirsalnikov.ru'
    }

    async loadProducts(page, limit) {

        let _catalog = await Product
                                    .query()
                                    .leftOuterJoin('cats', 'products.prod_cat', 'cats.cat_id')
                                    .select('*')
                                    .orderBy('prod_id', 'desc')
                                    .paginate(page, limit)

        this.catalog = _catalog.toJSON().data
    }

    async getSettings() {
        this.settings = await loadSettings([
            'yandex.turbo.rss.link', 
            'yandex.turbo.rss.cdata', 
            'yandex.turbo.rss.sitelink', 
            'yandex.turbo.rss.description'
        ])
        
        return this.settings
    } 

    makeLink(product: Product): string {
        let str = eval('`' + this.settings['yandex.turbo.rss.link'] + '`')
        return str
    }

    makeCDATA(product: Product): string {
        let str =  eval('`' + this.settings['yandex.turbo.rss.cdata'] + '`')
        return str
    }
}

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
    
    const { page = 1, limit = 10 } = request.qs()

    const RSS = new YandexTurboRSS()

    await RSS.getSettings()
    await RSS.loadProducts(page, limit)
    
    let products = RSS.catalog.map(product => {
        return {
            _attributes: {
                "turbo": 'true'
            },
            link: RSS.makeLink(product),
            'turbo:content': RSS.makeCDATA(product)
        }
    })

    const json = {
        _declaration: {
            _attributes: {
                "version": "1.0", 
                "encoding": "UTF-8"
            }
        },
        rss: {
            _attributes: {
                "xmlns:yandex": "http://news.yandex.ru",
                "xmlns:media": "http://search.yahoo.com/mrss/",
                "xmlns:turbo": "http://turbo.yandex.ru",
                "version":"2.0"
            },
            channel: {
                title: 'Мир Сальников',
                link: RSS.settings['yandex.turbo.rss.sitelink'] || RSS.siteurl,
                description: RSS.settings['yandex.turbo.rss.description'] || '',
                item: products
            }
        }
    }


    const options = { compact: true, ignoreComment: true, spaces: 4, fullTagEmptyElement:true }
    let result = convert.json2xml(json, options)

    response.header('Content-type', 'application/xml')
    response.type('application/xml')
    
    return unesc(result)
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        try {
            return await AlCB(httpCtx)
        } catch (error) {
            console.error('YandexTurboRSS: ', error)
            return httpCtx.response.status(500).send(String(error))
        }
    },
    route: '/yandex/turbo/rss'
}

export default plugin