import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
// import loadSettings from "App/Helpers/loadSettings"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import Product from "App/Models/Product"
import User from "App/Models/User"
import { FilePlugin } from "../Files"

const fs = require('fs')


const AlCB = async ({ request, params, response, auth, session, }: HttpContextContract) => {
    const ip = request.ip()    
    // const user: User = await auth.use('api').authenticate()

    const addImages = request.files('addImgs')
    const mainImage = request.file('mainImg')


    const {target: targetProductID} = request.qs()
    if (!targetProductID) throw new Error("targetProduct undefined")

    const product = await Product.findOrFail(targetProductID)

    if (addImages?.length) {
        product.prod_images = [...new Set(addImages.map(image => encodeURIComponent(image.clientName)), ...String(product.prod_images).split(','))].filter(i => i).join()
    
        for (let image of addImages) {
            const copyname = image.tmpPath + '10'
            fs.copyFileSync(image.tmpPath, copyname)
    
            await FilePlugin.save({ file: image, type: 'rti' })
            image.tmpPath = copyname
            await FilePlugin.save({ file: image, type: 'rumi'})
        }
     
    } else if (mainImage) {
        const copyname = mainImage.tmpPath + '10'
        fs.copyFileSync(mainImage.tmpPath, copyname)

        await FilePlugin.save({ file: mainImage, type: 'rti' })
        mainImage.tmpPath = copyname
        await FilePlugin.save({ file: mainImage, type: 'rumi'})
        
        product.prod_img = String(mainImage.clientName).split('.')[0]
        product.prod_img_rumi = String(mainImage.clientName).split('.')[0]

    } else {
        throw new Error("image empty");
    }


    return await product.save()

    //   return product.prod_images.split(',')
}

const plugin: dynPluginInterface = {
    httpmethods: ['POST'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/files/upload/images'
}

export default plugin