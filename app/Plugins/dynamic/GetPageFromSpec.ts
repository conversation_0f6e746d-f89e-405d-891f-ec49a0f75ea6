import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { findInvoicePageByOrders } from 'App/Plugins/Specs'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { ordersIds } = httpCtx.request.all()

  return await findInvoicePageByOrders(ordersIds)
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/pagefromspecbyproduct/'
}

export default plugin
