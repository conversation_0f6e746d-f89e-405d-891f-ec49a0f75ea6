import type { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import type dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { MeiliSearchPlugin } from '../MeiliSearch'
import { isSize } from 'App/Helpers/isSize'
import { SIZE_TOLERANCE } from 'App/Providers/ProductProvider'
import { ProductProvider } from 'App/Providers/ProductProvider'

const AlCB = async (httpCtx: HttpContextContract) => {
  const meiliSearch = new MeiliSearchPlugin({})
  const productProvider = new ProductProvider()

  const { q } = httpCtx.request.all()

  // Проверяем наличие размера в запросе
  const sizeData = productProvider.extractSizeData(q)

  // Базовые параметры поиска
  const searchParams = {
    highlightPreTag: '__ais-highlight__',
    highlightPostTag: '__/ais-highlight__',
    limit: 10,
    offset: 0,
    matchingStrategy: 'frequency',
    // attributesToCrop: ['prod_secret', 'prod_purchase', 'prod_suppplier'],
    sort: ['inStock:desc', 'cat_search_sort:asc'],
    attributesToSearchOn: [
      'prod_sku',
      'prod_analogsku',
      'prod_manuf',
      'prod_note',
      'prod_year',
      'prod_type',
      'prod_uses',
      'prod_purpose',
      'prod_analogs',
      'prod_model',
      'prod_material',
      'skuTokens',
      'prod_size'
    ],
    attributesToRetrieve: ['prod_id', 'prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_img', 'prod_count', 'prod_type', 'prod_purpose', 'prod_material', 'prod_size'],
    attributesToHighlight: ['prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_purpose', 'prod_analogs', 'prod_size'],
    rankingScoreThreshold: sizeData ? 0.5 : 0.4
  }

  if (sizeData) {
    // Добавляем фильтры по размеру
    searchParams.filter = productProvider.getMeiliSizeFiltersExact(sizeData)

    // Добавляем сортировку по размеру
    searchParams.sort = [
      // ...productProvider.buildMeiliSortConditions([{ column: 'prod_size', direction: 'asc' }]),
      ...searchParams.sort
    ]

    // Обрабатываем поисковый запрос, удаляя из него размеры
    const processedQuery = productProvider.processSearchQuery(q)

    return await meiliSearch.client.index('products').search(processedQuery, searchParams)
  }

  // Если размер не найден, выполняем обычный поиск
  return await meiliSearch.client.index('products').search(q, searchParams)
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET', 'POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/instantsearch'
}

export default plugin
