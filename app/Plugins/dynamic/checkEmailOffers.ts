import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { EmailMonitorService } from 'App/Services/EmailProcessingService'

const cbf = async (httpCtx: HttpContextContract) => {
  const emailMonitorService = new EmailMonitorService()

  emailMonitorService.start()

  return 'ok'
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await cbf(httpCtx)
  },
  route: '/checkemailoffers/'
}

export default plugin
