import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import Journal from 'App/Models/Journal'
import { getEmails } from '../Imap'

import { minify } from 'html-minifier'

const CB = async (httpCtx: HttpContextContract) => {
  let { clientemail, type = 'in' }: { clientemail?: string; type?: 'in' | 'out' } = httpCtx.request.qs()

  const currentDate = new Date()
  const currentYear = currentDate.getFullYear()
  const sixMonthsAgo = new Date(currentDate.getFullYear(), currentDate.getMonth() - 6, currentDate.getDate())

  const minifyHTML = (html: string) =>
    minify(html, {
      collapseWhitespace: true,
      continueOnParseError: true,
      decodeEntities: true,
      minifyCSS: true,
      minifyJS: true,
      minifyURLs: true,
      removeAttributeQuotes: true,
      removeComments: true,
      removeEmptyAttributes: true,
      removeEmptyElements: true,
      removeScriptTypeAttributes: true,
      removeTagWhitespace: true
    })

  const payload = {
    in: {
      searchCriteria: [
        // ['OR', ['SEEN'], ['UNSEEN']],
        ['FROM', clientemail],
        ['SINCE', sixMonthsAgo.toISOString()]
      ],
      box: 'INBOX' //'Sent' //'INBOX'
    },
    out: {
      searchCriteria: [
        // ['OR', ['SEEN'], ['UNSEEN']],
        ['TO', clientemail],
        ['SINCE', sixMonthsAgo.toISOString()]
      ],
      box: 'Sent' //'Sent' //'INBOX'
    }
  }

  try {
    let list = await getEmails(payload[type])
    let exlist = []

    // if (type == 'out') {
    //   exlist = await getEmails({
    //     ...payload[type],
    //     box: 'Sent',
    //     configName: 'timeweb'
    //   })
    // }

    return (
      list
        // .concat(exlist)
        .filter((i) => i)
        .map((item) => ({
          html: minifyHTML(item.html || ''),
          date: item.date,
          cc: item.cc,
          subject: item.subject,
          type
        }))
        .sort((a, b) => new Date(b.date) - new Date(a.date))
    )
  } catch (error) {
    return httpCtx.response.status(500).send({ error })
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    try {
      return await CB(httpCtx)
    } catch (error) {
      console.error('dynamic plugin GetEmailByClient: ', error)
      return httpCtx.response.status(500).send(String(error))
    }
  },
  route: '/emails/box/'
}

export default plugin
