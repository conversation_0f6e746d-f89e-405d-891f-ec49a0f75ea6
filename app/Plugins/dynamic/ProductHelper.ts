import Env from '@ioc:Adonis/Core/Env'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Database from '@ioc:Adonis/Lucid/Database'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { DateTime } from 'luxon'
const path = require('path')

const fs = require('fs')

interface MIP {
  prod_id: number
  prod_analogsku?: string
  prod_sku?: string
  prod_img?: string
  prod_img_rumi?: string
}

const CB = async (httpCtx: HttpContextContract) => {
  const { target = 'rti', toFile = false, dir, inStock = true, category, testIds } = httpCtx.request.qs()

  const columns = {
    rti: ['prod_analogsku', 'prod_sku', 'prod_img'],
    rumi: ['prod_sku', 'prod_analogsku', 'prod_img_rumi']
  }

  const resProds: any[] = []

  console.log('start download catalog')
  //console.time('catalog downloaded')
  const products: MIP[] = await Database.from('products')
    .select([...columns[target], 'prod_id', 'prod_cat'])
    .if(inStock, (query) => query.where('prod_count', '>', 0))
    .if(category, (query) => query.andWhere('prod_cat', category))
    .if(testIds, (query) => query.whereIn('prod_id', testIds.split(',')))
    .orderBy('prod_count', 'desc')

  //console.timeEnd('catalog downloaded')

  const filespath = dir || Env.get('UPLOAD_PATH') + '/' + target

  console.log('start read filedir')
  //console.time('filedir readed')
  let files: string[] = fs.readdirSync(filespath)
  // files = files.map((filename) => String(filename.split(".")[0]).trim());
  files = files.map((filename) => path.parse(filename).name.trim())
  //console.timeEnd('filedir readed')

  console.log('start map files')
  //console.time('map file success')

  products.map((product) => {
    let f = columns[target].some((col) => files.includes(String(product[col]).trim()))

    if (!f) {
      resProds.push({
        id: product.prod_id,
        oem: product.prod_analogsku,
        code: product.prod_sku,
        Ссылка: `=HYPERLINK("https://${target == 'rti' ? 'mirsalnikov.ru' : 'rumisota.eu'}/catalog/product/${product.prod_id}", "Открыть")`
      })
    }
  })

  //console.timeEnd('map file success')

  console.log('CATALOG LENGTH:', products.length)
  console.log('RES LENGTH: ', resProds.length)

  if (toFile) {
    // ------------- WRITE FILE
    //console.time('WRITE xlsx')

    let XLSX
    if (typeof require !== 'undefined') XLSX = require('xlsx')

    const wb = XLSX.utils.book_new()
    const ws = XLSX.utils.json_to_sheet(resProds)

    XLSX.utils.book_append_sheet(wb, ws, 'Отчет')

    const dn = DateTime.now().toLocal()
    const buffer = XLSX.write(wb, {
      type: 'array',
      bookType: 'xlsx',
      compression: true
    })

    //console.timeEnd('WRITE xlsx')

    httpCtx.response.header('Content-type', 'application/zip')
    httpCtx.response.header('Content-Disposition', `attachment; filename="${target}_products_withoutphoto_${dn}.xlsx"`)
    httpCtx.response.send(Buffer.from(buffer, 'binary'))

    // ---------------- ./ WRITE file

    return []
  }

  return {
    data: resProds,
    catalogLength: products.length,
    resQty: resProds.length
  }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    try {
      return await CB(httpCtx)
    } catch (error) {
      console.error('dynamic plugin ProductHelper: ', error)
      return httpCtx.response.status(500).send(String(error))
    }
  },
  route: '/products/productwithoutphoto'
}

export default plugin
