

const stripe = require('stripe')('***********************************************************************************************************')
//('***********************************************************************************************************')


class StripeClass {
    async paymentIntent({ amount, currency, email, paymentMethods = ['card'] }) {
        const res = await stripe.paymentIntents.create({
            amount: amount * 100,
            currency,
            payment_method_types: paymentMethods,
            receipt_email: email,
        })

        return res
    }
}

export const Stripe = new StripeClass()



