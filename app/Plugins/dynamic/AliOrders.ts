import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import { AliexpressOrders } from "App/Plugins/AliexpressOrders"


// TODO: move create order method to Order model
import OrdersController from 'App/Controllers/Http/OrdersController'
import { rtiOrder } from "App/Interfaces/orders/RtiOrder"
const ordersController = new OrdersController()

const AlCB = async (httpCtx: HttpContextContract) => {
   
    
   const AI = new AliexpressOrders()
   await AI.init()

   const orders = await AI.getOrders({toRTI: true})

    for (const item of orders) {
        // console.log('order ITEM: ', item);
        httpCtx.request.updateBody({
           ...item.orderData,
           orderItems: item.orderItems,
           sysnotice: item.sysnotice
        })
        
        // console.log('orderData: ', httpCtx.request.all().orderData)
        // console.log('orderItems: ', httpCtx.request.all().orderItems)

        await ordersController.create(httpCtx)
    }

   
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/aliexpress/orders/'
}

export default plugin