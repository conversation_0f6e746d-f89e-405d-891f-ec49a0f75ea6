import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { MeiliSearchPlugin } from '../MeiliSearch'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { action } = httpCtx.request.qs()
  const meiliSearch = new MeiliSearchPlugin({})

  if (action === 'syncProducts') {
    const { products } = httpCtx.request.body()

    return await meiliSearch.syncProducts(products)
  }

  if (action === 'syncProductsByPrisma') {
    const { products } = httpCtx.request.body()

    return await meiliSearch.syncProductsByPrisma(products)
  }

  if (action === 'syncCategories') {
    return await meiliSearch.syncCategories()
  }
  if (action === 'syncCategoriesByPrisma') {
    return await meiliSearch.syncCategoriesByPrisma()
  }

  if (action === 'syncColumns') {
    return await meiliSearch.syncColumns()
  }
  if (action === 'syncColumnsByPrisma') {
    return await meiliSearch.syncColumnsByPrisma()
  }

  if (action === 'syncFilters') {
    return await meiliSearch.syncFilters()
  }

  if (action === 'syncFiltersByPrisma') {
    return await meiliSearch.syncFiltersByPrisma()
  }

  if (action === 'search') {
    const { query } = httpCtx.request.qs()
    return meiliSearch.productsDB().search(query)
  }

  if (action === 'syncAll') {
    return await meiliSearch.syncAll()
  }

    if (action === 'syncAllByPrisma') {
      return await meiliSearch.syncAllByPrisma()
    }
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/meilisearch/'
}

export default plugin
