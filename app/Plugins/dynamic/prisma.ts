import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import { productProvider } from 'App/Providers/ProductProvider'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { id } = httpCtx.request.qs()

  const res = await productProvider.findByCategoryIdOrUrl({
    identifier: 5,
    limit: 3000
  })

  return res
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/prisma/'
}

export default plugin
