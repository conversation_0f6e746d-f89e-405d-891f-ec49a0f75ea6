import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import loadSettings from 'App/Helpers/loadSettings'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'
import Cache from 'App/Models/Cache'

const { api } = require('zadarma')

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
  const ip = request.ip()

  const { zadarma_api_secret, zadarma_api_key } = await loadSettings(['zadarma_api_secret', 'zadarma_api_key'])

  const { payload = {}, method = '' } = request.body()
  // console.log("🚀 ~ file: Zadarma_webrtc.ts:14 ~ AlCB ~ { payload = {}, method = '' }", { payload, method })

  const fCache = await Cache.query()
    .where({
      ckey: 'zadarmastats',
      hash: JSON.stringify({ payload, method })
    })
    .andWhere('updated_at', '>=', new Date(Date.now() - 1000 * 60 * 20))
    .first()

  if (fCache) {
    return fCache.body
  }

  let res = await api({
    api_method: method,
    api_user_key: zadarma_api_key,
    api_secret_key: zadarma_api_secret,
    params: {
      // sip:  '186191-100',
      ...payload
    }
  })

  const cacheRes = await Cache.updateOrCreate(
    {
      ckey: 'zadarmastats',
      hash: JSON.stringify({ payload, method })
    },
    {
      body: res,
      ckey: 'zadarmastats',
      hash: JSON.stringify({ payload, method })
    }
  )

  return res
}

const plugin: dynPluginInterface = {
  httpmethods: ['POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/zadarma/'
}

export default plugin
