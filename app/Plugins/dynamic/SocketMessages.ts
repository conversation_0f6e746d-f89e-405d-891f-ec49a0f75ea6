import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
// import loadSettings from "App/Helpers/loadSettings"
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

const fs = require('fs')

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
  const ip = request.ip()
  // const user: User = await auth.use('api').authenticate()

}

const plugin: dynPluginInterface = {
  httpmethods: ['POST'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/socket/newmsg'
}

export default plugin
