import Database from '@ioc:Adonis/Lucid/Database'
import { Prisma, products } from '@prisma/client'
import Product from 'App/Models/Product'
// import { productProvider } from 'App/Providers/ProductProvider'
import { $prisma } from 'App/Services/Prisma'
import { MeiliSearch } from 'meilisearch'

export class MeiliSearchPlugin {
  client: MeiliSearch
  index: 'products' | string

  constructor({ host = 'http://localhost:7700', apiKey = 'aSampleMasterKey', index = 'products' }) {
    this.client = new MeiliSearch({
      host,
      apiKey
    })
    this.index = index || 'products'
  }

  // async transformToAdonisProducts(hits) {
  //   return productProvider.initAndTransformToAdonisModel(hits)
  // }

  productsDB() {
    return this.client.index(this.index)
  }

  old_generateSkuTokens(articles: string | string[]): string[] {
    const tokens = new Set<string>()

    // Преобразуем входные данные в массив, если передана строка
    const articlesArray = Array.isArray(articles) ? articles : [articles]

    // Обрабатываем каждый артикул
    for (const article of articlesArray) {
      // Пропускаем пустые значения
      if (!article) continue

      // Приводим артикул к нижнему регистру и обрезаем пробелы
      const normalized = article.trim().toLowerCase()

      // Всегда добавляем полный артикул, независимо от длины
      tokens.add(normalized)

      // Разбиваем артикул по разделителям: дефис, слеш, пробел, запятая, точка, двоеточие и т.д.
      const segments = normalized.split(/[\-\/\s,.;:]+/).filter((seg) => seg.length > 0)

      // Добавляем токен без разделителей (конкатенация сегментов)
      if (segments.length > 1) {
        const noSeparators = segments.join('')
        tokens.add(noSeparators)
      }

      // Проверяем, начинается ли артикул с буквы, за которой следуют цифры
      const prefixMatch = normalized.match(/^([a-z]+)(\d+.*)$/)
      if (prefixMatch) {
        const prefix = prefixMatch[1] // Буквенный префикс
        const numericPart = prefixMatch[2] // Числовая часть

        // Добавляем числовую часть как отдельный токен
        tokens.add(numericPart)
      }

      // Для каждого сегмента генерируем все суффиксы длиной не менее 3 символов
      for (const segment of segments) {
        for (let i = 0; i < segment.length; i++) {
          const token = segment.substring(i)
          if (token.length > 3) {
            tokens.add(token)
          }
        }
      }

      // Генерируем токены для артикула без разделителей (конкатенация сегментов)
      const concatenated = segments.join('')
      for (let i = 0; i < concatenated.length; i++) {
        const token = concatenated.substring(i)
        if (token.length > 3) {
          tokens.add(token)
        }
      }
    }

    // Дополнительная фильтрация токенов: удаляем пустые значения и дубликаты
    return Array.from(tokens).filter((token) => {
      // Удаляем только пустые токены
      return token && token.trim().length > 0
    })
  }

  generateSkuTokens(articles: string | string[], minSuffixLength = 4, splitPattern = /[^a-z0-9]+/): string[] {
    const MIN_TOKEN_LENGTH = 3
    const tokens = new Set<string>()
    const list = Array.isArray(articles) ? articles : [articles]

    for (const raw of list) {
      if (!raw) continue
      const art = raw.trim().toLowerCase()
      // tokens.add(art)

      const segments = art.split(splitPattern).filter(Boolean)
      const base = segments.join('')
      if (segments.length > 1) tokens.add(base)

      // Все суффиксы от конкатенации сегментов
      for (let i = 0; i < base.length; i++) {
        const suf = base.substring(i)
        if (suf.length >= minSuffixLength) tokens.add(suf)
      }

      // Группы «буквы+цифры»
      const groups = art.match(/[a-z]+\d+|\d+/g)
      if (groups) groups.forEach((g) => tokens.add(g))
    }

    return [...tokens].filter((t) => t.length > MIN_TOKEN_LENGTH)
  }

  async _search(value) {
    const res = await this.client.index(this.index).search(value, {
      attributesToRetrieve: [
        'prod_id'
        // 'prod_sku'
      ],
      limit: 200
      // attributesToHighlight: ['prod_sku']
    })

    return res
  }
  async syncCategoriesByPrisma() {
    //console.time('syncCategoriesByPrisma')
    const rawCategories = await $prisma.cats.findMany({
      where: {
        cat_active: true
      }
    })

    const categories = rawCategories //.map(({ cat_id: id, ...rest }) => ({ id, ...rest }))

    await this.client.deleteIndex('categories')
    await this.client.createIndex('categories', { primaryKey: 'cat_id' })

    await this.client.index('categories').updateSettings({
      filterableAttributes: [
        'cat_id',
        'cat_url',
        'cat_rootcat',
        'cat_active',
        'cat_url_ru',
        'cat_url_de',
        'cat_url_en',
        'cat_url_es',
        'cat_url_it',
        'cat_url_de',
        'cat_url_fr',
        'cat_url_pl'
      ],
      sortableAttributes: ['cat_sort', 'cat_search_sort']
    })

    await this.updateRankingRulesSortFirst('categories')

    const result = await this.client.index('categories').addDocuments(categories)

    // .then((res) => console.log('Categories synced:', res))

    console.log('MeiliSearch response:', result)

    //console.timeEnd('syncCategoriesByPrisma')
    return result
  }

  async syncColumnsByPrisma() {
    //console.time('syncColumnsByPrisma')
    const rawColumns = await $prisma.cat_columns.findMany({
      orderBy: {
        ID: 'asc'
      }
    })

    // const columns = rawColumns.map(({ ID: id, cat_id: catId, ...rest }) => ({ id, catId, ...rest }))

    // const columns = rawColumns.map(c => {
    //   return {
    //     id: c.ID,
    //     keyname: c.keyname,
    //     title: c.title,
    //     sort: c.sort,
    //     cat_ide: c.cat_id,
    //     sorted: c.sorted,
    //     slot: c.slot
    //   }
    // })

    const columns = rawColumns

    await this.client.deleteIndex('columns')
    await this.client.createIndex('columns', { primaryKey: 'ID' })
    await this.client.index('columns').updateSettings({
      filterableAttributes: ['cat_id', 'keyname'],
      sortableAttributes: ['sort', 'ID']
    })

    await this.updateRankingRulesSortFirst('columns')

    await this.client
      .index('columns')
      .addDocuments(columns)
      .then((res) => console.log('Columns synced:', res))

    //console.timeEnd('syncColumnsByPrisma')
  }

  async syncFiltersByPrisma() {
    //console.time('syncFiltersByPrisma')
    const filters = await $prisma.filters.findMany()

    await this.client.deleteIndex('filters')
    await this.client.createIndex('filters', { primaryKey: 'id' })

    await this.client.index('filters').updateSettings({
      filterableAttributes: ['category_id', 'field']
    })

    await this.updateRankingRulesSortFirst('filters')

    await this.client
      .index('filters')
      .addDocuments(filters)
      .then((res) => console.log('Filters synced:', res))

    //console.timeEnd('syncFiltersByPrisma')
  }

  // Где-то при инициализации или настройке индекса
  async updateRankingRulesSortFirst(documentName = 'products') {
    try {
      const newRankingRules = ['sort', 'words', 'typo', 'proximity', 'attribute', 'exactness']

      const task = await this.client.index(documentName).updateRankingRules(newRankingRules)
      console.log('Update ranking rules task:', task)
      // Можно дождаться завершения задачи
      await this.client.tasks?.waitForTask(task.taskUid)
      console.log('Ranking rules updated successfully.')
    } catch (error) {
      console.error('Error updating ranking rules:', error)
    }
  }

  // 17.04.25 - old ['exactness', 'sort', 'words', 'typo', 'proximity', 'attribute']
  // 16.05.25 - old ['exactness', 'attribute', 'sort', 'words', 'typo', 'proximity']
  async updateRankingRules(documentName = 'products', newRankingRules = ['exactness', 'sort', 'attribute', 'words', 'typo', 'proximity']) {
    try {
      const task = await this.client.index(documentName).updateRankingRules(newRankingRules)
      console.log('Update ranking rules task:', task)
      // Можно дождаться завершения задачи
      await this.client.tasks?.waitForTask(task.taskUid)
      console.log('Ranking rules updated successfully.')
    } catch (error) {
      console.error('Error updating ranking rules:', error)
    }
  }

  // Вызвать эту функцию один раз для обновления настроек индекса
  // updateRankingRulesSortFirst();

  async syncProductsByPrisma(uploadProducts: products[] | undefined = undefined) {
    //console.time('syncProductsByPrisma')

    const { clientProductDBfields } = Product.getProductColumns()
    const sizeFields = ['size_in', 'size_in_2', 'size_out', 'size_out_2', 'size_h', 'size_h_2']

    const sortableAndFilterableFields = clientProductDBfields.map((i) => i.split('.')[1]).filter((i) => i)

    sortableAndFilterableFields.push(...sizeFields)

    sortableAndFilterableFields.push('prod_model')
    sortableAndFilterableFields.push('inStock')
    sortableAndFilterableFields.push('cat_search_sort')

    // sortableAndFilterableFields.push('skuTokens')

    // console.log("🚀 ~ MeiliSearchPlugin ~ syncProductsByPrisma ~ sortableAndFilterableFields:", sortableAndFilterableFields)

    const categories = await $prisma.cats.findMany({
      select: {
        cat_id: true,
        cat_title: true,
        cat_note: true,
        cat_search_sort: true,
        cat_sort: true
      },
      where: {
        cat_active: true,
        duplicate: false
      }
    })

    const products =
      uploadProducts ||
      (await $prisma.products.findMany({
        where: {
          prod_cat: {
            in: (
              await $prisma.cats.findMany({
                where: { cat_active: true },
                select: { cat_id: true }
              })
            ).map((cat) => String(cat.cat_id))
          }
        },
        orderBy: {
          prod_id: 'desc'
        }
      }))

    // const transformedProducts = products.map((product) => ({
    //   ...product,
    //   id: product.prod_id
    // }))

    // Создаем карту категорий для быстрого доступа
    const categoriesMap = new Map(categories.map((cat) => [String(cat.cat_id), cat]))

    const meiliProducts = products.map((product) => {
      const meiliProduct: Record<string, any> = { ...product }
      for (const field of sizeFields) {
        if (meiliProduct[field]) {
          meiliProduct[field] = Number(meiliProduct[field])
        }
      }
      meiliProduct.inStock = meiliProduct.prod_count > 0

      // Создаем массив артикулов для генерации токенов
      const skuArray = [meiliProduct.prod_sku, meiliProduct.prod_analogsku, ...(meiliProduct.prod_analogs ? meiliProduct.prod_analogs.split(',') : [])].filter(
        Boolean
      ) // Удаляем пустые значения

      meiliProduct.skuTokens = this.generateSkuTokens(skuArray as string[])

      // Добавляем информацию о категории к товару
      const category = categoriesMap.get(String(meiliProduct.prod_cat))
      if (category) {
        // Принудительно преобразуем cat_search_sort в число
        const cat_search_sort = Number(category.cat_search_sort) || 0

        meiliProduct.category = {
          cat_id: category.cat_id,
          cat_title: category.cat_title,
          cat_search_sort: cat_search_sort
        }

        // Добавляем поле напрямую в корень товара для удобства сортировки
        meiliProduct.cat_search_sort = cat_search_sort
      }
      return meiliProduct
    })

    // console.log('🚀 ~ MeiliSearchPlugin ~ syncProductsByPrisma ~ products[0]:', products[0])

    await this.client.deleteIndex('products')
    await this.client.createIndex('products', { primaryKey: 'prod_id' })

    // Добавляем поля для сортировки по категории
    sortableAndFilterableFields.push('category.cat_search_sort')
    sortableAndFilterableFields.push('cat_search_sort')


    await this.client.index('products').updateSettings({
      filterableAttributes: sortableAndFilterableFields,
      sortableAttributes: sortableAndFilterableFields,
      searchableAttributes: [
        'prod_sku',
        'prod_analogsku',
        'prod_analogs',
        'skuTokens',
        'prod_purpose',
        'prod_type',
        'prod_material',
        'prod_uses',
        'prod_note',
        'prod_manuf',
        'prod_year',
        'prod_size',
        'prod_model',
        'category.cat_title'
      ],
      pagination: {
        maxTotalHits: 200000
      },
      typoTolerance: {
        disableOnAttributes: ['skuTokens'],
        enabled: true,
        minWordSizeForTypos: {
          oneTypo: 5,
          twoTypos: 9
        },
        disableOnNumbers: true
      }
    })

    await this.updateRankingRules('products')

    const res = await this.client
      .index('products')
      .addDocuments(meiliProducts)
      .then((res) => console.log('Products synced:', res))

    //console.timeEnd('syncProductsByPrisma')

    return { res, product0: meiliProducts[0] }
  }

  async syncAllByPrisma() {
    await Promise.all([this.syncProductsByPrisma(), this.syncCategoriesByPrisma(), this.syncColumnsByPrisma(), this.syncFiltersByPrisma()])
  }

  async updateProduct({ product, isAdonisModel = false }: { product: Product | Record<string, unknown>; isAdonisModel?: boolean }) {
    try {
      const productJson: Record<string, any> = isAdonisModel ? (product as Product).toJSON() : { ...product }
      const sizeFields = ['size_in', 'size_in_2', 'size_out', 'size_out_2', 'size_h', 'size_h_2']

      // Обработка размеров
      for (const field of sizeFields) {
        if (productJson[field]) {
          productJson[field] = Number(productJson[field])
        }
      }

      // Добавляем флаг наличия
      productJson.inStock = productJson.prod_count > 0

      // Создаем массив артикулов для генерации токенов
      const skuArray = [productJson.prod_sku, productJson.prod_analogsku, ...(productJson.prod_analogs ? productJson.prod_analogs.split(',') : [])].filter(Boolean) // Удаляем пустые значения

      productJson.skuTokens = this.generateSkuTokens(skuArray as string[])

      // Получаем информацию о категории для добавления cat_search_sort
      if (productJson.prod_cat) {
        const category = await $prisma.cats.findFirst({
          where: { cat_id: Number(productJson.prod_cat) },
          select: { cat_id: true, cat_title: true, cat_search_sort: true }
        })

        if (category) {
          // Принудительно преобразуем cat_search_sort в число
          const cat_search_sort = Number(category.cat_search_sort) || 0

          productJson.category = {
            cat_id: category.cat_id,
            cat_title: category.cat_title,
            cat_search_sort: cat_search_sort
          }

          // Добавляем поле напрямую в корень товара для удобства сортировки
          productJson.cat_search_sort = cat_search_sort
        }
      }

      // Проверяем, есть ли у товара группа
      if (productJson.prod_group) {
        // Получаем все товары с такой же группой
        const groupProducts = await $prisma.products.findMany({
          where: { prod_group: String(productJson.prod_group) }
        })

        // Получаем уникальные ID категорий для всех товаров в группе
        const categoryIds = [...new Set(groupProducts.map((p) => Number(p.prod_cat)).filter(Boolean))]

        // Получаем данные категорий одним запросом
        const categoriesData = await $prisma.cats.findMany({
          where: { cat_id: { in: categoryIds } },
          select: { cat_id: true, cat_title: true, cat_search_sort: true }
        })
        const categoriesMap = new Map(categoriesData.map((c) => [c.cat_id, c]))

        // Синхронизируем поля prod_count и prod_group_count для всех товаров в группе
        const documentsToUpdate = groupProducts.map((groupProduct) => {
          const enrichedProduct: any = { ...groupProduct }
          // Копируем значения полей из обновляемого товара
          enrichedProduct.prod_count = Number(productJson.prod_count)
          enrichedProduct.prod_group_count = Number(productJson.prod_count)

          // Устанавливаем флаг наличия
          enrichedProduct.inStock = enrichedProduct.prod_count > 0

          // Генерируем токены
          const skuArray = [enrichedProduct.prod_sku, enrichedProduct.prod_analogsku, ...(enrichedProduct.prod_analogs ? enrichedProduct.prod_analogs.split(',') : [])].filter(
            Boolean
          )
          enrichedProduct.skuTokens = this.generateSkuTokens(skuArray as string[])

          // Добавляем данные категории
          const category = categoriesMap.get(Number(enrichedProduct.prod_cat))
          if (category) {
            const cat_search_sort = Number(category.cat_search_sort) || 0
            enrichedProduct.category = {
              cat_id: category.cat_id,
              cat_title: category.cat_title,
              cat_search_sort
            }
            enrichedProduct.cat_search_sort = cat_search_sort
          }

          for (const field of sizeFields) {
            if (enrichedProduct[field]) {
              enrichedProduct[field] = Number(enrichedProduct[field])
            }
          }

          return enrichedProduct
        })

        // Обновляем все товары группы в MeiliSearch
        await this.client.index('products').updateDocuments(documentsToUpdate)
        console.log(`Обновлено ${documentsToUpdate.length} товаров в группе ${productJson.prod_group}`)

        return true
      }
      // Если товар не входит в группу, обновляем только его
      await this.client.index('products').updateDocuments([productJson])
      return true
    } catch (error) {
      console.error('MeiliSearchPlugin ~ updateProduct error:', error)
      return false
    }
  }

  /**
   * Удаляет товар из индекса MeiliSearch
   * @param productId ID товара для удаления
   * @returns true в случае успеха, false в случае ошибки
   */
  async deleteProduct(productId: string | number) {
    try {
      console.log(`Удаление товара с ID ${productId} из MeiliSearch`)
      await this.client.index('products').deleteDocument(String(productId))
      return true
    } catch (error) {
      console.error(`MeiliSearchPlugin ~ deleteProduct error для ID ${productId}:`, error)
      return false
    }
  }
}

export const meiliDB = new MeiliSearchPlugin({})
