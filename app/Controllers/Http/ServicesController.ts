import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import LangDict from 'App/Models/LangDict'
//import { EbayInt } from './../../Plugins/dynamics/EbayApi';
import { parseXLS } from 'App/Helpers/parseXLS'

import Database from '@ioc:Adonis/Lucid/Database'
import Filter from 'App/Models/Filter'
import Cart from 'App/Models/Cart'
import CartItem from 'App/Models/CartItem'
import { mergeObjects } from 'App/Helpers/mergeObject'
import Category from 'App/Models/Category'
import Org from 'App/Models/Org'
import Product from 'App/Models/Product'
import Mail from '@ioc:Adonis/Addons/Mail'
import Statistic from 'App/Models/Statistic'
import Env from '@ioc:Adonis/Core/Env'
import ClearSnapshots from 'App/Plugins/ClearSnapshots'
import { SiteMap } from 'App/Plugins/SiteMap'
import { YandexTurbo } from 'App/Plugins/YandexTurbo'
import { Emex } from 'App/Plugins/Emex'
import { Stripe } from 'App/Plugins/dynamic/Stripe'
import loadSettings from 'App/Helpers/loadSettings'
import { RumisotaShipping } from 'App/Plugins/dynamic/RumisotaShipping'
import { i18n } from '../../../start/i18n'
import i18next from 'i18next'
import { Document, OfferDocument, OrderDocument } from 'App/Plugins/Documents'
import { Files } from 'App/Helpers/files'
import { PochtaRU } from 'App/Plugins/PochtaRU'
import { getModelColumns } from 'App/Helpers/getModelColumns'
import { AppSetting } from 'App/Helpers/settingsProvider'
import { DateTime } from 'luxon'

import Ws from 'App/Services/Ws'
import { AsyncLocalStorage } from 'async_hooks'
import { PriceList } from 'App/Plugins/Pricelist'
import Order from 'App/Models/Order'
import { getCountryCode } from 'App/Helpers/getCountryCode'
import User from 'App/Models/User'
import { Cdek } from 'App/Plugins/Cdek'
import * as fs from 'fs'
import * as path from 'path'
import { exec } from 'child_process'

const got = require('got')
const DADATA_TOKEN = Env.get('DADATA_TOKEN')
const EMAIL_FROM = Env.get('EMAIL_FROM')

// const { PassThrough, Writable } = require('stream')
const XLSX = require('xlsx')

const storage = { cnt: 0 }

class Dadata {
  headers: object

  constructor() {
    this.headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': 'Token ' + DADATA_TOKEN
    }
  }
}

class Suggestions extends Dadata {
  url: string
  methodurl: string

  constructor(methodurl = 'suggest') {
    super()
    this.methodurl = methodurl
    this.url = `https://suggestions.dadata.ru/suggestions/api/4_1/rs/${this.methodurl}/`
  }

  async find(method, body, options?) {
    if (body.query) body.query = decodeURIComponent(decodeURIComponent(body.query))

    try {
      const res = await got.post(this.url + method, {
        headers: this.headers,
        json: body
      })

      let { suggestions } = JSON.parse(res.body)

      if (method === 'address') {
        return suggestions.map((item) => ({
          location: options && options.withType ? item.data.street_with_type : item.value,
          index: item.data.postal_code,
          city_fias_id: item.data.city_fias_id,
          settlement_fias_id: item.data.settlement_fias_id,
          street_with_type: item.data.street_with_type
        }))
      } else {
        return suggestions
      }
    } catch (error) {
      console.error('dadata error::', error)
      return []
    }
  }
}

class AppInit {
  params
  request
  response
  auth

  user
  miniCart
  rootCategories
  childCategories

  constructor({ params, request, response, auth }) {
    this.auth = auth
    this.request = request
    this.response = response
    this.params = params
  }

  async getClientSettings(locale) {
    /*         if (locale && locale != 'ru') {
            const res = await loadSettings()
            return res
        } else {
            return {}
        } */
    const res = await loadSettings([
      'currency',
      // 'stripekeyclient',
      // 'paypalkeyclient',
      'DISCOUNT_START_SUM',
      'google_analytics_key',
      'rumisota.retail',
      'google_analytics_key_rti',
      'pochta.standard.maxValue',
      'copyaccess_users',
      'seo_templates'
    ])
    return res
  }

  async getUser() {
    try {
      //console.log('start get user: ');

      let sessionUser
      try {
        sessionUser = await this.auth.authenticate()
      } catch (error) {}

      if (sessionUser) {
        const org = await Org.query().where('org_client', sessionUser.client_number).first()
        if (org) {
          sessionUser.org = org
        }

        return (this.user = sessionUser)
      }
    } catch (error) {
      console.error('clientAppInit.getUser: ', error)
    }
  }

  async getMiniCart(locale) {
    try {
      const _cart = new Cart()
      const _cartItem = new CartItem()

      const page: number = this.params.page || 1
      const searchvalue: string = this.request.requestQs.searchvalue || ''
      const _limit = 30

      _cart.checkCookie({ response: this.response, request: this.request })

      const { idField, cartId } = await _cart.getCartId(this.request, this.response, this.auth)
      let { extandedSearchFields, cartListProductDBfields /* , limit */ } = Product.getParams(this.request)

      const cartPaginator = await CartItem.query()
        .leftOuterJoin('products', 'products.prod_id', 'cart_items.prod_id')
        .select('cart_items.*')
        .where(idField, cartId)
        .andWhere((builder) => {
          if (searchvalue) {
            builder.whereRaw(`CONCAT(${extandedSearchFields.join(',')}) LIKE ?`, [`%${searchvalue}%`])
          }
        })
        .preload('product', (query) => query.select(cartListProductDBfields))
        .orderBy('updated_at', 'desc')
        .orderBy('id', 'asc')
        .paginate(page, _limit)

      const cartsum = await _cart.getCartSum(idField, cartId)

      let { meta, data } = cartPaginator.toJSON()

      if (locale != 'ru') {
        await LangDict.prodsTranslate(data, locale)
      }

      meta['cartsum'] = cartsum.sum
      meta['discountStartSum'] = _cart.getDiscountStartSum()
      data = _cartItem.serializeCart(data)

      return (this.miniCart = { meta, data })
    } catch (error) {
      console.error('clientAppInit.getMiniCart: ', error)
    }
  }

  async getAllCategories(locale) {
    try {
      const _category = new Category()
      const allCategories = await _category.getCategories({
        // isNotDuplicate: true
      })
      //console.log('allCategories', allCategories)

      /*             await Promise.all(allCategories.map(async category => {
                category.fullurl = await Category.getFullUrl(category)
            })) */

      allCategories.map((category) => {
        category.$attributes.cat_base64_pic = category.$extras.cat_base64_pic
        category.subcategories = allCategories.filter((i) => i.cat_rootcat == category.cat_id)
      })

      /*             if (locale) {
                //console.log('getAllCategories locale: ', locale)

                allCategories.map(category => {
                    if (category.$extras['cat_url_' + locale]) {
                        category.cat_url = category.$extras['cat_url_' + locale]
                    }
                })

                await LangDict.categoriesTranslate(allCategories, locale)
            } */

      this.rootCategories = allCategories.filter((i) => i.cat_rootcat == 0)
      this.childCategories = allCategories.filter((i) => i.cat_rootcat != 0)

      return { rootCategories: this.rootCategories, childCategories: this.childCategories }
    } catch (error) {
      console.error('clientAppInit.getAllCategories: ', error)
    }
  }
}

export default class ServicesController {
  public async clientAppInit({ params, request, response, auth }: HttpContextContract) {
    const clientToken = request.headers()['x-token'] || undefined

    const locale = request.headers()['x-locale'] || undefined
    const appInit = new AppInit({ params, request, response, auth })
    const user = await appInit.getUser()

    const [settings, allCategories] = await Promise.all([appInit.getClientSettings(locale), appInit.getAllCategories(locale)])

    return { user, allCategories, locale, settings }
  }

  public async filtersPreData({ request, params }) {
    const { categoryid, limit } = params

    if (typeof categoryid == 'undefined') {
      return []
    }

    const res = await Filter.getPreData(categoryid, limit)
    return res
  }

  public async filtersdata({ request }) {
    const _filter = new Filter()
    const res = await _filter.getData(request)

    return res || []
  }

  public async findlocation({ request }) {
    const suggestions = new Suggestions()
    const { query } = request.requestBody

    const body = {
      query,
      from_bound: { value: 'city' },
      to_bound: { value: 'settlement' }
      //restrict_value: true
    }

    return await suggestions.find('address', body)
  }

  public async findstreet({ request }) {
    const { query, location, city_fias_id, settlement_fias_id } = request.requestBody

    let _location: string = ''

    if (location) {
      try {
        _location = location.split(' ')
        _location = _location[_location.length - 1]
      } catch (error) {
        _location = location
      }
    }

    const suggestions = new Suggestions()

    const body = {
      query,
      locations: [city_fias_id ? { city_fias_id } : settlement_fias_id ? { settlement_fias_id } : { city: _location }, { settlement: _location }],
      from_bound: { value: 'street' },
      to_bound: { value: 'street' },
      restrict_value: true
    }

    return await suggestions.find('address', body, { withType: true })
  }

  public async findbank({ params }) {
    const suggestions = new Suggestions()
    const { query } = params

    return await suggestions.find('bank', { query })
  }

  public async findorg({ params }) {
    const suggestions = new Suggestions()
    const { query } = params

    return await suggestions.find('party', { query })
  }

  public async findorgbyinn({ params }) {
    const suggestions = new Suggestions('findById')
    const { query } = params

    return await suggestions.find('party', { query })
  }

  public async iplocate({ request }) {
    const dadata = new Dadata()

    const url = 'https://suggestions.dadata.ru/suggestions/api/4_1/rs/iplocate/address?ip='
    const query = request.ip()

    //console.log('request: ', request.ip())

    try {
      const res = await got(url + query, { headers: dadata.headers })
      let parsedData = JSON.parse(res.body)
      return {
        location: parsedData.location.value,
        country: parsedData.location.data.country
      }
    } catch (error) {
      //console.error('dadata error::', error.response.body)
      return []
    }
  }

  //getcountries/en

  public async countries({ request, params }) {
    const { locale = 'ru' } = params
    const [countries] = await Database.knexRawQuery('SELECT country_id, iso, ?? as title FROM geodata._countries ORDER BY country_id ASC', ['title_' + locale])

    return countries
  }

  public async cpanCalcaluteShipping({ params, request, response, auth }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined
    let { type = 'standard', country = 643, destinationIndex = 101000, orderId } = request.all()

    if (isNaN(Number(country))) {
      country = getCountryCode(country)
    }

    // console.log('🚀 ~ file: ServicesController.ts:372 ~ ServicesController ~ cpanCalcaluteShipping ~ country:', country)

    if (!orderId) {
      throw new Error('orderId is undefined')
    }

    const _pochta = new PochtaRU()

    const orderPriceMeta = await Order.getOrderPrice(orderId)
    const orderWeight = await Order.getWeight(orderId)

    let price

    try {
      price = await (await _pochta.calculate(country, destinationIndex, orderPriceMeta.sum))[type](orderWeight)
      return price
    } catch (error) {
      price = await _pochta.manualCalc({ orderprice: orderPriceMeta.sum, type, countryID: country })
      console.error('pochta calc error, start manaul calc...::msg::')
      return { price }
    }
  }

  public async getCartWeight({ params, request, response, auth }: HttpContextContract) {
    const _cart = new Cart()

    const { idField, cartId } = await _cart.getCartId(request, response, auth)
    let weight: number = await _cart.getCartWeight(idField, cartId)

    return weight || 1000
  }
  public async calcaluteShipping({ params, request, response, auth }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined
    //console.log('calcaluteShipping locale: ', locale)

    const { type = 'standard', country = 643, destinationIndex = 101000 } = params

    const { city, address } = request.all()

    const _pochta = new PochtaRU()
    const _cart = new Cart()

    const cdek = new Cdek()

    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    if (locale && locale != 'ru') {
      const euShipping = new RumisotaShipping()
      let price = await euShipping.calcByPrice({ countryID: Number(country), idField, cartId })

      if (isNaN(price)) {
        console.log('Rumisota calcaluteShipping: isNaN')
        return 911
      }

      return price
    }

    let orderprice = await _cart.getCartSum(idField, cartId)
    let weight: number = await _cart.getCartWeight(idField, cartId)
    let price

    if (type == 'cdek') {
      // city
      // address

      try {
        const cdekres = await cdek.calculate({
          address,
          city,
          country_code: 'RU',
          postal_code: destinationIndex,
          weight
        })

        return cdekres?.total_sum || 901
      } catch (error) {
        price = await _pochta.manualCalc({ orderprice: orderprice.sum, type, countryID: country })
        console.error('CDEK calc error, start manaul calc...::msg::')
        return price
      }
    }

    try {
      price = await (await _pochta.calculate(country, destinationIndex, orderprice.sum))[type](weight)
      return price
    } catch (error) {
      price = await _pochta.manualCalc({ orderprice: orderprice.sum, type, countryID: country })
      console.error('pochta calc error, start manaul calc...::msg::')
      return price
    }

    price = await _pochta.manualCalc({ orderprice: orderprice.sum, type, countryID: country })

    if (type == 'standard' && price < 200) {
      price = 250
    }

    if (type == 'express' && price < 800) {
      price = 850
    }

    return price
  }

  public async parseCartFromExcelFile({ response, request, auth }) {
    const file = request.file('file', {
      size: '5mb',
      extnames: ['cfb', 'xls', 'xlsx', 'csv']
    })

    if (!file) {
      return 'Please upload file'
    }
    if (file.hasErrors) {
      return file.errors
    }

    const _cart = new Cart()
    const uparams = request.all()
    const products = await parseXLS({ file, uparams })
    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    const payloadItems = products.map((product) => {
      return {
        cart_id: cartId,
        prod_id: product.prod_id,
        qty: product.qty
      }
    })

    const merged = mergeObjects(payloadItems, 'prod_id', 'qty')

    //const trx = await Database.transaction()
    // add transaction

    await CartItem.query().where(idField, cartId).delete()
    await CartItem.createMany(merged)

    return { msg: 'ok' }
  }

  public async preOrder({ request, response, params, auth }) {
    const form = request.all()

    if (form.qty) {
      try {
        await Statistic.write({
          request,
          query: form.product.prod_analogsku,
          info: `${form.phone} / ${form.email}`,
          count: form.qty,
          manual: 1
        })

        if (form.email && Number(form.qty) >= 100) {
          let _msg = `IP: ${request.ip()}, <b>${form.phone}</b> / ${form.email}, оформил предзаказ на: <b>${form.product.prod_analogsku}</b> (${
            form.product.prod_sku
          }) в кол-ве: <b>${form.qty}</b>`
          Mail.send((message) => {
            message
              .from(Env.get('EMAIL_FROM'))
              .to(Env.get('EMAIL_FROM'))
              .subject('ПРЕДЗАКАЗ: ' + form.product.prod_analogsku + ' x ' + form.qty)
              .html(_msg)
          })
        }
      } catch (error) {
        console.warn('ERROR -> writeQ statistics: ', error)
      }
    }

    return { msg: 'Заявка отправлена' }
  }

  public async updateSubscribe({ request, response, params, auth }) {
    const { email } = params

    if (email) {
      Mail.send((message) => {
        message
          .from(EMAIL_FROM)
          .to(EMAIL_FROM)
          .subject('Подписка на рассылку: ' + email)
          .html(`${email}, подписался на рассылку о пополнении склада`)
      })
    }

    return []
  }

  public async clearSnapshots({ response, request, auth }) {
    await ClearSnapshots.init()
  }

  public async clearEmptyCarts({ response, request, auth }: HttpContextContract) {
    let { limit = 1000, mode = 'slow', iters = 5 } = request.qs()

    const init = async () => {
      const carts = await Cart.query().limit(limit).orderBy('cart_id', 'desc')

      const removeIds: number[] = []

      response.response.end('job start')

      const _proccess = async (cart: Cart) => {
        const cartItems = await CartItem.query().count('*', 'cnt').where('cart_id', cart.cart_id).first()
        if (cartItems?.$extras.cnt == 0) {
          removeIds.push(cart.cart_id)
        }
      }

      let i = 0

      if (mode == 'slow') {
        for (const cart of carts) {
          console.log('proccessed: ', `${i++} of ${carts.length}`)
          await _proccess(cart)
        }
      } else {
        console.log('start process in fast mode')
        await Promise.all(
          carts.map(async (cart) => {
            await _proccess(cart)
          })
        )
      }

      console.log('start remove length: ', removeIds.length)
      await Cart.query().del().whereIn('cart_id', removeIds)
      console.log('del success!')

      return 's'
    }

    let _iters = Array.from(Array(Number(iters)).keys())

    let gi = 0
    for (const item of _iters) {
      console.log('iter: ', `${gi++} of ${iters}`)
      await init()
    }
  }

  public async siteMap({ response, request, auth, params }: HttpContextContract) {
    console.log('params', params)
    const sitemap = new SiteMap()
    const { type = 'yandex', locale = 'ru' } = request.qs()

    const _map = type == 'yandex' ? await sitemap.make(params.page, params.limit || 10000) : await sitemap.makeGoogle(params.page, params.limit || 10000, locale)

    response.header('Content-type', 'application/xml')
    response.type('application/xml')

    response.send(_map)
  }

  public async yml({ response, request, auth, params }: HttpContextContract) {
    console.log('params', params)
    const yandexTurbo = new YandexTurbo()

    const yml = await yandexTurbo.makeYml(params.page, params.limit || 10)

    response.header('Content-type', 'application/xml')
    response.type('application/xml')

    response.send(yml)
  }

  public async emexPrice({ response, request, auth, params }) {
    const { limit } = request.requestQs
    const emex = new Emex()
    const date = new Date()
    const buffer = await emex.make(limit)

    const filename = `Emex_price_${date.toLocaleDateString()}.xlsx`

    response.header('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response.header('Content-Disposition', 'attachment; filename=' + filename)

    response.send(Buffer.from(buffer, 'base64'))
  }

  public async ebayListing({ response, request, auth, params }) {
    /*         const ebayApi = new EbayInt()

        //await ebayApi.addFixedPrice(1)
        await ebayApi.sendDrafts(1) */
  }

  public async createPaymentIntent({ response, request, auth, params }) {
    const { amount, email } = request.all()
    const res = await Stripe.paymentIntent({ amount, currency: 'eur', email })

    return {
      client_secret: res.client_secret
    }
  }

  public async pricelist({ response, request, auth, params }) {
    // const { limit } = request.requestQs
    const emex = new PriceList()
    const date = new Date()
    const buffer = await emex.make()

    const filename = `mirsalnikov_pricelist_${date.toLocaleDateString()}.xlsx`

    response.header('Content-type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
    response.header('Content-Disposition', 'attachment; filename=' + filename)

    response.send(Buffer.from(buffer, 'base64'))
  }

  public async sendMail({ response, request, auth, params }: HttpContextContract) {
    let to = '',
      subject = '',
      _message = ''

    if (request.method() == 'GET') {
      to = params.to
      subject = params.subject
      _message = params.message
    } else {
      let body = request.all()

      to = body.to
      subject = body.subject
      _message = body.message
    }

    console.log({ to, _message, subject })

    try {
      const res = await Mail.send((message) => {
        message
          .from(Env.get('EMAIL_FROM'), Env.get('EMAIL_FROM_NAME') )
          .to(to)
          .bcc(Env.get('EMAIL_FROM'))
          .subject(`${Env.get('EMAIL_FROM_NAME')}: ${subject}`)
          .html(_message)
        //TODO:
        // message.from(Env.get('EMAIL_FROM')).to('<EMAIL>').subject('из ап: ' + subject).html(_message)
      })

      console.log('res:', res)

      return response.status(200).send({ ok: true })
    } catch (error) {
      console.error(error)
      return response.status(500).send({ error })
    }
  }

  public async testr({ response, request, auth, params }) {
    const locale = request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    if (isRumisota) {
      i18next.changeLanguage(locale)
    }

    return i18next.t('Данные высланы на вашу электронную почту')

    /*         const t = i18n.getFixedT('en')

        return t('Тексттест', {
            name: 'testname'
        }) */
  }

  public async documents({ response, request, auth, params }: HttpContextContract) {
    let { orderId, documentName = 'test', locale = 'ru' } = request.qs()

    documentName = decodeURIComponent(String(documentName))

    const arn = documentName.split('.')
    const type = arn[arn.length - 1]

    const contentTypes = {
      docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
    }

    if (orderId) {
      let document = new OrderDocument({ orderId, type })

      await document.init(locale)
      let fileBuffer = await document.makeFile(documentName)

      response.header('Content-disposition', 'attachment; filename=' + (orderId + '_' + documentName))
      response.header('Content-Type', contentTypes[type] || 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

      response.send(Buffer.from(fileBuffer, 'binary'))
    }
  }

  // Функции для конвертации XLSX в PDF
  private async killSofficeProcesses(): Promise<void> {
    return new Promise((resolve, reject) => {
      exec('killall -9 soffice.bin', (error, stdout, stderr) => {
        if (error) {
          // Игнорируем ошибку, если процессов для завершения не найдено
          if (stderr.includes('no process found')) {
            return resolve()
          }
          return reject(error)
        }
        resolve()
      })
    })
  }

  private async convertXlsxToPdf(inputPath: string, outputDir: string): Promise<void> {
    const command = `soffice --norestore --headless --convert-to pdf "${inputPath}" --outdir "${outputDir}"`

    return new Promise(async (resolve, reject) => {
      try {
        await this.killSofficeProcesses()
      } catch (error) {
        // Игнорируем ошибки завершения процессов
      }

      exec(command, async (error, stdout, stderr) => {
        if (error && !stderr.includes('javaldx')) {
          reject(error)
        } else {
          try {
            await this.killSofficeProcesses()
          } catch (error) {
            // Игнорируем ошибки завершения процессов
          }
          resolve()
        }
      })
    })
  }

  public async offerDocument({ response, request, auth, params }: HttpContextContract) {
    let {
      client,
      WhosaleMode,
      filename,
      org,
      productData,
      shippingIndex = 101000,
      shippingType = 'express',
      countryId = 643,
      offerExp = 7,
      prodCategory = undefined,
      prodFilters = undefined,
      aQty = 1,
      inStock = undefined,
      shippingPrice,
      fileResponse = false
    } = request.all()

    if (typeof productData == 'string') {
      try {
        productData = JSON.parse(productData.trim())
      } catch (error) {
        console.log('error: ', error)
      }
    }

    if (prodCategory || prodFilters) {
      const _data = await Database.from('products')
        .select('prod_id')
        .if(prodCategory, (query) => {
          query.whereIn('prod_cat', prodCategory)
        })
        .if(prodFilters?.length, (query) => {
          query.andWhere((sq) => {
            prodFilters.map((filter) => sq.andWhere(filter.key, filter.value))
          })
        })
        .if(inStock, (q) => q.andWhere('prod_count', '>', 0))

      productData = _data.map((prod) => ({ id: prod.prod_id, qty: aQty }))
    }

    console.log('prodCategory || prodFilters:', { prodCategory, prodFilters })

    // write stats
    if (!prodCategory) {
      try {
        const _products = await Product.findMany(productData.map((i) => i.id))

        await Promise.all(
          _products.map(async (product) => {
            await Statistic.write({
              query: product.prod_analogsku,
              count: productData.find((x) => x.id == product.prod_id)?.qty || 0,
              request,
              manual: 1,
              info: 'Из коммерческого'
            })
          })
        )
      } catch (error) {
        console.error('Write statistic: ', error)
      }
    }

    let offerDocument = new OfferDocument({
      client,
      WhosaleMode,
      filename,
      org,
      productData,
      shippingIndex,
      shippingType,
      shippingPrice,
      countryId,
      offerExp
    })

    await offerDocument.init()

    let fileBuffer = await offerDocument.makeOfferFile()

    // Если нужно вернуть ссылку на файл
    if (fileResponse) {
      const { docs_file_url_prefix = '/docs' } = await loadSettings(['docs_file_url_prefix'])
      // Определяем путь для сохранения файла
      const uploadPath = path.resolve(Env.get('UPLOAD_PATH'))
      const tempDocsFolder = '_tempdocs'
      const docsPath = path.join(uploadPath, tempDocsFolder)

      // Создаем папку если её нет
      if (!fs.existsSync(docsPath)) {
        fs.mkdirSync(docsPath, { recursive: true })
      }

      // Генерируем уникальное имя файла с временной меткой
      const timestamp = Date.now()
      const baseFileName = `offer_${timestamp}_${filename.replace(/\.[^/.]+$/, '')}`

      // Пути для XLSX и PDF файлов
      const xlsxFileName = `${baseFileName}.xlsx`
      const pdfFileName = `${baseFileName}.pdf`
      const xlsxFilePath = path.join(docsPath, xlsxFileName)

      // Сохраняем XLSX файл
      fs.writeFileSync(xlsxFilePath, Buffer.from(fileBuffer))

      // Конвертируем в PDF
      let pdfUrl: string | null = null
      try {
        // LibreOffice создает PDF с тем же именем что и XLSX в указанной папке
        await this.convertXlsxToPdf(xlsxFilePath, docsPath)

        // Проверяем что PDF файл создался
        const generatedPdfPath = path.join(docsPath, `${baseFileName}.pdf`)
        if (fs.existsSync(generatedPdfPath)) {
          pdfUrl = `${docs_file_url_prefix}/${tempDocsFolder}/${pdfFileName}`
        }
      } catch (error) {
        console.error('Ошибка конвертации в PDF:', error)
      }

      // Формируем URLs для доступа к файлам
      const xlsxUrl = `${docs_file_url_prefix}/${tempDocsFolder}/${xlsxFileName}`

      return {
        success: true,
        files: {
          xlsx: {
            url: xlsxUrl,
            fileName: xlsxFileName
          },
          pdf: pdfUrl ? {
            url: pdfUrl,
            fileName: pdfFileName
          } : null
        },
        message: pdfUrl ? 'Файлы успешно созданы' : 'XLSX файл создан, ошибка создания PDF'
      }
    }

    // Стандартное поведение - отправка файла как attachment
    response.header('Content-disposition', 'attachment; filename=res_' + filename)
    response.header('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')

    response.send(fileBuffer)

    return []
  }

  public async getDocumentsList({ response, request, auth, params }: HttpContextContract) {
    const _files = new Files()
    const fileList = await _files.getFiles('/docs')

    return fileList
  }

  public async getUsers({ response, request, auth, params }: HttpContextContract) {
    const sessionUser = await auth.use('api').authenticate()

    if (sessionUser.user_role != 'su') {
      return response.status(401)
    }

    const res = await User.query()

    return res
  }

  public async updateUser({ response, request, auth, params }: HttpContextContract) {
    const sessionUser = await auth.use('api').authenticate()

    if (sessionUser.user_role != 'su') {
      return response.status(401)
    }

    const { payload } = request.all()
    const user = await User.findOrFail(payload.user_id)

    if (!payload.password) {
      delete payload.password
    }

    user.merge(payload)

    return await user.save()
    return
  }

  public async cpanInit({ response, request, auth, params }: HttpContextContract) {
    const { metaData } = request.all()

    const { productDBfields } = Product.getProductColumns()
    //const productFields = getModelColumns(Product)
    const Settings = new AppSetting()
    const app = new AppInit({ params })

    const [settings, categories] = await Promise.all([Settings.getJSON({ all: true }), app.getAllCategories('ru')])

    return {
      productDBfields: productDBfields.map((i) => i.split('.')[1]),
      settings,
      categories
    }
  }

  //

  public async productBulkUploadList({ response, request, auth, params }: HttpContextContract) {
    let { handlers, products, fields, categoryId, mergeMode, mergeFields, compareFields } = request.all()
    //const xlsxFile = request.file('ufile')
    //const workbook = XLSX.readFile('test.xlsx')

    const wsRoom = 'bulkupload_' /* + sessionUser.id */

    // return false

    if (!products || !Array.isArray(products)) {
      response.status(500).send('products must be array')

      Ws.io.in(wsRoom).emit('bulkuploaderror', {
        index: '',
        product: '',
        error: 'Товары должны быть массивом'
      })
    }

    if (!fields) {
      response.status(500).send('fields must be object')

      Ws.io.in(wsRoom).emit('bulkuploaderror', {
        index: '',
        product: '',
        error: 'Не указаны поля'
      })
    }

    if (!compareFields || !Array.isArray(compareFields)) {
      compareFields = ['prod_analogsku']
    }

    if (mergeMode == 'merge' && (!mergeFields || !mergeFields?.length)) {
      Ws.io.in(wsRoom).emit('bulkuploaderror', {
        index: '',
        product: '',
        error: 'Не указаны поля слияния'
      })
    }

    let data: Array<Product> = []

    try {
      products.map((product) => {
        let dataItem: Product = {}

        for (let key in product) {
          if (typeof fields[key] != 'undefined' && fields[key] !== 'undefined') {
            dataItem[fields[key]] = product[key]
          }
        }

        //dataItem['prod_cat'] = categoryId || 911
        // dataItem.prod_composition = dataItem.prod_composition + ', bulkupload: ' + DateTime.now().setLocale('ru').toLocaleString(DateTime.DATETIME_MED)

        data.push(dataItem)
      })
    } catch (error) {
      console.error(error)
    }

    response.status(200).send('start')

    const _proccess = async () => {
      let i = 0
      let createdI = 0
      let updatedI = 0

      for (let item of data) {
        let _compareObj = {}

        compareFields.map((f) => (_compareObj[f] = item[f]))
        try {
          const foundedProducts = await Product.query().where(_compareObj)

          await Promise.all(
            foundedProducts.map(async (fproduct) => {
              if (mergeMode == 'income') {
                let qtyField = ''

                for (const key in fields) {
                  if (fields[key]) {
                    qtyField = fields[key]
                  }
                }

                fproduct.prod_count = Number(fproduct.prod_count) + Number(item.prod_count)

                if (isNaN(fproduct.prod_count)) {
                  throw new Error('qty is NaN')
                }
              }

              if (mergeMode == 'mergeReplace') {
                fproduct.merge(item)
              }

              if (mergeMode == 'merge') {
                mergeFields.map((mfield) => {
                  if (!fproduct[mfield]) {
                    fproduct[mfield] = item[mfield]
                  } else {
                    fproduct[mfield] = fproduct[mfield] + ', ' + item[mfield]

                    if (mfield == 'prod_analogs') {
                      try {
                        fproduct[mfield] = [...new Set(fproduct[mfield].split(',').filter((x) => x))].join(',')
                      } catch (error) {
                        Ws.io.in(wsRoom).emit('bulkuploaderror', {
                          index: item.prod_analogsku || item.prod_sku,
                          product: item,
                          error: 'Ошибка обработки аналогов - ' + error.message
                        })
                      }
                    }
                  }
                })

                Object.keys(item).map((key) => {
                  if (!fproduct[key] && !mergeFields.includes(key)) {
                    fproduct[key] = item[key]
                  }
                })
              }

              if (mergeMode != 'skip') {
                await fproduct.save()
                updatedI++
              }

              Ws.io.in(wsRoom).emit('bulkuploadres', { index: i++, length: data.length, updated: updatedI, created: createdI })
            })
          )

          if (!foundedProducts || (!foundedProducts.length && mergeMode != 'income')) {
            const _product = new Product()
            _product.merge(item)
            await _product.save()

            createdI++
          }

          // Ws.io.in(wsRoom).emit('bulkuploadres', {index: i++, length: data.length, updated: updatedI, created: createdI})
        } catch (error) {
          console.log('productBulkUploadList ~ error', error)

          Ws.io.in(wsRoom).emit('bulkuploaderror', {
            index: item.prod_analogsku || item.prod_sku,
            product: item,
            error: error.message
          })
        }
      }

      Ws.io.in(wsRoom).emit('bulkupload_success', { updated: updatedI, created: createdI })
    }

    _proccess()

    //console.log('data: ', data)
    //return { success: true }
  }
}
