import { textSearchTranslateResults } from 'App/Interfaces/locales/index'

import LangDict from 'App/Models/LangDict'

import Filter from 'App/Models/Filter'
import Product from 'App/Models/Product'
import Category from 'App/Models/Category'
import Cart from 'App/Models/Cart'

// import { buildBySizeQuery }     from 'App/Helpers/buildBySizeQuery'
import { parseExtandedQueries } from 'App/Helpers/parseExtandedQueries'
import { searchValueHandler, transliterate } from 'App/Helpers/searchValueHandler'
import { setProdCartQty } from 'App/Helpers/setProdCartQty'
import Database from '@ioc:Adonis/Lucid/Database'
import { rebuildBySizeQuery } from 'App/Helpers/rebuildBySizeQuery'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

// import Database from '@ioc:Adonis/Lucid/Database'

function getSizeValue(value) {
  let _sizevalue = value.replace(/\x|х|X|Х|на|НА|-|:/gm, '*')
  _sizevalue = _sizevalue.replace(/\,{1,}/gm, '.')

  return _sizevalue
}

export default class SearchesController {
  public async fast({ request, params, response, auth }) {
    const locale = request.headers()['x-locale'] || undefined

    let value: string = decodeURIComponent(decodeURIComponent(params.value))
    let translatedValues: Array<textSearchTranslateResults> = []
    // lang-layout-switcher

    const { clientProductDBfields, skuFields } = Product.getProductColumns()
    const { orderByStockQuery } = Product.orderByStock()

    value = value.trim()

    if (value.length < 2) {
      return []
    }

    let translitValue = transliterate(value) || ''

    if (locale && locale != 'ru') {
      translatedValues = await LangDict.textSearchTranslate(value, locale, { more: true, moreQty: 3 })
    }

    let [products, categories] = await Promise.all([
      Product.query()
        .select(clientProductDBfields)
        .select(Database.raw(orderByStockQuery))
        .leftJoin('cats', 'products.prod_cat', '=', 'cats.cat_id')
        .where('cats.cat_active', true)
        .andWhere((builder) => {
          Product.fastSearchQueryBuilder({ fields: skuFields, query: builder, searchValue: value, translatedValues })
        })
        .if(translitValue && translitValue !== value, (query) => {
          query.andWhere((builder) => {
            Product.fastSearchQueryBuilder({ fields: skuFields, query: builder, searchValue: value, translatedValues })
          })
        })
        .limit(20),

      Category.query()
        .select('cat_title', 'cat_url')
        .where((builder) => {
          if (translatedValues.length) {
            translatedValues.map((item) => {
              builder.orWhere('cat_title', 'like', `%${item.text}%`)
              builder.orWhere('cat_title', 'like', `%${item.text.slice(0, item.text.length)}%`)
            })
          } else {
            builder.where('cat_title', 'like', `%${value}%`)
          }
        })
        .andWhere('cat_active', 1)
    ])

    /*         await Promise.all(products.map(async (product) => {
            await product.load('category')
            if (locale && locale != 'ru') {
                await LangDict.categoriesTranslate([product.category], locale)
            }
        })) */

    if (locale && locale != 'ru') {
      await Promise.all([LangDict.prodsTranslate(products, locale), LangDict.categoriesTranslate(categories, locale)])
    }

    return {
      products,
      categories
    }
  }

  public async global({ request, params, response, auth, session }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined
    const isRumisota = locale && locale != 'ru'

    const sizeField: string = 'prod_size'
    const categoriesFilterField: string = 'prod_morecats'
    const categoryFilterField: string = 'prod_cat'

    const { orderByStockField, orderByStockQuery } = Product.orderByStock()
    const { orderByFullMatchQuery } = Product.orderByFullMatch()
    const { skuFields } = Product.getProductColumns()

    const _category = new Category()
    const _filter = new Filter()
    const _cart = new Cart()

    const defOrder = {
      c: 'products.prod_cat'
    }

    let page = request.qs().page || 1

    const { debug: _debug } = request.qs()

    let categoryFilter: number[] = []
    let value: string = decodeURIComponent(decodeURIComponent(params.value)).trim() //.toUpperCase()
    let translatedValues: any = []

    // const { orderByLengthField, orderByLengthQuery } = Product.orderByLength(value)

    let { extandedSearchFields, productDBfields, clientProductDBfields, orderRaw, filters, limit } = Product.getParams(request)
    let { searchBySize, sizeIn, sizeIn_2, sizeOut, sizeOut_2, sizeHeight, sizeHeight_2, sizeTollerance, _value } = searchValueHandler(
      { value, productDBfields, sizeField },
      filters
    )
    // console.log('🚀 ~ SearchesController ~ global ~ searchBySize:', searchBySize)
    // console.log('🚀 ~ file: SearchesController.ts:117 ~ SearchesController ~ global ~ value:', value)
    // console.log('🚀 ~ file: SearchesController.ts:123 ~ SearchesController ~ global ~ _value:', _value)

    const clearRegex = /[^a-zA-ZА-Яа-я0-9]/gm
    let extandedQueries = parseExtandedQueries(_value)
    let translitValue = transliterate(value) || ''

    // extandedQueries.push((_value).replace())

    let originalQueries: any[] = []

    if (locale && locale != 'ru') {
      await Promise.all(
        extandedQueries.map(async (q) => {
          let r: Array<textSearchTranslateResults> = await LangDict.textSearchTranslate(q, locale, { more: true, moreQty: 3 })
          r.map((i) => translatedValues.push(i.text))
        })
      )

      originalQueries = extandedQueries
      extandedQueries = translatedValues
    }

    if (filters[categoriesFilterField]) {
      categoryFilter = filters[categoriesFilterField]
      delete filters[categoriesFilterField]

      let childsIds = await Promise.all(
        categoryFilter.map(async (id) => {
          let childCats = await Category.getChildCats(id)
          return childCats.map((cat) => cat.cat_id)
        })
      )

      categoryFilter = [...categoryFilter, ...childsIds.flat()]
    }

    let completeMatch,
      prodsIgnoreIds: number[] = [],
      queryIgnoreIds: number[] = []

    // console.log('start main query')
    // //console.time('start main query')

    const rmSortFields = ['prod_count', 'prod_id', 'prod_group_count', 'prod_img', 'prod_rk', 'prod_img_rumi', 'prod_cat', 'prod_group']
    const toSortFields = Array.from(new Set([...extandedSearchFields, ...productDBfields].map((i) => i.replace('products.', '')))).filter((i) => !rmSortFields.includes(i))

    async function getProducts() {
      return await Product.query()
        .select(clientProductDBfields)
        .select(Database.raw(orderByStockQuery))
        // .join('cats', 'products.prod_cat', '=', 'cats.cat_id')
        .where((mainquery) => {
          mainquery.if(searchBySize, (q) => {
            q.where((builder) => rebuildBySizeQuery(builder, { sizeField, sizeIn, sizeIn_2, sizeOut, sizeOut_2, sizeHeight, sizeHeight_2, sizeTollerance, searchBySize }))
            // q.orWhere('prod_size', value)
          })
          // [searchBySize ? 'orWhere' : 'andWhere']
          mainquery.where((query) => {
            Product.searchQueryBuilder({
              query,
              fields: [...extandedSearchFields, ...productDBfields],
              searchValue: _value,
              exSearchValue: value,
              translitValue: translitValue !== _value && translitValue !== value ? translitValue : undefined,
              searchBySize
            })
          })
          mainquery.orWhere((mquery) => {
            Product.fastSearchQueryBuilder({ fields: skuFields, query: mquery, searchValue: value, translatedValues })
          })
          mainquery.if(value.match(clearRegex) && !searchBySize, (mquery) => {
            mquery.orWhere((mquery) => {
              Product.fastSearchQueryBuilder({ fields: skuFields, query: mquery, searchValue: value.replace(clearRegex, ''), translatedValues })
            })
          })
        })
        .andWhere((builder) => {
          let fKeys = Object.keys(filters)
          if (fKeys.length > 0) fKeys.map((key) => builder.andWhereIn(key, filters[key]))
          if (categoryFilter && categoryFilter.length > 0) builder.andWhereIn(categoryFilterField, categoryFilter)
        })
        .if(request.requestQs?.where == 'instock', (query) => {
          query.andWhere((subquery) => {
            subquery.where('prod_count', '>', 0).orWhere('prod_group_count', '>', 0)
          })
        })
        .if(true, (query) => {
          toSortFields.map((field) => {
            query.orderByRaw(orderByFullMatchQuery, [field, value])
          })
        })
        .orderBy(orderByStockField, 'desc')
        .if(!searchBySize, (query) => {
          toSortFields.map((field) => {
            query.orderByRaw(`INSTR(?, ??) desc, LOCATE(?, ??) desc`, [value, field, value, field])
          })
        })
        // .if(searchBySize, (query) => {
        //   query.orderByRaw(`INSTR(?, prod_size) desc, LOCATE(?, prod_size) desc`, [value, value])
        // })
        .orderByRaw(orderRaw)
        //   .orderBy(defOrder.c, 'asc')
        // .orderBy('cats.cat_search_sort', 'asc')
        // .if(searchBySize, (query) => {
        //   query.orderBy('size_in', 'desc')
        //   query.orderBy('size_out', 'desc')
        // })
        .debug(_debug)
        .paginate(page, limit)
    }

    let products = await getProducts()

    if (products.length === 0 && page > 1) {
      page = 1
      products = await getProducts()
    }

    // //console.timeEnd('start main query')
    // console.log(
    //   'products:',
    //   products.map((prod) => ({ size: prod.prod_size, sku: prod.prod_analogsku }))
    // )

    if (products.length === 0) {
      return { searchresults: [], total: 0 }
    }

    try {
      products.forEach((item) => {
        if (item.prod_analogs) {
          const analogs = item.prod_analogs.split(',').filter((i) => i)
          const [foundAnalog] = analogs.filter((x) => x.search(_value) != -1)
          foundAnalog ? (item[isRumisota ? 'prod_analogsku' : 'prod_sku'] = foundAnalog) : ''
        }
      })
    } catch (error) {}

    let categoriesIds: Array<number> = []

    try {
      categoriesIds = [...new Set([...products].map((item) => item.prod_cat))]
    } catch (error) {}

    try {
      const [categories, filterslist, { idField, cartId }] = await Promise.all([
        _category.getCategories({ isSearch: true, withColumns: true, ids: categoriesIds.length > 0 ? categoriesIds : false }, true),
        _filter.getListByIds(categoriesIds),
        _cart.getCartId(request, response, auth)
      ])

      await setProdCartQty(idField, cartId, products)

      const defCategory = {} //new Category()
      defCategory.cat_title = 'Результаты поиска'
      defCategory.cat_id = 9999
      defCategory.products = []
      defCategory.columns = await Category.getCategoryColumns(9999, false, true)

      categories.push(defCategory)

      if (locale && locale != 'ru') {
        await Promise.all([LangDict.prodsTranslate(products, locale), LangDict.categoriesTranslate(categories, locale)])
      }

      let searchresults = await _category.merger({ categories, products })
      let showMoreResults = value.search(/\d{1,}/gm) > -1 && !searchBySize

      return {
        searchresults,
        total: products.total || completeMatch.length,
        filterslist,
        per_page: products.perPage,
        current_page: products.currentPage,
        value: searchBySize ? getSizeValue(value) : value,
        searchBySize,
        sizeParams: {
          sizeIn,
          sizeIn_2,
          sizeOut,
          sizeOut_2,
          sizeHeight,
          sizeHeight_2,
          sizeTollerance
        },
        showMoreResults,
        prodsIgnoreIds
      }
    } catch (error) {
      console.error(error)
      throw new Error('prepare paginator data: ' + error)
    }
  }
}
