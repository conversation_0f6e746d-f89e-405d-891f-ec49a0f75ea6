import LangDict from 'App/Models/LangDict'

// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import Cart from 'App/Models/Cart'
import Product from 'App/Models/Product'
import Category from 'App/Models/Category'
import CartItem from 'App/Models/CartItem'
import Filter from 'App/Models/Filter'
import { parseQS } from 'App/Helpers/parseQS'
import { searchValueHandler } from 'App/Helpers/searchValueHandler'
//import { buildBySizeQuery } from 'App/Helpers/buildBySizeQuery'

const categoriesFilterField: string = 'prod_morecats'
const categoryFilterField: string = 'prod_cat'

const defOrder = {
  c: 'products.prod_cat'
}

interface parsedQueryString {
  searchvalue: string
}

export default class CartsController {
  async clear({ response, request, params, auth }) {
    const _cart = new Cart()
    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    await CartItem.query().where(idField, cartId).delete()
    response.clearCookie('_basket')

    return { msg: 'Корзина очищена' }
  }

  async push({ response, request, auth }) {
    // const sessionUser = await auth.authenticate()

    const _cart = new Cart()
    //const _cartItem = new CartItem()
    const { product } = request.requestBody

    _cart.checkCookie({ response, request })

    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    const searchPayload = { cart_id: cartId, prod_id: product.prod_id }
    const savePayload = product

    if (product.qty === 0) {
      await CartItem.query().where(searchPayload).delete()
    } else {
      await CartItem.updateOrCreate(searchPayload, savePayload)
    }

    const cartsum = await _cart.getCartSum(idField, cartId)
    const total = await CartItem.query().count('* as total').where(idField, cartId).first()

    const discountStartSum = _cart.getDiscountStartSum()
    const whosalePrices = cartsum.whosalePrices //cartsum.sum > discountStartSum

    return {
      cartsum: cartsum.sum,
      total: total?.$extras.total || 1,
      discountStartSum,
      whosalePrices,
      discountValue: cartsum.discountValue,
      personalDiscountValue: cartsum.personalDiscountValue,
      bigDiscount: cartsum.bigDiscount
    }
  }

  async mini({ response, request, params, auth }) {
    // const sessionUser = await auth.authenticate()

    const _cart = new Cart()
    const _cartItem = new CartItem()

    const page: number = params.page || 1
    const searchvalue: string = request.requestQs.searchvalue || ''
    const _limit = 30

    _cart.checkCookie({ response, request })

    const { idField, cartId } = await _cart.getCartId(request, response, auth)
    let { extandedSearchFields, cartListProductDBfields } = Product.getParams(request)

    const cartPaginator = await CartItem.query()
      .leftOuterJoin('products', 'products.prod_id', 'cart_items.prod_id')
      .select('cart_items.*')
      .where(idField, cartId)
      .andWhere((builder) => {
        if (searchvalue) {
          builder.whereRaw(`CONCAT(${extandedSearchFields.join(',')}) LIKE ?`, [`%${searchvalue}%`])
        }
      })
      .preload('product', (query) => query.select(cartListProductDBfields))
      .orderBy('updated_at', 'desc')
      .orderBy(defOrder.c, 'asc')
      .paginate(page, _limit)

    const cartsum = await _cart.getCartSum(idField, cartId)
    let { meta, data } = cartPaginator.toJSON()

    const locale = request.requestQs.locale || 'ru'
    if (locale != 'ru') {
      await LangDict.prodsTranslate(data, locale)
    }

    meta['cartsum'] = cartsum.sum
    meta['discountStartSum'] = _cart.getDiscountStartSum()
    meta['discountValue'] = cartsum.discountValue
    data = _cartItem.serializeCart(data)

    return { meta, data }
  }

  async get({ response, request, params, auth }) {
    // const sessionUser = await auth.authenticate()
    const locale = request.headers()['x-locale'] || undefined

    // console.log('cart controller get locale', locale)

    const _cart = new Cart()
    const _category = new Category()
    const _cartItem = new CartItem()
    const _filter = new Filter()

    const page = request.requestQs.page || 1
    const sizeField: string = 'prod_size'
    let searchvalue: string = request.requestQs.searchvalue || ''

    let parsedQueries: parsedQueryString = await parseQS(request.requestQs.qs)

    if (parsedQueries) {
      searchvalue = parsedQueries.searchvalue
    }

    let categoriesIds: Array<number> = []
    _cart.checkCookie({ response, request })

    const { idField, cartId } = await _cart.getCartId(request, response, auth)

    if (!idField || !cartId) {
      response.status(500)
      return false
    }

    let { extandedSearchFields, clientProductDBfields, productDBfields, orderRaw, filters, limit } = Product.getParams(request)
    //let { searchBySize, sizeIn, sizeOut, sizeHeight, sizeTollerance, _value } = searchValueHandler({ value: '', productDBfields, sizeField }, filters)

    let categoryFilter: number[] = []

    if (filters[categoriesFilterField]) {
      categoryFilter = filters[categoriesFilterField]
      delete filters[categoriesFilterField]
    }

    delete filters?.size

    const cartPaginator = await CartItem.query()
      //.leftOuterJoin('products', 'products.prod_id', 'cart_items.prod_id')
      .rightOuterJoin('products', 'products.prod_id', 'cart_items.prod_id')
      .select('cart_items.*')
      .where(idField, cartId)
      .andWhere((builder) => {
        if (searchvalue) {
          builder.whereRaw(`CONCAT(${extandedSearchFields.join(',')}) LIKE ?`, [`%${searchvalue}%`])
        }
      })
      .andWhere((builder) => {
        let fKeys = Object.keys(filters)
        if (fKeys.length > 0) fKeys.map((key) => builder.andWhereIn(key, filters[key]))
        if (categoryFilter && categoryFilter.length > 0) builder.andWhereIn(categoryFilterField, categoryFilter)
      })
      .preload('product', (query) => query.select(clientProductDBfields))
      .orderBy(defOrder.c, 'asc')
      .orderByRaw(orderRaw)
      .paginate(page, searchvalue ? 9999 : limit)

    const cartsum = await _cart.getCartSum(idField, cartId)

    let { meta, data } = cartPaginator.toJSON()

    meta['discountStartSum'] = _cart.getDiscountStartSum()
    meta['cartsum'] = cartsum.sum
    meta['discountValue'] = cartsum.discountValue
    meta['whosalePrices'] = cartsum.whosalePrices //cartsum.whosalePrices//cartsum.sum > meta['discountStartSum']

    let filterslist: Array<object> = []

    let products = _cartItem.serializeCart(data)

    try {
      categoriesIds = [...new Set([...products].map((item) => item.prod_cat))]
    } catch (error) {
      console.warn('set categoriesIds: ', error)
    }

    if (cartPaginator.length > 0) {
      const categories = await _category.getCategories({ withFilters: true, withColumns: true, ids: categoriesIds.length > 0 ? categoriesIds : false })

      const defCategory = {} //new Category()
      defCategory.cat_title = ''
      defCategory.cat_id = 9999
      defCategory.products = []
      defCategory.columns = await Category.getCategoryColumns(9999, false, true)

      categories.push(defCategory)

      if (locale && locale != 'ru') {
        await Promise.all([LangDict.prodsTranslate(products, locale), LangDict.categoriesTranslate(categories, locale)])
      }

      data = await _category.merger({ categories, products })
      filterslist = await _filter.getListByIds(categoriesIds)
    }

    return { data, meta, filterslist, cartsum: cartsum.sum, cartId, personalDiscountValue: cartsum.personalDiscountValue, bigDiscount: cartsum.bigDiscount }
  }
}
