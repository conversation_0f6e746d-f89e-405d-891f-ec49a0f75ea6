import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import View from '@ioc:Adonis/Core/View'
import loadSettings from 'App/Helpers/loadSettings'
import { randomInteger } from 'App/Helpers/randomInteger'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import Category from 'App/Models/Category'
import LangDict from 'App/Models/LangDict'
import Order from 'App/Models/Order'
import Page from 'App/Models/Page'
import { OrderDocument } from 'App/Plugins/Documents'
import { Reconact } from 'App/Plugins/Reconact'
import { concatSpecs, findInvoicePageBySpecs, findInvoicePageBySpecsOneFile, FindPdfInvoiceResult, SpecificationData } from 'App/Plugins/Specs'
import dayjs from 'dayjs'

import fs from 'fs'

interface ClientSender {
  name: string
  address: string
  birthdate: string
  index: number
  phone: string
}

function sortProductsBySizeFields(products: SnapshotBodyItemInterface[]): SnapshotBodyItemInterface[] {
  return products.sort((a, b) => {
    if (a.size_in !== b.size_in) {
      return a.size_in - b.size_in
    } else if (a.size_in_2 !== b.size_in_2) {
      return a.size_in_2 - b.size_in_2
    } else if (a.size_out !== b.size_out) {
      return a.size_out - b.size_out
    } else if (a.size_out_2 !== b.size_out_2) {
      return a.size_out_2 - b.size_out_2
    } else if (a.size_h !== b.size_h) {
      return a.size_h - b.size_h
    } else {
      return a.size_h_2 - b.size_h_2
    }
  })
}

export default class PagesController {
  public async page({ params, request }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || 'ru'
    const { key } = params

    const page = await Page.pageByKey(key, locale)

    return page || {}
  }

  public async printFormList({ params, request, response }: HttpContextContract) {
    return await Page.query().where({ print: true })
  }

  public async update({ params, request, response, auth }: HttpContextContract) {
    const { formData, isPrint = true } = request.all()
    const user = await auth.use('api').authenticate()

    if (!formData.print) {
      formData.print = true
    }

    const res = await Page.updateOrCreate(
      {
        // page_id: formData.page_id,
        page_key: formData.page_key
      },
      formData
    )

    // console.log('res:', res);

    return res
  }

  public async printForm({ params, request, response }: HttpContextContract) {
    const { key, orderId, mergeOrdersIds, formHTML, locale, senderIndex, clientId = 349, dates = '2024-06-30T22:00:00.000Z,2024-08-29T22:00:00.000Z' } = request.all()
    // console.log('🚀 ~ PagesController ~ printForm ~ mergeOrdersIds:', mergeOrdersIds)

    type OrderData = Awaited<ReturnType<typeof Order.cpanOrderByID>>
    type OrderDataEx = (OrderData & { data: { formattedDate?: string; sender?: ClientSender }; documentData?: InitReturnType }) | undefined | null
    type OrderDocumentType = OrderDocument

    type InitReturnType = Awaited<ReturnType<InstanceType<typeof OrderDocument>['init']>>

    const searchPayload = { page_key: key, print: true }
    const rawForm = await Page.query().where(searchPayload).firstOrFail()

    if (rawForm?.page_body?.includes('$reconact') || (formHTML && formHTML?.includes('$reconact'))) {
      // console.log('🚀 ~ PagesController ~ printForm ~ dates:')
      let dateRange = String(dates)
        .split(',')
        .map((date) => dayjs(date).format('YYYY-MM-DD HH:mm:ss'))

      let data = {}
      const reconact = new Reconact(clientId)

      let orders = await reconact.getOrdersDataByClient(dateRange)

      if (orders.length === 0) {
        return response.status(404).send('orders not found')
      }

      data.orders = orders
      data.range = dateRange
      data.client = orders?.[0]?.client

      const html = await View.renderRaw(formHTML || rawForm?.page_body, { data })

      return { html } || response.status(500).send('server error')
    }

    let order: OrderDataEx = undefined

    if (orderId) {
      order = await Order.cpanOrderByID(orderId)
    }

    if (order === null) {
      return response.status(404).send('order not found')
    }

    if (order) {
      order.data.formattedDate = dayjs(order.data.order_datetime).format('DD.MM.YYYY') //new Date(order.data.order_datetime).toLocaleString()
    }

    if (rawForm?.page_body?.includes('documentData') || (formHTML && formHTML?.includes('documentData'))) {
      // console.log('🚀 ~ PagesController ~ printForm ~ documentData: make data...')
      const orderDocument = new OrderDocument({
        orderId,
        type: 'xlsx'
      })

      const documentData = await orderDocument.init()
      order.documentData = documentData

      order.documentData.products = sortProductsBySizeFields(order.documentData.products)
    }

    if (rawForm?.page_body?.includes('sender.') || (formHTML && formHTML?.includes('sender.'))) {
      const { senders }: { senders: ClientSender[] } = await loadSettings(['senders'])
      let sIndex = senderIndex || Math.floor(Math.random() * senders.length)
      const randomSender = senders[sIndex]

      order.data.sender = randomSender
    }

    if (rawForm?.page_body?.includes('postList') || (formHTML && formHTML?.includes('sender.'))) {
      const allCategories = await Category.query().where({ cat_active: true })
      const { cataliases } = await loadSettings(['cataliases'])

      const cIds = order.data.items.map((prod) => ({
        qty: prod.orderCount || prod.item_count,
        catId: prod.prod_cat,
        price: prod.prod_price
      }))

      // console.log("🚀 ~ file: viewOrder.vue:2758 ~ mounted ~ cIds:", cIds)

      const categoriesById = allCategories.reduce((acc, category) => {
        acc[category.cat_id] = category
        return acc
      }, {})

      // console.log('categoriesById:', categoriesById)

      const result = Object.values(
        cIds.reduce((acc, { catId, qty, price }) => {
          if (!acc[catId]) {
            acc[catId] = { catId, qty, price, total: qty * price }
            acc[catId].cat_title = categoriesById[catId].cat_title
          } else {
            acc[catId].qty += qty
            // acc[catId].price += price;
            acc[catId].total += qty * price
          }
          return acc
        }, {})
      )

      order.data.postList = result.map((i) => {
        let _total = Number(i.total) / 10
        return {
          title: cataliases[i.catId][i.qty > 1 ? 'plural' : 'singular'],
          qty: i.qty < 20 ? i.qty : randomInteger(5, 20),
          total: _total < 1000 ? _total.toFixed() : randomInteger(90, 1000)
          // brand: 'TCS'
        }
      })
    }

    order.data.items = sortProductsBySizeFields(order.data.items) //.reverse()

    if ((locale && locale != 'ru') || rawForm.page_locale != 'ru') {
      await LangDict.prodsTranslate(order.data.items, locale || rawForm.page_locale)
    }

    let mergOrders: OrderData[] = [order]
    let concatedSpecs: SpecificationData = order?.data.specs || {}
    let pdfInvoices: FindPdfInvoiceResult = {}

    if (mergeOrdersIds && order) {
      let ids = String(mergeOrdersIds)
        .split(',')
        .filter((i) => i)
        .map((i) => Number(i))

      await Promise.all(
        ids.map(async (i) => {
          let order = await Order.cpanOrderByID(Number(i))
          order.data.formattedDate = dayjs(order.data.order_datetime).format('DD.MM.YYYY')
          mergOrders.push(order)
        })
      )
      concatedSpecs = concatSpecs(mergOrders.map((i) => i?.data.specs))
      // console.log('🚀 ~ PagesController ~ printForm ~ concatedSpecs:', concatedSpecs)
    }

    if (order?.data.specs && (rawForm?.page_body?.includes('invoiceFilePath') || (formHTML && formHTML?.includes('invoiceFilePath')))) {
      // console.log('start make pdfInvoices')

      pdfInvoices = await findInvoicePageBySpecsOneFile(concatedSpecs, mergOrders.map((i) => `#${i?.data.order_id}`).join(','))

      // console.log('🚀 ~ PagesController ~ printForm ~ pdfInvoices:', pdfInvoices)
    }

    const html = await View.renderRaw(formHTML || rawForm?.page_body, {
      data: order?.data,
      documentData: order?.documentData,
      concatedSpecs,
      pdfInvoices,
      mergeMode: mergOrders?.length > 1,
      mergeOrdersIds: mergOrders.map((i) => i?.data.order_id),
      mergOrdersData: mergOrders.map((i) => i?.data)
    })

    return { html } || response.status(500).send('server error')
  }
}
