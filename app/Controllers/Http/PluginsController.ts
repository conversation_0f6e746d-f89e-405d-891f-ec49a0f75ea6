// import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

import { pluginsLoader } from '../../../start/plugins'

export default class PluginsController {
    public async init({ request, params, response, auth }) {

        const body = request.all()
        // console.log('body', body)
        // console.log('params: ', params);
        //pluginsLoader.plugins[params.name].heelo()
    }
}
