import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import Ws from 'App/Services/Ws'
import Journal from 'App/Models/Journal'
import HttpContext from '@ioc:Adonis/Core/HttpContext'

export default class Lock extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({
    autoCreate: true,
    serialize: (value?: DateTime) => {
      try {
        return value ? value.toFormat('dd.LL.yy TT') : value
      } catch (error) {
        return value
      }
    }
  })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public user_id: string

  @column()
  public user_name: string

  @column()
  public entity: string

  @column()
  public entity_id: string

  @column()
  public active: boolean

  public static async lock({ entity, entity_id }: Lock) {
    return {}

    
    const httpCtx = HttpContext.get()
    const user = await httpCtx?.auth.use('api').authenticate()

    // return await this.query().where({
    //   active: true,
    //   entity,
    //   entity_id
    // }).first()

    const res = await this.firstOrCreate(
      {
        active: true,
        entity,
        entity_id
      },
      {
        active: true,
        entity,
        entity_id,
        user_id: user?.user_id,
        user_name: user?.user_name
      }
    )

    // Journal.createItem({
    //   entity,
    //   entity_id,
    //   user_id: user?.user_id,
    //   user_name: user?.user_name,
    //   msg: 'открыт товар на редактирование'
    // })

    return res
  }

  public static async unlock({ entity, entity_id, user_id }: Lock) {
    // console.log("🚀 ~ Lock ~ unlock ~ { entity, entity_id, user_id }:", { entity, entity_id, user_id })
    const _locks = await this.query()
      .where({
        active: true,
        entity,
        entity_id,
        user_id
      })
      .del()

    return { isOk: true }
  }

  public static async forceunlock({ entity, entity_id, user_id }: Lock) {
    const _locks = await this.query()
      .where({
        active: true,
        entity,
        entity_id
        // user_id
      })
      .del()

    return { isOk: true }
  }

  public static async isLock({ entity, entity_id }): Promise<Boolean> {
    return false

    const _lock = await this.query().where({ entity, entity_id, active: true }).first()

    return _lock?.active || false
  }

  public static async inactive(id: number) {
    const _lock = await this.findOrFail(id)
    _lock.active = false
    const res = await _lock.save()

    return res
  }
}
