
import { DateTime } from 'luxon'
import { BaseModel, column, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'
import Product  from 'App/Models/Product'
import Order from 'App/Models/Order'

export default class OrderItem extends BaseModel {
  @column({ isPrimary: true })
  public ID: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public item_id: number

  @column()
  public item_count: number

  @column()
  public items_order_id: number

  @column()
  public cancelgtd: boolean

  @hasOne(() => Product, {
    localKey: 'item_id',
    foreignKey: 'prod_id'
  })
  public product: HasOne<typeof Product>

  @hasOne(() => Order, {
    localKey: 'items_order_id',
    foreignKey: 'order_id'
  })
  public order: HasOne<typeof Order>
}
