import Product from 'App/Models/Product'
import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import Database from '@ioc:Adonis/Lucid/Database'
import Cache from 'App/Models/Cache'
import loadSettings from 'App/Helpers/loadSettings'
import OrderSnapshot from 'App/Models/OrderSnapshot'
import { SnapshotBodyItemInterface } from 'App/Interfaces/orders/snapshotBodyItem'
import { SnapshotBodyInterface } from 'App/Interfaces/orders/snapshotBody'
import { groupByField } from 'App/Helpers/groupBy'
import Order from './Order'
import { $prisma } from 'App/Services/Prisma'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'

const isbot = require('isbot')

interface OIP {
  items_order_id: number
  item_id: number
  item_count: number
  ID: number
  order_id: number
  order_price: number
  order_shippingprice: number
  order_status: string
  order_datetime: string
  prod_id: number
  prod_price: number
  prod_discount: number
  prod_analogsku: string
  prod_sku: string
  prod_wholesaleprice: number
  order_discount: number
}

interface SalesByFilterProps {
  currentMonth?: number | undefined
  currentYear?: number | undefined
  filters?: any[] | undefined
  dateRange?: string[] | undefined
  groupby?: string[]
  calcGroupSum?: boolean
  oldPrice?: boolean
}

const _months = ['Январь', 'Февраль', 'Март', 'Апрель', 'Май', 'Июнь', 'Июль', 'Август', 'Сентябрь', 'Октябрь', 'Ноябрь', 'Декабрь']

export default class Statistic extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  /*   @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime */

  @column()
  public query: string

  @column()
  public client_ip: string

  @column()
  public client_info: string

  @column()
  public count: number

  @column()
  public manual: number

  public static async statList({ limit = 80, searchvalue = '', isNoStock = true }) {
    type Qresponse = {
      query: string
      total: number
      product?: Product
    }

    function updateArrays(arr1: Qresponse[], arr2: Qresponse[]): Qresponse[] {
      const mergedArray: Qresponse[] = [...arr1, ...arr2].map((q) => {
        q.query = String(q.query).replace(/\s*/gm, '')
        return q
      })

      const updatedArray: Qresponse[] = mergedArray.reduce((result: Qresponse[], current: Qresponse) => {
        const existingIndex = result.findIndex((item) => item.query === current.query)
        if (existingIndex !== -1) {
          result[existingIndex].total += current.total
        } else {
          result.push(current)
        }
        return result
      }, [])

      return updatedArray
    }

    const fCache = await Cache.query()
      .where({
        ckey: 'statlist',
        hash: JSON.stringify({ limit, searchvalue, isNoStock })
      })
      // .andWhere('updated_at', '>=', new Date(Date.now() - 1000 * 60 * 60 * 3))
      .andWhere('updated_at', '>=', DateTime.now().minus({ hours: 4 }).toSQL({ includeZone: false, includeOffset: false }))
      .first()

    if (fCache) {
      return fCache.body
    }

    const countFieldName = 'total'

    async function q({ manual = true }) {
      const res: Qresponse[] = await Database.from('statistics')
        .select('query')
        .if(!manual, (builder) => builder.count('*', countFieldName))
        .if(manual, (builder) => builder.sum('count', countFieldName))
        .where({ manual })
        .if(searchvalue, (query) => {
          query.where('query', 'like', `%${searchvalue}%`)
        })
        .groupBy('query')
        .orderBy(countFieldName, 'desc')
        .limit(limit)
      // //.debug(true)

      return res
    }

    const [manualRes, defRes] = await Promise.all([await q({ manual: true }), await q({ manual: false })])

    const mergeData = updateArrays(manualRes, defRes)
    const productIds = Array.from(new Set(mergeData.map((i) => i.query)))

    const products = await Product.query()
      .where((query) => {
        query.whereIn('prod_sku', productIds).orWhereIn('prod_analogsku', productIds)
      })
      .if(isNoStock, (query) => query.andWhere('prod_count', 0))

    const result = products
      .map((product) => {
        const fi = mergeData.find((x) => x.query == product.prod_sku || x.query == product.prod_analogsku)
        return {
          ...fi,
          product
        }
      })
      .filter((i) => i.total)
      .sort((a, b) => b.total - a.total)

    const cacheRes = await Cache.updateOrCreate(
      {
        ckey: 'statlist',
        hash: JSON.stringify({ limit, searchvalue, isNoStock })
      },
      {
        body: result,
        ckey: 'statlist',
        hash: JSON.stringify({ limit, searchvalue, isNoStock })
      }
    )

    return result
  }

  public static async statsByOrders({ groupby, shippingtypes = undefined, sumby = 'order_shippingprice' }) {
    const fCache = await Cache.query()
      .where({
        ckey: 'orderstats',
        hash: JSON.stringify({ groupby, shippingtypes, sumby })
      })
      // .andWhere('updated_at', '>=', new Date(Date.now() - 1000 * 60 * 60 * 2))
      .andWhere('updated_at', '>=', DateTime.now().minus({ hours: 12 }).toSQL({ includeZone: false, includeOffset: false }))
      //.debug(true)
      .first()

    if (fCache) {
      return fCache.body
    }

    console.log('load q...')
    //console.time('load q')
    const orders = await Order.query()
      .select('*')
      .where('order_status', '!=', 'Отменен')
      .orderBy('order_id', 'desc')
      .if(sumby == 'sumQty', (query) => query.preload('items', (q) => q.select('item_count')))
      .if(shippingtypes, (query) => query.andWhereIn('order_shipping', shippingtypes))
    // .limit(100)
    //console.timeEnd('load q')

    const mOrders = orders.map((mOrder) => {
      return {
        ...mOrder,
        shop: mOrder.order_locale == 'ru' ? 'МирСальников' : 'Rumisota',
        orderSum: mOrder.order_price + mOrder.order_shippingprice,
        monthNumber: mOrder.order_datetime.month + 1,
        month: _months[mOrder.order_datetime.month - 1],
        year: mOrder.order_datetime.year,
        sumQty: mOrder.items?.reduce((sum, item) => (sum += Number(item.item_count)), 0)
        // sumQty: 0 //mOrder.items
      }
    })
    console.log('🚀 ~ Statistic ~ mOrders ~ mOrders:', mOrders)

    function calcSum(orders: Order[]): number {
      if (Array.isArray(orders)) {
        return orders.reduce((sum, order) => {
          return (sum += Number(order[sumby]))
        }, 0)
      } else {
        return 0
      }
    }

    function process(obj: any) {
      const res = {}
      Object.keys(obj).forEach((key) => {
        if (Array.isArray(obj[key])) {
          const items = obj[key]
          res[key] = calcSum(items)
        } else if (typeof obj[key] == 'object') {
          res[key] = process(obj[key])
        } else {
          console.error('unknow type in', key)
        }
      })

      return res
    }

    function recursiveGroupByField(items, ggroups, index = 0) {
      if (index >= ggroups.length) {
        return items
      }
      const res = groupByField({
        array: items,
        fieldName: ggroups[index]
      })
      Object.keys(res).forEach((key) => {
        res[key] = recursiveGroupByField(res[key], ggroups, index + 1)
      })
      if (ggroups[index] === 'month') {
        const sortedRes = {}
        Object.keys(res)
          .sort((a, b) => {
            const monthA = _months.indexOf(a)
            const monthB = _months.indexOf(b)
            return monthA - monthB
          })
          .forEach((key) => {
            sortedRes[key] = res[key]
          })
        return sortedRes
      }
      return res
    }

    const res = process(recursiveGroupByField(mOrders, groupby))

    const cacheRes = await Cache.updateOrCreate(
      {
        ckey: 'orderstats',
        hash: JSON.stringify({ groupby, shippingtypes, sumby })
      },
      {
        body: res,
        ckey: 'orderstats',
        hash: JSON.stringify({ groupby, shippingtypes, sumby })
      }
    )

    return res
  }
  public static async salesByFilters({
    currentMonth = undefined,
    currentYear = undefined,
    filters = undefined,
    dateRange = undefined,
    groupby,
    calcGroupSum,
    oldPrice = true
  }: SalesByFilterProps) {
    const categoriesFilterField: string = 'prod_morecats'
    const categoryFilterField: string = 'products.prod_cat'

    // console.log('salesByFilter ~ currentMonth:', currentMonth)
    // console.log('salesByFilter ~ groupby:', groupby)

    const _year = currentYear || new Date().getFullYear()
    const _currentMonth = currentMonth || new Date().getMonth() + 1

    if (dateRange && Array.isArray(dateRange)) {
      dateRange = dateRange.map((date) => DateTime.fromISO(date).toSQLDate())
    }

    if (filters) {
      try {
        Object.keys(filters).forEach((key) => {
          if (Array.isArray(filters[key]) && !filters[key].length) {
            delete filters[key]
          }
        })
      } catch (error) {
        console.log('filters error:', error)
      }
    }

    let categoryFilter: number[] = []

    if (filters?.[categoriesFilterField]) {
      categoryFilter = filters[categoriesFilterField]
      delete filters[categoriesFilterField]
    }
    let ctm_cache = Date.now()
    //console.time('salesByFilter fCache query:' + ctm_cache)
    const fCache = await Cache.query()
      .where({
        ckey: 'salesByFilter',
        hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
      })
      .andWhere('updated_at', '>=', DateTime.now().minus({ hours: 4 }).toSQL({ includeZone: false, includeOffset: false }))
      //.debug(true)
      .first()

    //console.timeEnd('salesByFilter fCache query:' + ctm_cache)

    if (fCache) {
      console.log('exist cache', { id: fCache.id, ckey: fCache.ckey })

      return fCache.body
    }

    console.log(' _currentMonth:', _currentMonth)
    console.log(' _year:', _year)

    const selects = {
      // products: ['products.prod_id', 'products.prod_price', 'products.prod_discount', 'products.prod_analogsku', 'products.prod_sku', 'products.prod_cat', 'products.prod_manuf'],
      products: ['products.*'],
      // cats: ['cats.cat_id', 'cats.cat_title'],
      cats: ['cats.*'],
      orderItems: ['order_items.items_order_id', 'order_items.item_id', 'order_items.item_count', 'order_items.ID'],
      orders: [
        'orders.order_id',
        'orders.order_price',
        'orders.order_shipping',
        'orders.order_payment',
        'orders.order_shippingprice',
        'orders.order_status',
        'orders.order_datetime',
        'orders.order_coupons'
      ]
    }

    const { DISCOUNT_START_SUM, BIG_DISCOUNT_START_SUM, BIG_DISCOUNT_VALUE } = await loadSettings(['DISCOUNT_START_SUM', 'BIG_DISCOUNT_VALUE', 'BIG_DISCOUNT_START_SUM'])
    console.log('success load settings')

    // //console.time('products query')

    // const products = await Database.from('products')
    //   .select([...selects.products, ...selects.cats])
    //   .leftJoin('cats', 'cats.cat_id', '=', 'products.prod_cat')
    //   .if(filters, (query) => {
    //     query.andWhere((builder) => {
    //       let fKeys = Object.keys(filters)
    //       if (fKeys.length > 0) fKeys.forEach((key) => builder.andWhereIn(key, filters[key]))
    //       if (categoryFilter && categoryFilter.length > 0) builder.andWhereIn(categoryFilterField, categoryFilter)
    //     })
    //   })
    // // .orderBy('prod_id', 'desc')

    // //console.timeEnd('products query')

    // console.log('products len', products.length)

    // return []

    const orderItemsSelect = [...selects.orderItems, ...selects.orders, ...selects.products, ...selects.cats]

    // return orderItemsSelect

    console.log('start orderItems query... ', new Date().toLocaleTimeString())

    let ctm_orderItems = Math.random()
    //console.time('orderItems query: ' + ctm_orderItems)
    const orderItems = await Database.from('order_items')
      .select(orderItemsSelect)
      .innerJoin('orders', 'order_items.items_order_id', '=', 'orders.order_id')
      .innerJoin('products', 'order_items.item_id', '=', 'products.prod_id')
      .innerJoin('cats', 'cats.cat_id', '=', 'products.prod_cat')
      // .whereIn(
      //   'item_id',
      //   products.map((item) => item.prod_id)
      // )
      .andWhere('orders.order_status', '!=', 'Отменен')
      .if(!dateRange, (query) => {
        query.if(!currentMonth, (query) => {
          query.andWhereBetween('orders.order_datetime', [`${_year}-01-01`, `${_year}-12-31`])
        })
        query.if(currentMonth, (query) => {
          query.andWhereBetween('orders.order_datetime', [`${_year}-${_currentMonth}-00`, `${_year}-${_currentMonth}-31`])
        })
      })
      .if(dateRange, (query) => {
        query.andWhereBetween('orders.order_datetime', dateRange)
      })
      .if(filters, (query) => {
        query.andWhere((builder) => {
          let fKeys = Object.keys(filters)
          if (fKeys.length > 0) fKeys.forEach((key) => builder.andWhereIn('products.' + key, filters[key]))
          if (categoryFilter && categoryFilter.length > 0) builder.andWhereIn(categoryFilterField, categoryFilter)
        })
      })

    console.log('success load orderItems, count:', orderItems.length)
    //console.timeEnd('orderItems query: ' + ctm_orderItems)

    const orderIds = orderItems.map((item) => item.order_id)
    let uniqueArray = Array.from(new Set(orderIds))
    let snaphots: OrderSnapshot[] | undefined = undefined
    console.log('success process orderIds array')

    if (oldPrice) {
      console.log('start loadlsnapshots... ', new Date().toLocaleTimeString())
      let ctm_orderSnapshot = Math.random()
      //console.time('load OrderSnapshot: ' + ctm_orderSnapshot)
      snaphots = await OrderSnapshot.query().whereIn('orderid', uniqueArray).groupBy('orderid').orderBy('ID', 'desc')
      //console.timeEnd('load OrderSnapshot: ' + ctm_orderSnapshot)
    }

    console.log('orderCount:', uniqueArray.length)

    type SnaphotProductsMeta = {
      [key: string]: {
        [key: number]: SnapshotBodyItemInterface
      }
    }

    console.log('start make orderItemsWithProduct... ', new Date().toLocaleTimeString())

    const productsMap = new Map()
    // const productsObj = {}
    const snaphotsProductsObj: SnaphotProductsMeta = {}

    // products.forEach((product) => (productsObj[product.prod_id] = product))
    let ctm_productsObj = Math.random()
    //console.time('make productsObj: ' + ctm_productsObj)
    orderItems.forEach((item) => {
      // productsObj[item.prod_id] = item
      productsMap.set(item.prod_id, item)
    })
    //console.timeEnd('make productsObj: ' + ctm_productsObj)

    if (oldPrice) {
      snaphots?.forEach((snaphot) => {
        const body: SnapshotBodyInterface = snaphot.body
        snaphotsProductsObj[snaphot.orderid] = {}
        body.items.forEach((item) => (snaphotsProductsObj[snaphot.orderid][item.prod_id] = item))
      })
    }

    console.log('start make orderItemsWithProduct -> OIP[] = orderItems.map')
    let ctm_orderItemsWithProduct = Math.random()
    //console.time('make orderItemsWithProduct: ' + ctm_orderItemsWithProduct)

    let coi = 0
    const orderItemsWithProduct: OIP[] = []

    const chunkSize = 50000 // Размер каждой части
    for (let i = 0; i < orderItems.length; i += chunkSize) {
      const chunk = orderItems.slice(i, i + chunkSize) // Получаем очередную часть массива

      for (const item of chunk) {
        // const product = productsObj[item.item_id]
        const product = productsMap.get(item.prod_id)

        // console.log('OIP[] orderItems.map  start: ', `${coi++} / ${orderItems.length}`)
        if (product) {
          try {
            if (oldPrice) {
              // rewrite prod_price and prod_discont from snapshot
              const snapProduct = snaphotsProductsObj[String(item.items_order_id)]?.[String(product.prod_id)]
              if (snapProduct) {
                product.prod_price = snapProduct.prod_price
                product.prod_discont = snapProduct.prod_discount
              }
            }
          } catch (error) {}

          product.prod_wholesaleprice = Number((product.prod_price - (product.prod_price / 100) * product.prod_discount).toFixed(2))
          let order_discount = 0

          try {
            order_discount = JSON.parse(item.order_coupons)?.personal
          } catch (error) {}

          if (!order_discount) {
            order_discount = item.order_price > BIG_DISCOUNT_START_SUM ? BIG_DISCOUNT_VALUE : 0
          }

          const _date = new Date(item.order_datetime)

          orderItemsWithProduct.push({
            ...item,
            ...product,
            order_discount,
            order_month: _months[_date.getMonth()],
            order_year: _date.getFullYear()
          })
        }
      }
    }

    //console.timeEnd('make orderItemsWithProduct: ' + ctm_orderItemsWithProduct)

    function recursiveGroupByField(orderItemsWithProduct, ggroups, index = 0) {
      if (index >= ggroups.length) {
        return orderItemsWithProduct
      }

      const res = groupByField({
        array: orderItemsWithProduct,
        fieldName: ggroups[index]
      })

      Object.keys(res).forEach((key) => {
        res[key] = recursiveGroupByField(res[key], ggroups, index + 1)
      })

      return res
    }

    function calcSum(items: OIP[]): number {
      return items.reduce((sum, item) => {
        const discount = item.order_discount / 100 // Преобразование процента в десятичную дробь
        const itemPrice = item.order_price > DISCOUNT_START_SUM ? item.prod_wholesaleprice : item.prod_price
        const totalPrice = itemPrice * item.item_count
        const discountedPrice = totalPrice - totalPrice * discount // Отнять процент от общей суммы
        return sum + discountedPrice
      }, 0)
    }

    console.log('start group by...', new Date().toLocaleTimeString())
    let ctm_groupby = Math.random()

    //console.time('group by: ' + ctm_groupby)
    if (groupby) {
      let ctm_recursiveGroupByField = Math.random()

      console.log('start recursiveGroupByField...', new Date().toLocaleTimeString())

      //console.time('make recursiveGroupByField: ' + ctm_recursiveGroupByField)
      const result = recursiveGroupByField(orderItemsWithProduct, groupby)
      //console.timeEnd('make recursiveGroupByField: ' + ctm_recursiveGroupByField)

      function process(obj: any) {
        const res = {}
        Object.keys(obj).forEach((key) => {
          if (Array.isArray(obj[key])) {
            const items = obj[key]

            res[key] = calcSum(items)
          } else if (typeof obj[key] == 'object') {
            res[key] = process(obj[key])
          } else {
            console.error('unknow type in', key)
          }
        })

        return res
      }

      if (calcGroupSum) {
        function sortBySum(obj) {
          const sorted = {}

          Object.entries(obj).forEach(([key, value]) => {
            if (value.items) {
              sorted[key] = {
                items: sortBySum(value.items),
                sum: value.sum
              }
            } else {
              sorted[key] = value
            }
          })

          return Object.entries(sorted)
            .sort((a, b) => b[1].sum - a[1].sum)
            .reduce((acc, [key, value]) => {
              acc[key] = value
              return acc
            }, {})
        }

        function calculateTotalSum(data) {
          let totalSum = 0
          Object.keys(data).forEach((key) => {
            if (typeof data[key] === 'object') {
              totalSum += calculateTotalSum(data[key])
            } else {
              totalSum += data[key]
            }
          })
          return totalSum
        }

        function groupSum(data) {
          const res = {}

          for (const key in data) {
            if (typeof data[key] === 'object') {
              res[key] = {
                items: groupSum(data[key]),
                sum: calculateTotalSum(data[key])
              }
            } else {
              res[key] = data[key]
            }
          }

          return res
        }

        //@@ @ch
        const resSortBySum = sortBySum(groupSum(process(result)))

        await Cache.updateOrCreate(
          {
            ckey: 'salesByFilter',
            hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
          },
          {
            body: resSortBySum,
            ckey: 'salesByFilter',
            hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
          }
        )

        return resSortBySum
      }

      //@@ @ch

      let ctm_groupprocess = Math.random()
      //console.time('process function: ' + ctm_groupprocess)

      console.log('start groupprocess(process)...', new Date().toLocaleTimeString())
      const resbyP = await process(result)
      console.log('success groupprocess(process)...', new Date().toLocaleTimeString())
      //console.timeEnd('process function: ' + ctm_groupprocess)

      console.log('start Cache.updateOrCreate(groupby)... ', new Date().toLocaleTimeString())

      await Cache.updateOrCreate(
        {
          ckey: 'salesByFilter',
          hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
        },
        {
          body: resbyP,
          ckey: 'salesByFilter',
          hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
        }
      )
      console.log('sucess Cache.updateOrCreate(groupby)... ', new Date().toLocaleTimeString())

      //console.timeEnd('group by: ' + ctm_groupby)

      console.log('@SUCCESS! return response')
      return resbyP
    }

    console.log('start calc sum...')

    let ctm_calcsum = Math.random()
    //console.time('calc sum: ' + ctm_calcsum)
    const sumOfResults = calcSum(orderItemsWithProduct)
    //console.timeEnd('calc sum: ' + ctm_calcsum)

    //@@ @ch
    const resultd = {
      [dateRange?.length ? dateRange.join(' - ') : currentMonth ? _months[_currentMonth - 1] : currentYear || _year]: Number(sumOfResults.toFixed(2))
    }

    console.log('start Cache.updateOrCreate... ', new Date().toLocaleTimeString())

    await Cache.updateOrCreate(
      {
        ckey: 'salesByFilter',
        hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
      },
      {
        body: resultd,
        ckey: 'salesByFilter',
        hash: JSON.stringify({ currentMonth, currentYear, filters, dateRange, groupby, calcGroupSum, oldPrice })
      }
    )

    console.log('success Cache.updateOrCreate:', new Date().toLocaleTimeString())

    console.log('@SUCCESS! return response')
    return resultd
    // return orderItemsWithProduct
  }

  public static async getStatByOem({ countFieldName = 'qstat', oem, brand, list = false }) {
    const product = await Product.findByOrFail('prod_analogsku', oem)

    // const skus = [...product.prod_analogs.split(',').filter((i) => i), product.prod_analogsku, product.prod_sku]

    const skus = [
      ...new Set([
        ...product.prod_analogs
          .split(',')
          .filter((i) => i)
          .map((sku) => sku.trim()),
        product.prod_analogsku.trim(),
        product.prod_sku.trim()
      ])
    ]

    // console.log('🚀 ~ Statistic ~ getStatByOem ~ skus:', skus)

    const rma = []

    const qskus = skus.filter((i) => !rma.includes(i)).map((i) => String(i).toUpperCase())
    // console.log("🚀 ~ Statistic ~ getStatByOem ~ qskus:", qskus)

    const getData = async ({ manual }) => {
      const res = await Statistic.query()
        .select('query')
        .if(!manual, (builder) => builder.count('*', countFieldName))
        .if(manual, (builder) => builder.sum('count', countFieldName))
        .whereIn('query', qskus)
        .if(brand, (query) => query.andWhere('prod_manuf', brand))
        .where({ manual })
        .groupBy('query')
        .orderBy(countFieldName, 'desc')
        .debug(true)

      const sum = res.reduce((acc, current) => {
        return (acc += !isNaN(current.$extras[countFieldName]) ? current.$extras[countFieldName] : 0)
      }, 0)

      if (list) {
        return {
          sum,
          list: res
        }
      }

      return sum
    }

    const total = Number(await getData({ manual: false })) + Number(await getData({ manual: true }))
    return total
  }

  public static async getFullStat({ countFieldName = 'qstat', manual = false, test = false, toObject = false, testSkus = [], dates }) {
    interface StatItem {
      query: string
      qty: number
    }

    const res = await Database.from('statistics')
      .select('query')
      .if(!manual, (builder) => builder.count('*', countFieldName))
      .if(manual, (builder) => builder.sum('count', countFieldName))
      .if(test, (builder) => builder.limit(10))
      .if(test && testSkus.length, (builder) => builder.whereIn('query', testSkus))
      .where({ manual })
      .if(dates, (query) => {
        query.where((subquery) => subquery.whereBetween('statistics.created_at', dates))
      })
      .groupBy('query')
      .orderBy(countFieldName, 'desc')

    if (toObject) {
      const data = {}
      res.map((item) => (data[String(item.query).toUpperCase()] = item[countFieldName]))

      return data
    } else {
      const data: StatItem[] = res.map((item) => ({ query: String(item['query']).toUpperCase(), qty: item[countFieldName] }))

      return data
    }
  }

  public static async remove(id: number) {
    return await Statistic.query().where('id', id).delete()
  }

  public static async write({
    request,
    query,
    info = '',
    count = 1,
    manual = 0
  }: {
    request: HttpContextContract['request']
    query: string
    info?: string
    count?: number
    manual?: number
  }) {
    if (!request) {
      console.warn('Helper::writeStats::warn::request is empty!')
    }
    
    const { 'user-agent': userAgent } = request.headers()

    if (isbot(userAgent)) {
      return false
    }

    if (!query) {
      console.warn('Helper::writeStats::warn::query is empty!')
      return false
    }

    query = decodeURIComponent(query)

    let client_ip: string
    client_ip = request.ip()

    if (!client_ip) {
      console.log('Helper::writeStats::info: set client_ip to "127"')
      client_ip = '127'
    }

    const insertData = {
      count,
      manual,
      query,
      client_ip,
      client_info: `${info} | ${userAgent}`
    }

    try {
      /*      const found = await this.query().where({ query, client_ip }).first()
      //console.log('found', found)

      if (!found) {
        const newi = await this.create(insertData)
      } else {
        await this.query().where({ query, client_ip }).update({ count, manual, client_info: info })
      } */

      const newi = await Statistic.create(insertData)
      return newi
    } catch (error) {
      console.error('Helper::writeStats::error:', error)
    }
  }
}
