import { DateTime } from 'luxon'
import { column, BaseModel, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'

import Product from 'App/Models/Product'
import Cart from 'App/Models/Cart'

export default class CartItem extends BaseModel {
  
  @hasOne(() => Product, {
    localKey: 'prod_id',
    foreignKey: 'prod_id',
  })
  public product: HasOne<typeof Product>

  @hasOne(() => Cart, {
    localKey: 'cart_id',
    foreignKey: 'cart_id',
  })
  public cart: HasOne<typeof Cart>

  @column({ isPrimary: true })
  public id: number

  @column()
  public cart_id: number

  @column()
  public prod_id: number

  @column()
  public qty: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime


  serializeCart(clientCart: any) {
    let res = clientCart.map(i => {

      try {
        let item = i.toObject()
        let pobj = item.product
  
        if (pobj) {
          pobj['qty'] = item['qty']
  
          pobj['createdAt'] = item['createdAt'].setLocale('ru').toISODate()
          pobj['updatedAt'] = item['updatedAt'].setLocale('ru').toISODate()
  
          return pobj
        }
      } catch (error) {
        console.error('serializeCart: ', error)
      }

    })

    return res.filter(i => i)
  }
}
