// app/Models/Point.js

import { DateTime } from 'luxon'
import { BaseModel, column, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'
import Client from 'App/Models/Client'
import Order from 'App/Models/Order'

interface PointPayload {
  order: Order
  client: Client
}

export default class Point extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public client_id: number

  @column()
  public order_id: number

  @column()
  public points: number

  @column()
  public type: 'increment' | 'decrement'

  @column.dateTime({ autoCreate: true })
  public created_at: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updated_at: DateTime

  @hasOne(() => Client, {
    foreignKey: 'client_id',
    localKey: 'client_id'
  })
  public client: HasOne<typeof Client>

  @hasOne(() => Order, {
    foreignKey: 'order_id',
    localKey: 'order_id'
  })
  public order: HasOne<typeof Order>

  public static async createPoints() {
    const res = await this.create({})
  }

  public static async cancelPoints() {

  }

}
