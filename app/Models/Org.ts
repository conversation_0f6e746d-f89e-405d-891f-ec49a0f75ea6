import { DateTime } from 'luxon'
import { BaseModel, column, belongsTo, BelongsTo, hasOne, HasOne } from '@ioc:Adonis/Lucid/Orm'
import Client from 'App/Models/Client'

export default class Org extends BaseModel {
  @column({ isPrimary: true })
  public org_id: number

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  @column()
  public org_client: string

  @column()
  public org_name: string

  @column()
  public org_adress: string

  @column()
  public org_inn: string

  @column()
  public org_kpp: string

  @column()
  public org_rschet: string

  @column()
  public org_kschet: string

  @column()
  public org_bik: string

  @column()
  public org_bank: string

  @column()
  public org_vat: string

  @hasOne(() => Client, {
    foreignKey: 'client_number',
    localKey: 'org_client'
  })
  public user: HasOne<typeof Client>
}
