import { SearchTextTranslateParams, textSearchTranslateResults } from 'App/Interfaces/locales/index'
import Product from 'App/Models/Product'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'
import Category from './Category'
import i18next from 'i18next'
import { reverseObject } from 'App/Helpers/reverseObject'

export const translatableFields: string[] = ['prod_note', 'prod_purpose', 'prod_uses', 'prod_material', 'prod_type', 'cat_title']

const categoryTranslatedFields: string[] = ['cat_title', 'cat_note']

export default class LangDict extends BaseModel {
  public static table = 'lang_dict'

  @column({ isPrimary: true })
  public id: number

  @column()
  public tkey: string

  @column()
  public en: string

  @column()
  public pl: string

  @column()
  public de: string

  @column()
  public it: string

  @column()
  public fr: string

  @column()
  public ar: string

  @column()
  public backend: boolean

  public static async categoriesTranslate(categories: Array<Category>, locale: string = 'en') {
    //const Dict = await this.query().select('tkey', locale).orderByRaw(`CHAR_LENGTH(${locale}) DESC`)
    //let dict = this.dictTransform(Dict, locale)

    let dict = i18next.getDataByLanguage(locale)?.translation

    // //console.time('category @for dict')
    categories.forEach((category) => {
      for (const key in dict) {
        if (dict.hasOwnProperty(key)) {
          const regex = new RegExp(key, 'gim')
          categoryTranslatedFields.forEach((field) => {
            if (category[field]) category[field] = category[field].replace(regex, dict[key])
          })
        }
      }
    })
    // //console.timeEnd('category @for dict')
  }

  public static async prodsTranslate(products: Array<Product>, locale: string = 'en') {
    if (!products || !products.length) {
      return false
    }

    Product.rumisotaProductTransform(products)

    //const Dict = await this.query().select('tkey', locale).orderByRaw(`CHAR_LENGTH(${locale}) DESC`)
    //let dict = this.dictTransform(Dict, locale)

    let dict = i18next.getDataByLanguage(locale)?.translation

    products.forEach((product) => {
      for (const key in dict) {
        if (dict.hasOwnProperty(key)) {
          try {
            const regex = new RegExp(key, 'gim')
            // обходим поля замены
            translatableFields.forEach((field) => {
              if (product[field]) product[field] = product[field].replace(regex, dict[key])
            })

            if (product.category?.cat_title) {
              product.category.cat_title = product.category.cat_title.replace(regex, dict[key])
            }
          } catch (error) {
            console.error('prodsTranslate: ', error)
          }
        }
      }

      product.prod_img = product.prod_img_rumi
    })

    /*     //console.time('start @translatableFields')
    products.map(product => {
      translatableFields.map(field => {
        if (dict.hasOwnProperty(product[field])) {
          product[field] = dict[product[field]]
          console.log('product[field]', product[field])
        }
      })

      product.prod_img = product.prod_img_rumi
    })
    //console.timeEnd('start @translatableFields') */
  }

  public static dictTransform(Dict, locale = 'en', reverse?) {
    //console.log('Dict', Dict)
    //console.log('reverse', reverse)
    return Dict.reduce(function (obj, item) {
      if (reverse) obj[item[locale]] = item.tkey
      else obj[item.tkey] = item[locale]

      return obj
    }, {})
  }

  public static async getDict(locale, reverse?) {
    const Dict = await this.query().select('tkey', locale).orderByRaw(`CHAR_LENGTH(${locale}) DESC`)
    let dict = this.dictTransform(Dict, locale, reverse)

    return dict
  }

  public static async textSearchTranslate(str, locale = 'en', params?: SearchTextTranslateParams) {
    //const Dict = await this.getDict(locale, 'reverse')
    let Dict = i18next.getDataByLanguage(locale)?.translation
    let reverseDict = reverseObject(Dict)

    //reverseObject
    //console.log('Dict', Dict)

    /*     for (const key in Dict) {
      if (Dict.hasOwnProperty(key)) {
        const regex = new RegExp(key, 'gim')
        //str = str.replace(regex, Dict[key])
      }
    } */

    try {
      //TODO: str trim | cach dict

      let results: Array<textSearchTranslateResults> = []

      for (const key in reverseDict) {
        const regex = new RegExp(str, 'gim')
        let spos = key.search(regex)

        if (spos > 0) {
          results.push({
            text: reverseDict[key],
            key,
            spos
          })
        }
      }

      let sortedResults = results.sort((a, b) => a.spos + a.key.length - (b.spos + b.key.length))
      return params && params.more ? sortedResults.slice(0, params.moreQty || 3) : sortedResults[0].text
    } catch (error) {
      return str
    }
  }
}
