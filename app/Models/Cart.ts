import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import Product from 'App/Models/Product'

import { DateTime } from 'luxon'
import { column, BaseModel, hasMany, HasMany } from '@ioc:Adonis/Lucid/Orm'

import CartItem from 'App/Models/CartItem'
import Database from '@ioc:Adonis/Lucid/Database'

import { randomInteger } from 'App/Helpers/randomInteger'
import { mergeObjects } from 'App/Helpers/mergeObject'
import Env from '@ioc:Adonis/Core/Env'
import { CartSum } from 'App/Interfaces/cart/CartSum'
import isbot from 'isbot'
import { RequestContract } from '@ioc:Adonis/Core/Request'
import Client from './Client'

interface qtyError {
  sku: string
  query: number
  stock: number
  cartItemId?: number
  msg?: string
  isDel?: boolean
}

interface groupMessage {
  group: string
  skus: object
  isGroup: boolean
}

const discountStartSum = Number(Env.get('DISCOUNT_START_SUM')) //|| 7500
const bigDiscountStartSum = Number(Env.get('BIG_DISCOUNT_START_SUM')) //|| 100000
const bigDiscountValue = Number(Env.get('BIG_DISCOUNT_VALUE')) //|| 10

export default class Cart extends BaseModel {
  public static table = 'cart'

  @hasMany(() => CartItem, {
    foreignKey: 'cart_id'
  })
  @hasMany(() => CartItem)
  public cartItems: HasMany<typeof CartItem>

  @column({ isPrimary: true })
  public cart_id: number

  @column()
  public cart_cookie: string

  @column()
  public cart_client: string

  @column()
  public cart_items: string

  @column.dateTime({ autoCreate: true })
  public createdAt: DateTime

  @column.dateTime({ autoCreate: true, autoUpdate: true })
  public updatedAt: DateTime

  cartCookie: string

  async checkCookie({ response, request }: HttpContextContract) {
    //console.log('start checkCookie');

    const locale = request.headers()['x-locale'] || undefined

    if (!request.cookieParser.cookies['_cart']) {
      this.cartCookie = Buffer.from(randomInteger(3 ** 9, 9 ** 9)).toString('base64')
      response.plainCookie('_cart', this.cartCookie, { maxAge: 30 * 24 * 60 * 60 * 1000 })
    } else {
      this.cartCookie = request.cookieParser.cookies['_cart']
    }
  }

  getDiscountStartSum() {
    return Number(discountStartSum)
  }

  getDiscountMeta() {
    return {
      discountStartSum,
      bigDiscountStartSum,
      bigDiscountValue
    }
  }

  async getCartSum(idField, cartId): Promise<CartSum> {
    let client: Client | null = null

    if (idField == 'cart_client') {
      client = await Client.findBy('client_number', cartId)
    } else {
      const _cart = await Cart.findBy('cart_id', cartId)
      if (_cart) {
        client = await Client.findBy('client_number', _cart.cart_client)
      }
    }

    const q = `select sum( cart_items.qty * 
      IF( (select sum(cart_items.qty * products.prod_price) as cartsum 
      from cart_items 
      left outer join products 
      on products.prod_id = cart_items.prod_id 
      where \`${idField}\` = ? limit ?) > ${discountStartSum}, 
      products.prod_price - ((products.prod_price / 100) * products.prod_discount), products.prod_price ) ) as cartsum,
      sum(cart_items.qty * products.prod_price) as defsum 
      from cart_items 
      left outer join products 
      on products.prod_id = cart_items.prod_id 
      where \`${idField}\` = ? limit ?`

    let [[{ cartsum, defsum }]]: any = await Database.rawQuery(q, [cartId, 1, cartId, 1])

    let discountValue = 0
    let whosalePrices = defsum > discountStartSum
    let bigDiscount = cartsum > bigDiscountStartSum
    let personalDiscountValue = client?.client_discount || 0

    if (whosalePrices && personalDiscountValue && !bigDiscount) {
      discountValue += (cartsum / 100) * personalDiscountValue
      cartsum -= discountValue
    }

    if (bigDiscount) {
      discountValue = (cartsum / 100) * (bigDiscountValue > personalDiscountValue ? bigDiscountValue : personalDiscountValue)
      cartsum -= discountValue
    }

    return {
      sum: cartsum ? Number(cartsum.toFixed(2)) : 0,
      discountValue,
      whosalePrices,
      defsum,
      bigDiscount,
      bigDiscountValue,
      personalDiscountValue
    }
  }

  async getCartWeight(idField, cartId) {
    try {
      const cartItems = await CartItem.query().where(idField, cartId).preload('product')
      let weight = await Product.getProductsWeight(cartItems.map((item) => ({ qty: item.qty, product: item.product })))

      return weight
    } catch (error) {
      console.error('getCartWeight:', error)

      const q = `select sum(cart_items.qty * IF(products.prod_weight > 0, products.prod_weight, 30)) as cartweight from cart_items left outer join products on products.prod_id = cart_items.prod_id where \`${idField}\` = ? limit ?`
      const [[{ cartweight }]]: any = await Database.rawQuery(q, [cartId, 1])

      return Math.ceil(cartweight)
    }
  }

  decodeBasketCookie(cookie: string) {
    if (!cookie) {
      return false
    }

    try {
      let decodedCookie = Buffer.from(cookie, 'base64').toString('utf8')
      let parsedC = JSON.parse(decodedCookie)

      return parsedC.message
    } catch (error) {
      console.error('decodeBasketCookie error: ', error)
    }
  }

  async getCartId(request: RequestContract, response, auth) {
    // console.log('getCartId request url: ', request.url())

    const { 'user-agent': userAgent } = request.headers()

    /*     if (isbot(userAgent) && userAgent != 'node-fetch') {
      return {}
    } */

    let sessionUser

    // if (request.headers()['authorization']) {
    //   try {
    //     sessionUser = await auth.use('api').authenticate()
    //   } catch (error) {
    //     console.log('error api auth: ', error)
    //   }
    // } else {
    //   try {
    //     let isAuth = await auth.check()
    //     //console.log('isAuth', isAuth)
    //     if (isAuth) {
    //       sessionUser = await auth.authenticate()
    //     }
    //   } catch (error) {}
    // }

    try {
      let isAuth = await auth.check()
      //console.log('isAuth', isAuth)
      if (isAuth) {
        sessionUser = await auth.authenticate()
      }
    } catch (error) {}

    if (response) {
      this.checkCookie({ request, response })
    }

    const idField = 'cart_id'

    let clientNumber: number | undefined = sessionUser?.client_number
    let _basketId = request.qs().id || this.decodeBasketCookie(request.cookieParser.cookies['_basket'])
    let clientIde: any = _basketId || clientNumber || this.getCartCookie()
    let findBy: string = _basketId ? 'cart_id' : clientNumber ? 'cart_client' : 'cart_cookie'

    // console.log("cart data: ", {_basketId, clientIde, findBy})

    let cart = await Cart.findBy(findBy, clientIde)
    let cartId: number = 0

    // legacy cart move
    try {
      if (cart) {
        let cartItems = JSON.parse(cart.cart_items)
        if (cartItems?.length) {
          let savedItems = cartItems.map((i) => ({ cart_id: cart?.cart_id, prod_id: i.id, qty: i.count }))

          CartItem.createMany(savedItems)
          cart.cart_items = ''

          await cart.save()
        }
      }
    } catch (error) {}

    if (!cart) {
      let createdCart = await Cart.create({ [findBy]: clientIde })
      cartId = createdCart[idField]
    } else {
      cartId = cart[idField]
    }

    if (!request.cookieParser.cookies['_basket'] && request.qs().id) {
      response.plainCookie('_basket', Number(_basketId), {
        maxAge: 30 * 24 * 60 * 60 * 1000,
        httpOnly: false,
        secure: false
      })
    }

    return { idField, cartId }
  }

  public static async checkStockAvailability(cartItems: Array<CartItem>) {
    let stockErrors: Array<qtyError> = []
    let stockWarns: Array<qtyError> = []
    let groupMsgs: Array<groupMessage> = []
    //let groups: Array<CartItem> = cartItems.filter(i => i.product.prod_group)
    let processedGroups: string[] = []
    let deletedItems: Array<CartItem> = []

    async function mergeGroup(cartItem: CartItem) {
      if (processedGroups.includes(cartItem.product.prod_group)) {
        return false
      }

      processedGroups.push(cartItem.product.prod_group)
      //console.log('processedGroups', processedGroups)

      let group = cartItems.filter((i) => i.product.prod_group == cartItem.product.prod_group)

      if (group.length > 1) {
        groupMsgs.push({
          group: cartItem.product.prod_group,
          skus: group.map((i) => i.product.prod_sku),
          isGroup: true
        })

        // Получаем общее запрашиваемое кол-во группы
        let totalQty = group.reduce((a, b) => a + b.qty, 0)

        // Если запрашиваемое кол-во больше чем наличие
        if (totalQty > cartItem.product.prod_count) {
          // Распределяем остатки по группе
          const remainder = Number(cartItem.product.prod_count) % group.length // Остаток деления
          const _qty = Math.round(Number(cartItem.product.prod_count) / group.length) // доступное кол-во для каждого элемента группы

          /*           console.log('prod_group count', cartItem.product.prod_count)
          console.log('totalQty', totalQty)
          console.log('cartItem.product.prod_count', cartItem.product.prod_count)
          console.log('group.length', group.length)
          console.log('remainder', remainder)
          console.log('_qty', _qty) */

          // Если наличия не хватает на все товары в группе, удаляем часть товаров из корзины
          if (group.length > cartItem.product.prod_count) {
            deletedItems = group.slice(cartItem.product.prod_count)
            group.length = cartItem.product.prod_count

            await Promise.all(
              deletedItems.map(async (deletedItem) => {
                stockErrors.push({ sku: deletedItem.product.prod_sku, query: deletedItem.qty, stock: 0, isDel: true })
                await deletedItem.delete()
              })
            )
          }

          // записываем доступное кол-во и последнему элементу + остаток деления
          await Promise.all(
            group.map(async (groupItem, index) => {
              const newQty = index + 1 == group.length ? _qty + remainder : _qty
              stockWarns.push({ sku: groupItem.product.prod_sku, query: groupItem.qty, stock: newQty })
              groupItem.qty = newQty

              await groupItem.save()
            })
          )
        }

        // Вносим обработаную группу
        processedGroups.push(cartItem.product.prod_group)
        //groups = groups.filter(i => i.product.prod_group != cartItem.product.prod_group)
      }
    }

    await Promise.all(
      cartItems.map(async (cartItem: CartItem) => {
        let product: Product = cartItem.product

        if (typeof cartItem.product == 'undefined') {
          console.log('PROD INDEF cartItem', cartItem)
        }

        if (cartItem.product.prod_group) {
          await mergeGroup(cartItem)
        }

        if (cartItem.qty > product.prod_count) {
          if (product.prod_count == 0) {
            stockErrors.push({ sku: product.prod_sku, query: cartItem.qty, stock: product.prod_count, isDel: true })

            await cartItem.delete()
          } else {
            stockWarns.push({ sku: product.prod_sku, query: cartItem.qty, stock: product.prod_count })
            cartItem.qty = product.prod_count

            await cartItem.save()
          }
        }
      })
    )

    return { stockErrors, stockWarns }
  }

  getCartCookie() {
    return this.cartCookie
  }
}
