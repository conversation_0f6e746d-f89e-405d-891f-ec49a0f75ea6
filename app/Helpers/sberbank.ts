
import { shippingInterface } from 'App/Interfaces/orders/shippingInterface';
import { sberCartItemInterface } from 'App/Interfaces/sberbank/sberCartItemInterface';
import { regOrderInterface } from 'App/Interfaces/sberbank/regOrderInterface';


import Env from "@ioc:Adonis/Core/Env";
import CartItem from "App/Models/CartItem";

const got = require('got')

const sberLogin = Env.get('SBER_USER')
const sberPwd = Env.get('SBER_PWD')
const apiUrl = 'https://securepayments.sberbank.ru/payment/rest/register.do' 


export class SberbankApi {
    
    constructor() {

    }

    public async regOrder(data: regOrderInterface) {

        const orderBundle = {
            customerDetails: {
                email: data.client.email,
                //phone: data.client.phone
            },
            cartItems: {
                items: this.prepareCartItems(data.cartItems, data.shipping)
            }
        }
        
        let query = this.objtourl({
            userName: sberLogin,
            password: sberPwd,
            amount: data.amount * 100,
            orderNumber: data.orderNumber,
            returnUrl: data.returnUrl || 'https://mirsalnikov.ru',
            pageView: data.pageView || 'DESKTOP',
            orderBundle: encodeURI(JSON.stringify(orderBundle))
        })

        //console.log('query: ', query)
        
        const { body } = await got.get(apiUrl + query)

        return body
    }

    private objtourl(obj: {}) {
        let str = '?'

        for (const [key, value] of Object.entries(obj)) {
            str+= `${key}=${value}&`
        }

        return str
    }

    private prepareCartItems(cartItems: Array<CartItem>, shipping?: shippingInterface): Array<sberCartItemInterface> {
        const sberCartItemInterfaces: Array<sberCartItemInterface> = cartItems.map((item, index) => {
            return {
                positionId: index + 1,
                name: item.product.prod_purpose,
                quantity: {
                    value: item.qty,
                    measure: 'шт.'
                },
                itemAmount: (Number(item.product.prod_price) * Number(item.qty)) * 100,
                itemPrice: Number(item.product.prod_price) * 100,
                itemCode: item.product.prod_analogsku + '_' + index,
                tax: {
                    taxType: 0
                }
            }
        })

        if (shipping) {
            sberCartItemInterfaces.push({
                positionId: sberCartItemInterfaces.length + 1,
                name: 'Доставка:' + shipping.method,
                quantity: {
                    value: 1,
                    measure: 'ед.'
                },
                itemAmount: Number(shipping.price) * 100,
                itemPrice: Number(shipping.price) * 100,
                itemCode: '0001',
                tax: {
                    taxType: 0
                }
            }) 
        }

        return sberCartItemInterfaces
    }
    
}