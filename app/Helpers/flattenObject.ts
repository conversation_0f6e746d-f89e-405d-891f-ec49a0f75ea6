export function flattenObject(ob: Record<string, any>, prefix = '', result: Record<string, any> | null = null, delimiter = '.'): Record<string, any> {
  result = result || {}

  // Сохраняем пустые объекты и массивы, чтобы они не потерялись
  if (prefix && typeof ob === 'object' && ob !== null && Object.keys(ob).length === 0) {
    result[prefix] = Array.isArray(ob) ? [] : {}
    return result
  }

  prefix = prefix ? prefix + delimiter : ''

  for (const i in ob) {
    if (Object.prototype.hasOwnProperty.call(ob, i)) {
      if (typeof ob[i] === 'object' && ob[i] !== null) {
        // Рекурсия для вложенных объектов
        flattenObject(ob[i], prefix + i, result, delimiter)
      } else {
        result[prefix + i] = ob[i]
      }
    }
  }
  return result
}
