export const ruSymbolsToEN = (...rest) => {
    rest = rest.map(i => typeof i == 'string' ? i.split('') : i)

    const dict = {
        'а': 'a',
        'в': 'b',
        'с': 'c',
        'е': 'e',
        'т': 't',
        'к': 'k',
        'н': 'h',
        'г': 'r',
        'х': 'x',
        'р': 'p',
        'о': 'o',
        'м': 'm',
        'ш': 'w'
    }

    return rest.flat().map(i => dict[String(i).toLowerCase()] || i)
}

// console.log(ruSymbolsToEN(['a','b']));
// console.log(ruSymbolsToEN('c','d','b'));

// console.log(ruSymbolsToEN('а','г','В'));

// console.log(ruSymbolsToEN('г222').join(''))

