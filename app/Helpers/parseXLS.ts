import Application from '@ioc:Adonis/Core/Application'
import Product from 'App/Models/Product'
import Category from 'App/Models/Category'
import { mergeObjects } from './mergeObject'
import Logger from '@ioc:Adonis/Core/Logger'
import { Exception } from '@adonisjs/core/build/standalone'

const standardBrand = 'TCS'
const dbfield = {
  sku: 'prod_sku',
  oem: 'prod_analogsku',
  brand: 'prod_manuf',
  qty: 'prod_count',
  group_qty: 'prod_group_count',
  category: 'prod_cat'
}

interface ParseParams {
  file: any
  uparams: {
    qty_column: number
    sku_column: number
    brand_column: number
  }
}

interface ProcessedProduct {
  qty: number
  sku: string
  _sku: string
  brand: string
}

const validateColumns = (columns: (number | string)[], maxColumns: number) => {
  return columns.every((col) => {
    const num = Number(col)
    return !Number.isNaN(num) && num > 0 && num <= maxColumns
  })
}

const handleSku = (sku: string): string => {
  try {
    if (typeof sku !== 'string') return ''
    return sku.replace(/^(\D{1})/, '$1-')
  } catch (error) {
    return sku
  }
}

const parseProductRow = (item: any[], params: ParseParams['uparams'], rowIndex: number): ProcessedProduct | null => {
  try {
    const maxColumn = Math.max(params.qty_column, params.sku_column, params.brand_column)
    if (item.length < maxColumn) {
      Logger.warn(`Row ${rowIndex} has insufficient columns`)
      return null
    }

    const getValue = (column: number, defaultValue: any = '') => {
      const value = item[column - 1]
      return value !== undefined && value !== null ? String(value) : defaultValue
    }

    const sku = getValue(params.sku_column, '').trim()
    const rawQty = item[params.qty_column - 1]
    const brand = getValue(params.brand_column, '').trim().toUpperCase()

    let qty = Number(rawQty)
    if (Number.isNaN(qty) || !Number.isFinite(qty)) {
      qty = 0
      Logger.warn(`Invalid quantity in row ${rowIndex}: ${rawQty}`)
    }

    if (!sku) {
      Logger.warn(`Empty SKU in row ${rowIndex}`)
      return null
    }

    return {
      sku,
      _sku: handleSku(sku),
      qty: Math.max(0, qty),
      brand: brand || standardBrand
    }
  } catch (error) {
    Logger.error(`Error processing row ${rowIndex}: ${error.message}`)
    return null
  }
}

const findProduct = async (prod: ProcessedProduct): Promise<Product | null> => {
  try {
    if (!prod.sku && !prod._sku) return null

    const { clientProductDBfields } = Product.getProductColumns()

    const query = Product.query()
      .select(clientProductDBfields)
      .where((builder) => {
        builder
          .where((qb) => {
            qb.where(dbfield.sku, prod.sku).orWhere(dbfield.sku, prod._sku).orWhere(dbfield.oem, prod.sku).orWhere(dbfield.oem, prod._sku)
          })
          .andWhere(dbfield.brand, prod.brand)
      })
      .orderBy([
        { column: dbfield.brand, order: 'desc' },
        { column: dbfield.qty, order: 'desc' },
        { column: dbfield.group_qty, order: 'desc' }
      ])
      .first()

    const product = await query
    if (product) {
      product.$attributes.qty = prod.qty
    }
    return product
  } catch (error) {
    Logger.error(`Error finding product ${prod.sku}: ${error.message}`)
    return null
  }
}

export const parseXLS = async (params: ParseParams): Promise<Product[]> => {
  try {
    if (!params.file) {
      throw new Exception('File is required', 400)
    }

    await params.file.move(Application.tmpPath('uploads'))

    if (!params.file.filePath) {
      throw new Exception('File upload failed', 500)
    }

    const XLSX = await import('xlsx')
    const workbook = XLSX.readFile(params.file.filePath) // Используем реальный путь к файлу
    const worksheet = workbook.Sheets[workbook.SheetNames[0]]
    const rows = XLSX.utils.sheet_to_json(worksheet, { header: 1, defval: null })

    if (rows.length < 1) {
      throw new Exception('Empty spreadsheet', 400)
    }

    // Validate column numbers using first row
    const maxColumns = rows[0].length
    const columnsToValidate = [params.uparams.qty_column, params.uparams.sku_column, params.uparams.brand_column]

    if (!validateColumns(columnsToValidate, maxColumns)) {
      throw new Exception('Invalid column numbers', 400)
    }

    const processedData = rows
      .slice(1)
      .map((row, index) => parseProductRow(row, params.uparams, index + 1))
      .filter((item): item is ProcessedProduct => item !== null)

    const mergedData = mergeObjects(processedData, '_sku', 'qty')

    const products = await Promise.all(
      mergedData.map(async (prod) => {
        try {
          return await findProduct(prod)
        } catch (error) {
          Logger.error(`Error processing product ${prod.sku}: ${error.message}`)
          return null
        }
      })
    )

    return products.filter((product): product is Product => product !== null)
  } catch (error) {
    Logger.error(`XLS parsing failed: ${error.message}`)
    throw error
  }
}
