// Базовые типы для Ozon Seller API

export interface OzonApiResponse<T = any> {
  result: T
  error?: {
    code: string
    message: string
    details?: any[]
  }
}

export interface OzonApiError {
  code: string
  message: string
  details?: any[]
}

// Типы для товаров
export interface OzonProduct {
  offer_id: string
  name: string
  description?: string
  category_id: number
  price: string
  old_price?: string
  premium_price?: string
  recommended_price?: string
  min_price?: string
  marketing_price?: string
  currency_code: string
  vat: string
  height?: number
  depth?: number
  width?: number
  dimension_unit?: string
  weight?: number
  weight_unit?: string
  images?: string[]
  image_group_id?: string
  primary_image?: string
  attributes: OzonProductAttribute[]
  complex_attributes?: OzonComplexAttribute[]
  color_image?: string
  last_id?: string
  pdf_list?: OzonPdfFile[]
  video?: string[]
}

export interface OzonProductAttribute {
  complex_id?: number
  id: number
  values: OzonAttributeValue[]
}

export interface OzonAttributeValue {
  dictionary_value_id?: number
  value: string
  unit_id?: number
}

export interface OzonComplexAttribute {
  attributes: OzonProductAttribute[]
}

export interface OzonPdfFile {
  file_name: string
  index: number
}

// Типы для остатков
export interface OzonStock {
  offer_id: string
  product_id?: number
  stock: number
  warehouse_id: number
}

export interface OzonStockUpdate {
  stocks: OzonStock[]
}

// Типы для цен
export interface OzonPrice {
  auto_action_enabled?: string
  currency_code: string
  min_price?: string
  offer_id: string
  old_price?: string
  premium_price?: string
  price: string
  product_id?: number
  recommended_price?: string
  marketing_price?: string
}

export interface OzonPriceUpdate {
  prices: OzonPrice[]
}

// Типы для изображений
export interface OzonImageUpload {
  file_name: string
  url?: string
}

export interface OzonImageUploadResponse {
  pictures: OzonUploadedImage[]
}

export interface OzonUploadedImage {
  file_name: string
  url: string
  state: 'uploaded' | 'processing' | 'failed'
  error?: string
}

// Типы для категорий
export interface OzonCategory {
  category_id: number
  title: string
  parent_category_id?: number
  children?: OzonCategory[]
  disabled?: boolean
}

export interface OzonCategoryTree {
  result: OzonCategory[]
}

// Типы для информации о товарах
export interface OzonProductInfo {
  id: number
  name: string
  offer_id: string
  barcode: string
  buybox_price: string
  category_id: number
  created_at: string
  images: OzonProductImage[]
  marketing_price: string
  min_ozon_price: string
  min_price: string
  old_price: string
  premium_price: string
  price: string
  recommended_price: string
  sources: OzonProductSource[]
  state: string
  stocks: OzonProductStock
  errors: OzonProductError[]
  vat: string
  visible: boolean
  visibility_details: OzonVisibilityDetails
  price_index: string
  images360: any[]
  color_image: string
  primary_image: string
  status: OzonProductStatus
}

export interface OzonProductImage {
  file_name: string
  default: boolean
  index: number
}

export interface OzonProductSource {
  is_enabled: boolean
  sku: number
  source: string
}

export interface OzonProductStock {
  coming: number
  present: number
  reserved: number
}

export interface OzonProductError {
  code: string
  field: string
  message: string
}

export interface OzonVisibilityDetails {
  has_price: boolean
  has_stock: boolean
  active_product: boolean
}

export interface OzonProductStatus {
  state: string
  state_failed_reason: string
  moderate_status: string
  decline_reasons: string[]
  validation_state: string
  state_name: string
  state_description: string
  is_failed: boolean
  is_created: boolean
  state_tooltip: string
  item_errors: OzonItemError[]
  state_updated_at: string
}

export interface OzonItemError {
  code: string
  field: string
  message: string
  level: string
  description: string
}

// Типы для запросов
export interface OzonProductImportRequest {
  items: OzonProduct[]
}

export interface OzonProductInfoRequest {
  offer_id?: string[]
  product_id?: number[]
  sku?: number[]
  last_id?: string
  limit?: number
}

export interface OzonStockInfoRequest {
  filter: {
    offer_id?: string[]
    product_id?: number[]
    visibility?: string
  }
  last_id?: string
  limit?: number
}

// Настройки интеграции
export interface OzonSettings {
  ozon_api_key: string
  ozon_client_id: string
  ozon_api_url: string
  ozon_warehouse_id: number
  ozon_sync_enabled: boolean
  ozon_auto_sync_interval: number
  ozon_default_category_id: number
  ozon_image_upload_enabled: boolean
  ozon_image_base_url: string
  ozon_max_images_per_product: number
}

// Типы для логирования
export interface OzonSyncLog {
  id?: number
  product_id: number
  action: 'create' | 'update' | 'sync' | 'upload_image' | 'update_stock' | 'update_price'
  status: 'success' | 'error' | 'pending'
  error_message?: string
  ozon_product_id?: number
  created_at?: Date
}

// Типы для маппинга данных
export interface ProductToOzonMapping {
  prod_sku: string
  prod_note: string
  prod_price: number
  prod_count: number
  prod_images: string
  prod_manuf: string
  prod_cat: string
  prod_size: string
  prod_weight: string
}

// Enum для статусов
export enum OzonProductState {
  IMPORTED = 'imported',
  PROCESSED = 'processed',
  MODERATING = 'moderating',
  DECLINED = 'declined',
  APPROVED = 'approved',
  ARCHIVED = 'archived',
  FAILED_MODERATION = 'failed_moderation',
  FAILED_VALIDATION = 'failed_validation'
}

export enum OzonSyncAction {
  CREATE = 'create',
  UPDATE = 'update',
  SYNC = 'sync',
  UPLOAD_IMAGE = 'upload_image',
  UPDATE_STOCK = 'update_stock',
  UPDATE_PRICE = 'update_price'
}

export enum OzonSyncStatus {
  SUCCESS = 'success',
  ERROR = 'error',
  PENDING = 'pending'
}
