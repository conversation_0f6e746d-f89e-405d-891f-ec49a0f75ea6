export interface rtiOrder {
    address?: {
        country: string
        location: string
        street: string
        house: string
        flat?: string
        index?: string
    },
    client?: {
        name: string
        email: string
        phone: string
        org?: {
            name: string
            address: string
            inn: string
            kpp: string
            rschet: string
            kschet: string
            bik: string
            bank: string
            meta: string
            vat: string
        }
    },
    shipping: {
        method: string
        price: number
    },
    payment: {
        method: string
    },
    notice?: string,
    // orderItems: Array<{qty: Number, prod_id: Number}>
}