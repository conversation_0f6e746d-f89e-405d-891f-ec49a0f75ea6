import Client from "App/Models/Client"
import { SnapshotBodyItemInterface } from "App/Interfaces/orders/snapshotBodyItem"
// import Product from "App/Models/Product"

interface CouponInterface {
    discountVal: number
    personal: number
    bigDisc?: number
}

export interface SnapshotBodyInterface {
  client: Client

  items: SnapshotBodyItemInterface[]
  clientcache: Clientcache

  orderDate: string
  orderDateTime: string
  order_client: string
  order_clienttype: string
  order_company: string
  order_coupons: CouponInterface
  order_datetime: string
  order_desc: string
  order_endprice: number
  order_gtd: number
  order_id: number
  order_lastupdate: string
  order_lastupdate_person: number
  order_locale: string
  order_notice: string
  order_payment: string
  order_price: number
  order_shipping: string
  order_shippingprice: number
  order_status: string
  order_tracknumber: string
  whosalePrices?: boolean
  defsum?: number
}

export interface ClientCompany {
  org_id: number
  org_client: string
  org_name: string
  org_adress: string
  org_inn: string
  org_kpp: string
  org_rschet: string
  org_kschet: string
  org_bik: string
  org_bank: string
  org_vat: string
  created_at: string
  updated_at: string
}

export interface Clientcache {
  client_name: string
  client_mail: string
  client_phone: string
  client_city: string
  client_country: string
  client_street: string
  client_house: string
  client_flat: string
  client_postindex: string
  org: Org2
  password: string
  client_company: ClientCompany
  client_number: number
  client_id: number
}

export interface Org2 {
  org_name: string
  org_adress: string
  org_inn: string
  org_rschet: string
  org_kschet: string
  org_bik: string
  org_bank: string
  org_vat: string
  org_client: number
}