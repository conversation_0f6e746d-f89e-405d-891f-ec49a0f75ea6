import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { randomInteger } from 'App/Helpers/randomInteger'
import User from 'App/Models/User'
import { $prisma } from 'App/Services/Prisma'
import { productProvider } from './ProductProvider'

import { getCartSum } from '@prisma/client/sql'
import Env from '@ioc:Adonis/Core/Env'
import loadSettings from 'App/Helpers/loadSettings'

const discountStartSum = Number(Env.get('DISCOUNT_START_SUM')) //|| 7500
const bigDiscountStartSum = Number(Env.get('BIG_DISCOUNT_START_SUM')) //|| 100000
const bigDiscountValue = Number(Env.get('BIG_DISCOUNT_VALUE')) //|| 10

interface GetCartIdProps {
  p
  ctx: HttpContextContract
  sessionUser?: User
  autoCreate?: boolean
}

interface GetCartProductsProps {
  ctx: HttpContextContract
  sessionUser?: User
  page?: number
  sorting?: Array<{ column: string; direction: 'asc' | 'desc' }>
}

type StateItem = {
  prodId: number
  qty: number
}

class CartProvider {
  db = $prisma.cart
  cartItemsDB = $prisma.cart_items
  productsDB = $prisma.products
  clientsDB = $prisma.clients

  constructor() {}

  //!! deprecated
  async checkCookie({ response, request, auth }: HttpContextContract) {
    const locale = request.headers()['x-locale'] || undefined

    let cartCookie = request.cookiesList()._cart

    if (!cartCookie) {
      cartCookie = Buffer.from(randomInteger(3 ** 9, 9 ** 9)).toString('base64')
      response.plainCookie('_cart', cartCookie, { maxAge: 30 * 24 * 60 * 60 * 1000 })
    }
  }

  //! duplicate from Cart model
  decodeBasketCookie(cookie: string) {
    if (!cookie) {
      return false
    }

    try {
      let decodedCookie = Buffer.from(cookie, 'base64').toString('utf8')
      let parsedC = JSON.parse(decodedCookie)

      return parsedC.message
    } catch (error) {
      console.error('decodeBasketCookie error: ', error)
    }
  }

  async getCartWeight({ cartId }: { cartId: number }) {
    const cartItems = await this.getCartItems({ cartId })

    const productsWithWeight = await this.productsDB.findMany({
      select: {
        prod_id: true,
        prod_weight: true
      }
    })

    // Рассчитываем общий вес корзины
    const totalWeight = cartItems.reduce((acc, item) => {
      const product = productsWithWeight.find((p) => p.prod_id === item.prod_id)
      const weight = product ? Number.parseFloat(product?.prod_weight ?? '10') || 10 : 10
      return acc + (item?.qty || 1) * weight
    }, 0)

    return Number(totalWeight.toFixed(2)) // Округляем до 2 знаков после запятой
  }

  async getCartItems({ cartId }: { cartId: number }) {
    if (!cartId) {
      return false
      // throw Error('cartId is required')
    }
    const cartItems = await this.cartItemsDB.findMany({
      where: {
        cart_id: cartId
      },
      select: {
        prod_id: true,
        qty: true
      }
    })

    return cartItems
  }

  async getCartCount(cartId: number) {
    if (!cartId) {
      return 0
    }

    const cartItemsCount = await this.cartItemsDB.count({
      where: {
        cart_id: cartId
      },
      select: {
        _all: true
      }
    })

    return cartItemsCount?._all || 0
  }

  async broken__getCartId({ ctx, sessionUser, autoCreate = false }: GetCartIdProps) {
    const { request, auth, response } = ctx

    //! check auth
    // try {
    //   let isAuth = await auth.check()
    //   ////console.log('isAuth', isAuth)
    //   if (isAuth) {
    //     sessionUser = await auth.authenticate()
    //   }
    // } catch (error) {}

    //!deprecated ?
    // if (response) {
    //   this.checkCookie({ request, response })
    // }

    function getCartCookie() {
      if (!request.cookieParser.cookies['_cart']) {
        return Buffer.from(randomInteger(3 ** 9, 9 ** 9)).toString('base64')
      }
      return request.cookieParser.cookies._cart
    }

    const idField = 'cart_id'

    let clientNumber: number | undefined = sessionUser?.client_number
    let _basketId = request.qs().id || this.decodeBasketCookie(request.cookieParser.cookies['_basket'])
    let clientIde: any = _basketId || clientNumber || getCartCookie()
    let findBy: string = _basketId ? 'cart_id' : clientNumber ? 'cart_client' : 'cart_cookie'

    //console.log('🚀 ~ getCartId ~ _basketId:', _basketId)
    //console.log('🚀 ~ getCartId ~ findBy:', findBy)
    //console.log('🚀 ~ getCartId ~ clientIde:', clientIde)

    if (!request.cookieParser.cookies['_basket'] && request.qs().id) {
      response.plainCookie('_basket', Number(_basketId), {
        maxAge: 30 * 24 * 60 * 60 * 1000,
        httpOnly: false,
        secure: false
      })
    }

    let cartId: number | undefined

    // //console.log("cart data: ", {_basketId, clientIde, findBy})

    // let cart = await Cart.findBy(findBy, clientIde)
    //console.log('load cart..')

    const cart = await this.db.findFirst({
      select: {
        cart_client: true,
        cart_cookie: true,
        cart_id: true
      },
      where: {
        [findBy]: clientIde
      }
    })
    //console.log('🚀 ~ !!!!getCartId ~ cart:', cart)

    if (cart?.cart_id) {
      cartId = cart.cart_id
    }

    if (!cart && autoCreate) {
      // let createdCart = await Cart.create({ [findBy]: clientIde })
      const createdCart = await this.db.create({
        data: {
          [findBy]: clientIde,
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      cartId = createdCart.cart_id //createdCart[idField]
    }

    if (!cartId) {
      //console.log('getCartId: cart not founed, return undefined')

      return undefined
    }

    //console.log('getCardId res: ', cartId)

    return cartId
  }

  async getCartId({ ctx, sessionUser, autoCreate = false }: GetCartIdProps) {
    const { request, response } = ctx

    request.cookiesList()

    // //console.log('🚀 ~ getCartId ~ request.toJSON():', request.toJSON())
    //console.log('🚀 ~ getCartId ~ request.cookiesList()_cart:', request.cookiesList()?._cart)

    let cartId: number | undefined
    let findBy: string
    let clientIde: string | number

    let clientNumber: number | undefined = sessionUser?.client_number
    //console.log('🚀 ~ getCartId ~ clientNumber:', clientNumber)
    let _basketId = request.qs().id || this.decodeBasketCookie(request.cookiesList()._basket)
    let cartCookie = request.cookiesList()._cart

    if (_basketId) {
      findBy = 'cart_id'
      clientIde = _basketId
    } else if (clientNumber) {
      findBy = 'cart_client'
      clientIde = String(clientNumber)
    } else {
      findBy = 'cart_cookie'

      // Используем существующую куку или создаем новую только если её нет
      if (!cartCookie) {
        cartCookie = Buffer.from(randomInteger(3 ** 9, 9 ** 9)).toString('base64')
        response.plainCookie('_cart', cartCookie, {
          maxAge: 30 * 24 * 60 * 60 * 1000,
          domain: ''
        })
      }
      clientIde = cartCookie
    }

    let cart = await this.db.findFirst({
      select: {
        cart_client: true,
        cart_cookie: true,
        cart_id: true
      },
      where: {
        [findBy]: clientIde
      }
    })

    if (cart?.cart_id) {
      cartId = cart.cart_id
    } else if (autoCreate || findBy === 'cart_client') {
      const createdCart = await this.db.create({
        data: {
          [findBy]: clientIde,
          created_at: new Date(),
          updated_at: new Date()
        }
      })
      cartId = createdCart.cart_id
    }

    //console.log('getCartId returned: ', cartId)

    return cartId
  }

  // async getCartProducts({ ctx, sessionUser, page = 1 }: GetCartProductsProps) {
  // let cartId = await this.getCartId({ ctx: ctx, sessionUser, autoCreate: false })

  async getCartSum({ cartId = 0 }) {
    //!! TODO: get discount from db (loadSettings)
    //!! const { DISCOUNT_START_SUM } = await loadSettings(['DISCOUNT_START_SUM'])

    let [{ cartsum, defsum }] = await $prisma.$queryRawTyped(getCartSum(cartId, discountStartSum, cartId))

    const cart = await this.db.findFirst({
      where: {
        cart_id: cartId
      },
      select: {
        cart_client: true
      }
    })

    let client

    if (cart?.cart_client) {
      client = await this.clientsDB.findFirst({
        where: {
          client_number: Number(cart.cart_client)
        }
      })
    }

    cartsum = Number(cartsum)
    defsum = Number(defsum)

    if (!cartsum || !defsum) {
      return {
        sum: 0,
        discountValue: 0,
        whosalePrices: false,
        defsum,
        bigDiscount: false,
        bigDiscountValue: 0,
        personalDiscountValue: 0
      }
    }

    let discountValue = 0
    let whosalePrices = defsum > discountStartSum
    let bigDiscount = cartsum > bigDiscountStartSum
    let personalDiscountValue = client?.client_discount || 0

    if (whosalePrices && personalDiscountValue && !bigDiscount) {
      discountValue += (cartsum / 100) * personalDiscountValue
      cartsum -= discountValue
    }

    if (bigDiscount) {
      discountValue = (cartsum / 100) * (bigDiscountValue > personalDiscountValue ? bigDiscountValue : personalDiscountValue)
      cartsum -= discountValue
    }

    return {
      sum: cartsum ? Number(cartsum.toFixed(2)) : 0,
      discountValue,
      whosalePrices,
      defsum,
      bigDiscount,
      bigDiscountValue,
      personalDiscountValue
    }
  }
  /**
   * Получает товары корзины с поддержкой сортировки через Prisma
   * @param cartId - ID корзины
   * @param page - номер страницы
   * @param sorting - массив параметров сортировки
   * @returns данные товаров корзины с примененной сортировкой
   */
  async getCartProducts({ cartId = 0, page = 1, sorting = [] }: { cartId?: number; page?: number; sorting?: Array<{ column: string; direction: 'asc' | 'desc' }> }) {
    if (!cartId) {
      //console.log('getCartProducts cartId error:', cartId)
      return null
    }

    // Строим orderBy для Prisma на основе параметров сортировки
    const orderBy = this.buildPrismaOrderBy(sorting)

    // Получаем элементы корзины с сортировкой через Prisma
    const cartItems = await this.cartItemsDB.findMany({
      where: {
        cart_id: cartId
      },
      orderBy: orderBy
    })

    if (!cartItems.length) {
      return { categories: [], meta: { totalCount: 0 } }
    }

    // Получаем ID товаров для дальнейшего запроса
    const productIds = cartItems.map(item => item.prod_id).filter(Boolean) as number[]

    if (!productIds.length) {
      return { categories: [], meta: { totalCount: 0 } }
    }

    // Получаем данные товаров
    const productsData = await this.productsDB.findMany({
      where: {
        prod_id: { in: productIds }
      }
    })

    // Создаем мапу товаров для быстрого доступа
    const productsMap = new Map()
    productsData.forEach(product => {
      productsMap.set(product.prod_id, product)
    })

    // Объединяем данные корзины и товаров, сохраняя порядок сортировки
    let products = cartItems.map((cartItem) => {
      const product = productsMap.get(cartItem.prod_id)
      if (product) {
        // Добавляем информацию из корзины к товару
        return {
          ...product,
          qty: cartItem.qty,
          addedAt: cartItem.created_at,
          updatedAt: cartItem.updated_at
        }
      }
      return null
    }).filter(Boolean)

    // Применяем дополнительную сортировку по полям товаров, если необходимо
    const productFieldsSorting = sorting.filter(sort =>
      !['created_at', 'updated_at', 'qty', 'addedAt', 'date_added'].includes(sort.column)
    )

    if (productFieldsSorting.length > 0) {
      products = this.applySortingToProducts(products, productFieldsSorting)
    }

    // Группируем товары по категориям (как в ProductProvider)
    const categoriesMap = new Map()

    for (const product of products) {
      const categoryId = parseInt(product.prod_cat) // преобразуем в число
      if (!categoriesMap.has(categoryId)) {
        categoriesMap.set(categoryId, {
          categoryId: categoryId,
          categoryTitle: '', // будет заполнено позже
          products: []
        })
      }
      categoriesMap.get(categoryId).products.push(product)
    }

    const categories = Array.from(categoriesMap.values())

    // Получаем названия категорий
    if (categories.length > 0) {
      const categoryIds = categories.map(cat => cat.categoryId)
      const categoryData = await $prisma.cats.findMany({
        where: {
          cat_id: { in: categoryIds }
        },
        select: {
          cat_id: true,
          cat_title: true
        }
      })

      // Заполняем названия категорий
      categories.forEach(category => {
        const catData = categoryData.find(c => c.cat_id === category.categoryId)
        if (catData) {
          category.categoryTitle = catData.cat_title
        }
      })
    }

    return {
      categories,
      meta: {
        totalCount: products.length
      }
    }
  }

  /**
   * Строит объект orderBy для Prisma на основе параметров сортировки
   * Для полей товаров сортировка будет применена после получения данных
   * @param sorting - массив параметров сортировки
   * @returns объект orderBy для Prisma (только для полей cart_items)
   */
  private buildPrismaOrderBy(sorting: Array<{ column: string; direction: 'asc' | 'desc' }>): any[] {
    if (!sorting || sorting.length === 0) {
      // Сортировка по умолчанию - по дате добавления в корзину (новые сначала)
      return [{ created_at: 'desc' }]
    }

    // Фильтруем только те поля, которые есть в таблице cart_items
    const cartItemsFields = ['created_at', 'updated_at', 'qty', 'addedAt', 'date_added']

    const cartItemsSort = sorting.filter(sort =>
      cartItemsFields.includes(sort.column) ||
      sort.column === 'addedAt' ||
      sort.column === 'date_added'
    )

    if (cartItemsSort.length === 0) {
      // Если нет полей для сортировки на уровне cart_items, используем дату по умолчанию
      return [{ created_at: 'desc' }]
    }

    return cartItemsSort.map(sort => {
      switch (sort.column) {
        case 'addedAt':
        case 'date_added':
          return { created_at: sort.direction }
        case 'qty':
          return { qty: sort.direction }
        case 'created_at':
          return { created_at: sort.direction }
        case 'updated_at':
          return { updated_at: sort.direction }
        default:
          return { created_at: 'desc' }
      }
    })
  }

  /**
   * Применяет сортировку к массиву товаров корзины
   * @param products - массив товаров для сортировки
   * @param sorting - параметры сортировки
   * @returns отсортированный массив товаров
   */
  private applySortingToProducts(products: any[], sorting: Array<{ column: string; direction: 'asc' | 'desc' }>): any[] {
    return products.sort((a, b) => {
      for (const sortRule of sorting) {
        let result = 0

        // Обработка специальных случаев сортировки
        switch (sortRule.column) {
          case 'prod_size':
            // Сложная сортировка по размеру (как в ProductProvider)
            result = this.compareSizes(a, b)
            break

          case 'prod_price':
            // Сортировка по цене
            const priceA = Number(a.prod_price) || 0
            const priceB = Number(b.prod_price) || 0
            result = priceA - priceB
            break

          case 'prod_count':
            // Сортировка по количеству на складе
            const countA = Number(a.prod_count) || 0
            const countB = Number(b.prod_count) || 0
            result = countA - countB
            break

          case 'prod_name':
            // Сортировка по названию товара
            const nameA = String(a.prod_name || '').toLowerCase()
            const nameB = String(b.prod_name || '').toLowerCase()
            result = nameA.localeCompare(nameB, 'ru')
            break

          default:
            // Обычная сортировка по строковым полям
            const valueA = String(a[sortRule.column] || '').toLowerCase()
            const valueB = String(b[sortRule.column] || '').toLowerCase()
            result = valueA.localeCompare(valueB, 'ru')
            break
        }

        // Применяем направление сортировки
        if (sortRule.direction === 'desc') {
          result = -result
        }

        // Если значения не равны, возвращаем результат
        if (result !== 0) {
          return result
        }
      }

      // Если все критерии сортировки дали одинаковый результат,
      // используем дату добавления как fallback
      const dateA = new Date(a.addedAt || 0).getTime()
      const dateB = new Date(b.addedAt || 0).getTime()
      return dateB - dateA // новые сначала
    })
  }

  /**
   * Сравнивает размеры товаров для сортировки (аналогично ProductProvider)
   * @param a - первый товар
   * @param b - второй товар
   * @returns результат сравнения
   */
  private compareSizes(a: any, b: any): number {
    // Извлекаем размеры из строки prod_size
    const sizeA = this.parseSizeString(a.prod_size || '')
    const sizeB = this.parseSizeString(b.prod_size || '')

    // Сравниваем по внутреннему диаметру
    if (sizeA.sizeIn !== sizeB.sizeIn) {
      return sizeA.sizeIn - sizeB.sizeIn
    }

    // Если внутренние диаметры равны, сравниваем по внешнему
    if (sizeA.sizeOut !== sizeB.sizeOut) {
      return sizeA.sizeOut - sizeB.sizeOut
    }

    // Если внешние диаметры равны, сравниваем по высоте
    return sizeA.sizeHeight - sizeB.sizeHeight
  }

  /**
   * Парсит строку размера в объект с числовыми значениями
   * @param sizeString - строка размера (например, "10x20x5")
   * @returns объект с размерами
   */
  private parseSizeString(sizeString: string): { sizeIn: number; sizeOut: number; sizeHeight: number } {
    const defaultSize = { sizeIn: 0, sizeOut: 0, sizeHeight: 0 }

    if (!sizeString) return defaultSize

    // Разбираем строку размера по различным разделителям
    const parts = sizeString.split(/[x×*\-\/]/).map(part => {
      const num = parseFloat(part.trim())
      return isNaN(num) ? 0 : num
    })

    return {
      sizeIn: parts[0] || 0,
      sizeOut: parts[1] || 0,
      sizeHeight: parts[2] || 0
    }
  }

  async push(cartId, item: StateItem) {
    // await this.cartItemsDB.upsert({
    //   create: {
    //     cart_id: cartId,
    //     prod_id: item.prodId,
    //     qty: item.qty
    //   },
    //   update: {
    //     qty: item.qty
    //   },
    //   where: {
    //     id: 0,
    //     cart_id: cartId,
    //     prod_id: item.prodId
    //   }
    // })

    if (item.qty === 0) {
      return await this.cartItemsDB.deleteMany({
        where: {
          cart_id: cartId,
          prod_id: item.prodId
        }
      })
    }

    const cartItem = await this.cartItemsDB.findFirst({
      where: {
        cart_id: cartId,
        prod_id: item.prodId
      }
    })

    if (cartItem) {
      return await this.cartItemsDB.update({
        where: {
          id: cartItem.id
        },
        data: {
          qty: item.qty,
          updated_at: new Date()
        }
      })
    }

    return await this.cartItemsDB.create({
      data: {
        cart_id: cartId,
        prod_id: item.prodId,
        qty: item.qty,
        updated_at: new Date()
      }
    })
  }

  async remove(cartId, item: StateItem) {
    return await this.cartItemsDB.deleteMany({
      where: {
        cart_id: cartId,
        prod_id: item.prodId
      }
    })
  }

  async syncState(cartId, items: StateItem[]) {}
}

export const cartProvider = new CartProvider()
