import { products, cat_columns, filters, cats, Prisma } from '@prisma/client'
import { isSize } from 'App/Helpers/isSize'
import { searchValueHandler } from 'App/Helpers/searchValueHandler'
import Product from 'App/Models/Product'
import { MeiliSearchPlugin } from '../Plugins/MeiliSearch'
import { $prisma } from 'App/Services/Prisma'
import { MeiliSearch } from 'meilisearch'
import { PageNumberCounters, PageNumberPagination } from 'prisma-extension-pagination/dist/types'
import { z } from 'zod'

const SEARCH_FIELDS = ['prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_note', 'prod_year', 'prod_type', 'prod_uses', 'prod_purpose', 'prod_analogs', 'prod_model']

const FULLTEXT_SEARCH_FIELDS = [
  'prod_analogsku',
  'prod_sku',
  'prod_manuf',
  'prod_note',
  'prod_model',
  'prod_type',
  'prod_uses',
  'prod_purpose',
  'prod_analogs',
  'prod_material',
  'prod_secret',
  'prod_supplier'
]

const SIZE_TOLERANCE = 0.3
const GLOBAL_CATEGORY_ID = 9999

export type ProductWithMeta = {
  data: products[]
  meta: PageNumberPagination & PageNumberCounters
}

// Interfaces
interface Size {
  sizeIn: number
  sizeOut: number
  sizeHeight: number
  sizeIn_2?: number
  sizeOut_2?: number
  sizeHeight_2?: number
}

interface CategoryData {
  categoryId: string
  categoryTitle: string
  columns: cat_columns[]
  products: Product[]
  filters?: filters[]
}

export const ProductPaginateParamsSchema = z.object({
  page: z.number().optional(),
  limit: z.number().optional(),
  sortBy: z.string().optional(),
  order: z.string().optional(),
  sorting: z.array(z.any()).optional(),
  filters: z.any().optional()
})

export const FindByCategoryIdParamsSchema = z.object({
  identifier: z.string().or(z.number()),
  search: z.string().max(50).optional(),
  ...ProductPaginateParamsSchema.shape
})

export const GlobalSearchParamsSchema = z.object({
  searchvalue: z.string().max(70).optional(),
  ...ProductPaginateParamsSchema.shape
})

export type ProductPaginateParams = z.infer<typeof ProductPaginateParamsSchema>
export type FindByCategoryIdParams = z.infer<typeof FindByCategoryIdParamsSchema>
export type GlobalSearchParams = z.infer<typeof GlobalSearchParamsSchema>

const { clientProductDBfields } = Product.getProductColumns()
const searchFields = ['prod_sku', 'prod_analogsku', 'prod_manuf', 'prod_note', 'prod_year', 'prod_type', 'prod_uses', 'prod_purpose', 'prod_analogs', 'prod_model'] //clientProductDBfields.map((i) => i.split('.')[1])
const fulltextSearchFields = [
  'prod_analogsku',
  'prod_sku',
  'prod_manuf',
  'prod_note',
  'prod_model',
  'prod_type',
  'prod_uses',
  'prod_purpose',
  'prod_analogs',
  'prod_material',
  'prod_secret',
  'prod_supplier'
]

const fields = [
  // {
  //   keyname: 'prod_purpose',
  //   title: 'Назначение'
  // },
  {
    keyname: 'prod_sku',
    title: 'Артикул'
  },
  {
    keyname: 'prod_analogsku',
    title: 'Аналог'
  },
  {
    keyname: 'prod_size',
    title: 'Размер',
    searchable: true
  },
  {
    keyname: 'prod_manuf',
    title: 'Производитель',
    searchable: true
  },
  {
    keyname: 'prod_year',
    title: 'Год'
  },
  {
    keyname: 'prod_type',
    title: 'Тип',
    searchable: true
  },
  {
    keyname: 'prod_uses',
    title: 'Применение',
    searchable: true
  },

  {
    keyname: 'prod_analogs',
    title: 'Аналоги',
    type: 'array'
  },
  {
    keyname: 'prod_model',
    title: 'Модель',
    searchable: true
  },
  {
    keyname: 'prod_note',
    title: 'Описание',
    type: 'html'
  }
]

type FindByCategoryIdParamsReponse = {
  products: {
    data: Product[]
    meta: PageNumberPagination & PageNumberCounters
  }
  category: {
    columns: cat_columns[]
    filters: filters[]
    [key in cats]: string
  }
}

class ProductProvider {
  db = $prisma.products
  catDB = $prisma.cats
  schemaDB = $prisma.product_schemas
  meiliDB: MeiliSearch

  constructor() {
    this.meiliDB = new MeiliSearch({
      host: 'http://localhost:7700',
      apiKey: 'aSampleMasterKey'
    })
  }

  transformToAdonisModel(rawProducts: Partial<products>[]) {
    // const rmFields = ['prod_group_price']

    return rawProducts.map((item) => {
      const productOverModel = new Product()
      //   productOverModel.$isDirty = false
      //   productOverModel.$isNew = false
      productOverModel.fill(item)

      return productOverModel
    })
  }

  async initAndTransformToAdonisModel(rawProducts: Partial<products>[]) {
    const adonisProducts = this.transformToAdonisModel(rawProducts)
    await Product.productInit(adonisProducts)
    return adonisProducts
  }

  getPublicFieldsObject() {
    const { clientProductDBfields } = Product.getProductColumns()
    const obj = {}

    clientProductDBfields.forEach((item) => {
      obj[item.split('.')[1]] = true
    })

    return obj
  }

  async filterDataByIdentifierMeili({ identifier, search = null, filters = null }) {
    //console.time('filterDataByIdentifierMeili: ' + identifier)

    const identifiers = Array.isArray(identifier) ? [...new Set(identifier)] : [identifier]
    let categories = []
    let filterConditions = {}

    await Promise.all(
      identifiers.map(async (id) => {
        const category = await this.findCategoryByIdentifier(id)
        if (category) {
          categories.push(category)
        }
      })
    )

    if (filters) {
      try {
        const parsedFilters = typeof filters === 'string' ? JSON.parse(decodeURIComponent(filters)) : filters

        Object.keys(parsedFilters).forEach((key) => {
          if (parsedFilters[key]?.length) {
            filterConditions[key] = parsedFilters[key]
          }
        })
      } catch (error) {
        console.log('Error parsing filters:', error)
      }
    }

    const filterList = await this.meiliDB.index('filters').search('', {
      filter: [`category_id IN [${[...categories.map((cat) => Number(cat.cat_id)), 9999]}]`, 'field != bysize'],
      limit: 500
    })

    const values: {
      [key: string]: {
        values: string[]
        title: string
      }
    } = {}

    // ОБРАБОТКА searchQuery для удаления размеров!!!!!!!!
    await Promise.all(
      filterList.hits.map(async (filter) => {
        const searchQuery = search || ''
        const productsWithFilter = await this.meiliDB.index('products').search(searchQuery, {
          filter: [`prod_cat IN [${categories.map((cat) => String(cat.cat_id))}]`, ...Object.entries(filterConditions).map(([key, values]) => `${key} IN [${values}]`)],
          attributesToRetrieve: [filter.field],
          distinct: filter.field,
          sort: ['prod_count:desc', `${filter.field}:asc`],
          matchingStrategy: 'frequency',
          limit: 500
        })

        values[filter.field] = {
          values: productsWithFilter.hits.map((product) => product[filter.field]),
          title: filter.title
        }
      })
    )

    //console.timeEnd('filterDataByIdentifierMeili: ' + identifier)
    return values
  }

  async filterDataByIdentifier({ identifier, search = null, filters = null }) {
    //console.time('filterDataByIdentifier: ' + identifier)

    const identifiers = Array.isArray(identifier) ? [...new Set(identifier)] : [identifier]
    let categories = []
    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    if (search) {
      searchWhere = this.makeSearchWhere({ filters, input: search })
      // console.log('🚀 ~ ProductProvider ~ filterDataByIdentifier ~ searchWhere:', searchWhere)
    }

    if (filters) {
      try {
        if (typeof filters === 'string') {
          filters = JSON.parse(decodeURIComponent(filters))
        }
      } catch (error) {}

      filtersWhere = this.makeFiltersWhere(filters)
      // console.log('🚀 ~ ProductProvider ~ filterDataByIdentifier ~ filtersWhere:', filtersWhere)
    }

    // Get all requested categories
    // for (const id of identifiers) {
    //   const category = await this.findCategoryByIdentifier(id)
    //   if (category) {
    //     categories.push(category)
    //   }
    // }

    await Promise.all(
      identifiers.map(async (id) => {
        const category = await this.findCategoryByIdentifier(id)
        if (category) {
          categories.push(category)
        }
      })
    )

    const filterList = await $prisma.filters.findMany({
      where: {
        category_id: {
          in: [...categories.map((cat) => Number(cat.cat_id)), 9999]
        },
        field: {
          not: 'bysize'
        }
      },
      distinct: 'field',
      orderBy: {
        id: 'desc'
      }
    })

    const values: {
      [key: string]: {
        values: string[]
        title: string
      }
    } = {}

    await Promise.all(
      filterList.map(async (filter) => {
        const res = await this.db.groupBy({
          take: 500,
          by: [filter.field],
          where: {
            AND: [
              {
                prod_cat: {
                  in: categories.map((cat) => String(cat.cat_id))
                }
              },
              searchWhere ?? {},
              filtersWhere ?? {}
            ]
          },
          orderBy: {
            _count: {
              [filter.field]: 'desc'
            }
          }
        })

        values[filter.field] = {
          values: res.map((i) => i[filter.field]),
          title: filter.title
        }
      })
    )

    //console.timeEnd('filterDataByIdentifier: ' + identifier)

    return values
  }

  async findCategoryByIdentifier(identifier: string | number) {
    // console.log('🚀 ~ ProductProvider ~ findCategoryByIdentifier ~ identifier:', identifier)
    const category = await this.catDB.findFirst({
      where: {
        OR: [
          {
            cat_url_de: String(identifier)
          },
          {
            cat_url_en: String(identifier)
          },
          {
            cat_url_es: String(identifier)
          },
          {
            cat_url_fr: String(identifier)
          },
          {
            cat_url_it: String(identifier)
          },
          {
            cat_url_pl: String(identifier)
          },
          {
            cat_url_ru: String(identifier)
          },
          {
            cat_id: Number.isNaN(Number(identifier)) ? undefined : Number(identifier)
          }
        ],
        cat_active: true
      }
    })

    return category
  }

  async getRootCategories() {
    const categories = await this.catDB.findMany({
      where: {
        cat_rootcat: 0,
        cat_active: true
      },
      orderBy: {
        cat_sort: 'asc'
      }
    })

    return categories
  }

  async getCategories() {
    type Category = cats & { subcategories?: cats[] }

    const categories: Category[] = await this.catDB.findMany({
      where: {
        cat_active: true
      },
      orderBy: {
        cat_sort: 'desc'
      }
    })

    categories.forEach((category) => {
      const subcategories = categories.filter((i) => i.cat_rootcat === category.cat_id)
      if (subcategories?.length) {
        category.subcategories = subcategories
      }
      // console.log('🚀 ~ ProductProvider ~ categories.forEach ~ category.subcategories:', subcategories)
    })

    // if (locale) {
    //   await LangDict.categoriesTranslate(categories, request.requestQs.locale)
    // }

    return categories.filter((i) => i.cat_rootcat == 0)
  }

  async getProductDataById(id: number) {
    const rawProduct = await this.db.findFirstOrThrow({
      where: {
        prod_id: id
      },
      select: this.getPublicFieldsObject()
    })

    const [product] = await this.initAndTransformToAdonisModel([rawProduct]) //this.transformToAdonisModel([rawProduct])

    return product
  }
  async getProductById(id: number) {
    //console.time('getProductById: ' + id)

    const rawProduct = await this.db.findFirstOrThrow({
      where: {
        prod_id: id
      },
      select: this.getPublicFieldsObject()
    })

    const [product] = await this.initAndTransformToAdonisModel([rawProduct]) //this.transformToAdonisModel([rawProduct])

    //console.time('load product schema: ' + rawProduct.prod_id)
    const schemaRes = await this.schemaDB.findFirst({
      where: {
        OR: [
          { product_id: rawProduct.prod_id },
          {
            related_ids: {
              contains: String(rawProduct.prod_id)
            }
          }
        ]
      },
      select: {
        body: true
      }
    })

    let schema = null
    if (schemaRes) {
      try {
        schema = JSON.parse(schemaRes.body)
      } catch (error) {
        console.log('🚀 ~ ProductProvider ~ getProductById schema parse ~ error:', error)
      }

      //console.timeEnd('load product schema: ' + rawProduct.prod_id)

      //console.timeEnd('getProductById: ' + id)
    }

    return { product, fields, schema }
  }

  async getProductBreadcrumbs(id: number) {
    //console.time('getProductBreadcrumbs: ' + id)

    type BreadCrumb = {
      cat_id: Number
      cat_url_ru: String
      cat_title: String
      cat_rootcat?: Number
    }

    const buildCumulativePaths = (items) => {
      return items.map((item, index) => {
        const path = items
          .slice(0, index + 1)
          .map((i) => i.cat_url_ru)
          .join('/')
        return {
          ...item,
          fullPath: `/${path}`
        }
      })
    }

    const product = await this.db.findFirstOrThrow({
      where: {
        prod_id: id
      },
      select: {
        prod_cat: true
      }
    })

    const breadcrumbs: BreadCrumb[] = []

    const getCategoryChain = async (categoryId: number) => {
      const category = await this.catDB.findFirst({
        where: {
          cat_id: categoryId
        },
        select: {
          cat_id: true,
          cat_url_ru: true,
          cat_title: true,
          cat_rootcat: true
        }
      })

      if (category) {
        breadcrumbs.unshift({
          cat_id: category.cat_id,
          cat_url_ru: category.cat_url_ru,
          cat_title: category.cat_title
        })

        if (category.cat_rootcat) {
          await getCategoryChain(category.cat_rootcat)
        }
      }
    }

    await getCategoryChain(Number(product.prod_cat))

    //console.timeEnd('getProductBreadcrumbs: ' + id)

    return buildCumulativePaths(breadcrumbs)
  }

  async getCategoryNameById(id: number) {
    const category = await this.catDB.findFirst({
      where: {
        cat_id: id
      },
      select: {
        cat_title: true
      }
    })

    return category?.cat_title || undefined
  }

  async globalFlatSearchMeili({ searchvalue = '', filters, limit = 100, page = 1, sorting = [] }: GlobalSearchParams) {
    const timestamp = Date.now()

    searchvalue = decodeURIComponent(searchvalue)

    //console.time(`globalFlatSearchMeili fetch: search/categories: | ${timestamp}`)

    const [productsResults, activeCategories] = await Promise.all([
      this.meiliDB.index('products').search(searchvalue, {
        offset: limit * (page - 1),
        limit,
        // filter,
        // sort,
        matchingStrategy: 'frequency',
        // attributesToHighlight: ['*'],
        attributesToHighlight: [
          'prod_sku',
          'prod_analogsku',
          'prod_manuf',
          'prod_note',
          'prod_year',
          'prod_type',
          'prod_uses',
          'prod_purpose',
          'prod_analogs',
          'prod_model',
          'prod_material'
        ],
        highlightPreTag: '__ais-highlight__',
        highlightPostTag: '__/ais-highlight__',
        attributesToCrop: ['prod_secret', 'prod_purchase', 'prod_suppplier'],

        attributesToSearchOn: [
          'prod_sku',
          'prod_analogsku',
          'prod_manuf',
          'prod_note',
          'prod_year',
          'prod_type',
          'prod_uses',
          'prod_purpose',
          'prod_analogs',
          'prod_model',
          'prod_material'
        ]
        // attributesToRetrieve: ['prod_id', 'prod_sku', 'prod_analogsku', 'prod_analogs', 'prod_manuf', 'prod_img', 'prod_count', 'prod_type', 'prod_purpose', 'prod_material']
      }),
      this.meiliDB.index('categories').search('', {
        filter: ['cat_active = true'],
        limit: 1000
      })
    ])
    //console.timeEnd(`globalFlatSearchMeili fetch: search/categories: | ${timestamp}`)

    const categoriesIds = Array.from(new Set(productsResults.hits.map((p) => Number(p.prod_cat))))
    const categoriesData = {}

    await Promise.all(
      categoriesIds.map(async (catId) => {
        const [columns, filters, category] = await Promise.all([
          this.meiliDB.index('columns').search('', {
            filter: [`cat_id IN [${catId}, 9999]`],
            sort: ['sort:asc'],
            distinct: 'keyname',
            limit: 900
          }),
          this.meiliDB.index('filters').search('', {
            filter: [`category_id = ${catId}`]
          }),
          this.meiliDB.index('categories').search('', {
            filter: [`cat_id = ${catId}`],
            limit: 1
          })
        ])

        categoriesData[catId] = {
          columns: columns.hits,
          filters: filters.hits,
          category: category.hits[0]
        }
      })
    )

    const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits
    const flatProducts = productsResults.hits //await this.initAndTransformToAdonisModel(productsResults.hits)

    return {
      products: flatProducts,
      categoriesData,
      meta: {
        totalCount: totalCount,
        lastPage: Math.ceil(totalCount / limit),
        currentPage: page,
        perPage: limit,
        isFirstPage: page,
        isLastPage: page === Math.ceil(totalCount / limit),
        previousPage: page - 1,
        nextPage: page + 1,
        pageCount: Math.ceil(totalCount / limit)
      }
    }
  }

  async globalSearchMeili({ searchvalue = '', filters, limit = 100, page = 1, sorting = [] }: GlobalSearchParams) {
    searchvalue = decodeURIComponent(searchvalue)

    console.log('🚀 ~ ProductProvider ~ globalSearchMeili ~ sorting:', sorting)
    const timestamp = Date.now()
    //console.time(`globalSearchMeili: ${searchvalue} | ${timestamp}`)

    let filterConditions = {}

    if (filters) {
      try {
        const parsedFilters = typeof filters === 'string' ? JSON.parse(decodeURIComponent(filters)) : filters
        Object.keys(parsedFilters).forEach((key) => {
          if (parsedFilters[key]?.length) {
            filterConditions[key] = parsedFilters[key]
          }
        })
      } catch (error) {
        console.log('Error parsing filters:', error)
      }
    }

    const searchvalueWithPhrases = searchvalue
      .split(' ')
      .map((word) => `"${word}"`)
      .join(' ')

    const sort = sorting.map((sortItem) => `${sortItem.column}:${sortItem.direction}`)
    console.log('sort: ', sort)

    let filter = Object.entries(filterConditions).map(([key, values]) => {
      return `${key} IN [${values.map((value) => `"${value}"`).join(',')}]`
    })

    const sizeregexp = /(\*|x|х|X|Х|\s|на|НА|-|:)/gm
    let sizeMatches = searchvalue.split(' ').filter((i) => isSize(i))[0]

    if (sizeMatches) {
      let sizeIn = 0.0
      let sizeOut = 0.0
      let sizeHeight = 0.0
      const sizeTollerance = 0.3

      let sizeIn_2: number | undefined = undefined
      let sizeOut_2: number | undefined = undefined
      let sizeHeight_2: number | undefined = undefined

      sizeMatches = String(sizeMatches).replace(sizeregexp, '*')
      let sizes: string[] = sizeMatches.split('*').filter((i) => i)

      try {
        sizeIn = Number(String(sizes[0]).split('/')[0])
        sizeIn_2 = Number(String(sizes[0]).split('/')[1]) || undefined

        sizeOut = Number(String(sizes[1]).split('/')[0])
        sizeOut_2 = Number(String(sizes[1]).split('/')[1]) || undefined
      } catch (error) {
        sizeIn = Number(sizes[0])
        sizeOut = Number(sizes[1])
      }

      if (sizes[2]) {
        try {
          sizeHeight = Number(String(sizes[2]).split('/')[0])
          sizeHeight_2 = Number(String(sizes[2]).split('/')[1]) || undefined
        } catch (error) {
          sizeHeight = Number(sizes[2])
        }
      }

      filter = [
        ...filter,
        `size_in ${sizeIn - sizeTollerance} TO ${sizeIn + sizeTollerance}`,
        sizeOut ? `size_out ${sizeOut - sizeTollerance} TO ${sizeOut + sizeTollerance}` : null,
        sizeIn_2 ? `size_in_2 ${sizeIn_2 - sizeTollerance} TO ${sizeIn_2 + sizeTollerance}` : null,
        sizeOut_2 ? `size_out_2 ${sizeOut_2 - sizeTollerance} TO ${sizeOut_2 + sizeTollerance}` : null,
        sizeHeight ? `size_h ${sizeHeight - sizeTollerance} TO ${sizeHeight + sizeTollerance}` : null,
        sizeHeight_2 ? `size_h_2 ${sizeHeight_2 - sizeTollerance} TO ${sizeHeight_2 + sizeTollerance}` : null
      ].filter(Boolean)
    }

    const dotregexp = /\,{1,}/gm

    let _value = String(searchvalue).replace(dotregexp, '.')
    _value = _value
      .split(' ')
      .filter((i) => !isSize(i))
      .join(' ')
    console.log('🚀 ~ ProductProvider ~ globalSearchMeili ~ _value:', _value)

    //возможно нужно искать searchvalueWithPhrases или _value за 2 захода

    console.log('🚀 ~ ProductProvider ~ globalSearchMeili ~ filter:', filter)
    console.log('🚀 ~ ProductProvider ~ globalSearchMeili ~ searchvalue:', searchvalue)
    console.log('🚀 ~ ProductProvider ~ globalSearchMeili ~ searchvalueWithPhrases:', searchvalueWithPhrases)

    //console.time(`globalSearchMeili fetch: search/categories: | ${timestamp}`)

    const [productsResults, activeCategories] = await Promise.all([
      this.meiliDB.index('products').search(_value, {
        offset: limit * (page - 1),
        limit,
        filter,
        sort,
        matchingStrategy: 'frequency'
      }),
      this.meiliDB.index('categories').search('', {
        filter: ['cat_active = true'],
        limit: 1000
      })
    ])
    //console.timeEnd(`globalSearchMeili fetch: search/categories: | ${timestamp}`)

    if (!filterConditions['prod_cat']) {
      filterConditions['prod_cat'] = activeCategories.hits.map((i) => String(i.cat_id))
    }

    const productsByCategory = productsResults.hits.reduce((acc, product) => {
      const catId = product.prod_cat
      if (!acc[catId]) {
        acc[catId] = []
      }
      acc[catId].push(product)
      return acc
    }, {})

    const categoriesData = await Promise.all(
      Object.keys(productsByCategory).map(async (catId) => {
        const [categoryColumns, filters, categorySearch] = await Promise.all([
          this.meiliDB.index('columns').search('', {
            filter: [`cat_id IN [${catId}, 9999]`],
            sort: ['sort:asc'],
            distinct: 'keyname',
            limit: 900
          }),
          this.meiliDB.index('filters').search('', {
            filter: [`category_id = ${catId}`]
          }),
          this.meiliDB.index('categories').search('', {
            filter: [`cat_id = ${catId}`],
            limit: 1
          })
        ])

        const category = categorySearch.hits[0]
        const products = await this.initAndTransformToAdonisModel(productsByCategory[catId])

        return {
          categoryId: catId,
          categoryTitle: category.cat_title,
          columns: categoryColumns.hits,
          products: products,
          filters: filters.hits
        }
      })
    )

    const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits

    const res = {
      categories: categoriesData,
      meta: {
        totalCount: totalCount,
        lastPage: Math.ceil(totalCount / limit),
        currentPage: page,
        perPage: limit,
        isFirstPage: page,
        isLastPage: page === Math.ceil(totalCount / limit),
        previousPage: page - 1,
        nextPage: page + 1,
        pageCount: Math.ceil(totalCount / limit)
      }
      // filters: filters
    }

    console.log('globalSearchMeili total count: ', res.meta.totalCount)

    //console.timeEnd(`globalSearchMeili: ${searchvalue} | ${timestamp}`)
    return res
  }

  async globalSearch({ searchvalue = '', filters, limit = 100, page = 1, sorting = [] }: GlobalSearchParams) {
    searchvalue = decodeURIComponent(searchvalue)

    //console.time(`globalSearch: ${searchvalue}`)
    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    searchWhere = this.makeSearchWhere({ filters, input: searchvalue, enableFullTextSearch: false })
    // console.log('🚀 ~ ProductProvider ~ globalSearch ~ searchWhere:', JSON.stringify(searchWhere))

    if (filters) {
      try {
        if (typeof filters === 'string') {
          filters = JSON.parse(decodeURIComponent(filters))
          // console.log('🚀 ~ ProductProvider ~ makeFiltersWhere ~ filters:', filters)
        }
      } catch (error) {
        console.log('Error parsing:', error)
      }

      filtersWhere = this.makeFiltersWhere(filters)
      // console.log('🚀 ~ ProductProvider ~ globalSearch ~ filtersWhere:', JSON.stringify(filtersWhere))
    }

    const orderBy = this.makeOrderBy(sorting)

    const activeCategories = await this.catDB.findMany({
      select: {
        cat_id: true
      },
      where: {
        cat_active: true
      }
    })

    // console.log('this.getPublicFieldsObject():', this.getPublicFieldsObject())

    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          AND: [
            !filters?.prod_cat
              ? {
                  prod_cat: {
                    in: activeCategories.map((i) => String(i.cat_id)).filter((i) => i)
                  }
                }
              : {},
            filtersWhere ?? {},
            searchWhere ?? {}
          ]
        },
        orderBy: [
          {
            _relevance: {
              fields: fulltextSearchFields,
              search: searchvalue,
              sort: 'asc'
            }
          },
          ...orderBy
        ],
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(limit),
        page: Number(page)
      })

    const productsByCategory = rawProducts.reduce((acc, product) => {
      const catId = product.prod_cat
      if (!acc[catId]) {
        acc[catId] = []
      }
      acc[catId].push(product)
      return acc
    }, {})

    const [tableFilters] = await Promise.all([
      $prisma.filters.findMany({
        where: {
          category_id: {
            in: rawProducts.map((i) => Number(i.prod_cat)).filter((i) => i)
          }
        }
      })
    ])

    // Get columns for each category
    const categoriesData = await Promise.all(
      Object.keys(productsByCategory).map(async (catId) => {
        const [categoryColumns, categoryTitle] = await Promise.all([
          $prisma.cat_columns.findMany({
            where: {
              OR: [
                { cat_id: Number(catId) },
                { cat_id: 9999 } // Include global columns
              ]
            },
            orderBy: {
              sort: 'asc'
            },
            distinct: ['keyname']
          }),
          await this.getCategoryNameById(Number(catId))
        ])

        const products = await this.initAndTransformToAdonisModel(productsByCategory[catId]) //this.transformToAdonisModel(productsByCategory[catId])
        await Product.afterFetchHook(products)

        return {
          categoryId: catId,
          categoryTitle: categoryTitle,
          columns: categoryColumns?.flat(),
          products: products
        }
      })
    )

    const res = {
      categories: categoriesData,
      meta: {
        ...meta
      },
      filters: tableFilters
    }

    // //console.time('transformToAdonisModel')
    // const products = this.transformToAdonisModel(rawProducts)
    // //console.timeEnd('transformToAdonisModel')

    // //console.time('Product.afterFetchHook')
    // await Product.afterFetchHook(products)
    // //console.timeEnd('Product.afterFetchHook')

    // const res = {
    //   data: products,
    //   meta,
    //   columns: tableColumns,
    //   filters: tableFilters
    // }

    //console.timeEnd(`globalSearch: ${searchvalue}`)

    return res
  }

  private makeFiltersWhere(filters) {
    try {
      if (typeof filters === 'string') {
        filters = JSON.parse(decodeURIComponent(filters))
        // console.log('🚀 ~ ProductProvider ~ makeFiltersWhere ~ filters:', filters)
      }
    } catch (error) {
      console.log('Error parsing:', error)
    }

    let filtersWhere: Prisma.productsWhereInput | undefined
    Object.keys(filters).forEach((key) => !filters[key]?.length && delete filters[key])

    filtersWhere = {
      AND: Object.keys(filters).map((filter) => {
        return {
          [filter]: {
            in: filters[filter]
          }
        }
      })
    }

    return filtersWhere
  }
  private makeSearchWhere({ input, filters, enableFullTextSearch = true }) {
    try {
      if (typeof filters === 'string') {
        filters = JSON.parse(decodeURIComponent(filters))
      }
    } catch (error) {
      console.log('Error parsing:', error)
    }

    let searchWhere: Prisma.productsWhereInput = {}

    const { _value } = searchValueHandler(
      {
        value: input,
        productDBfields: clientProductDBfields,
        sizeField: 'prod_size'
      },
      filters
    )

    const searchValue = _value
      ?.split(' ')
      .filter((i) => i)
      .map((i) => String(i).trim())

    const sizeregexp = /(\*|x|х|X|Х|\s|на|НА|-|:)/gm
    let sizeMatches = input.split(' ').filter((i) => isSize(i))[0]

    if (sizeMatches) {
      let sizeIn = 0.0
      let sizeOut = 0.0
      let sizeHeight = 0.0
      const sizeTollerance = 0.3

      let sizeIn_2: number | undefined = undefined
      let sizeOut_2: number | undefined = undefined
      let sizeHeight_2: number | undefined = undefined

      sizeMatches = String(sizeMatches).replace(sizeregexp, '*')
      // console.log('🚀 ~ _searchValueHandler ~ sizeMatches:', sizeMatches)

      // value = value.replace(sizeregexp, '*')

      let sizes: string[] = sizeMatches.split('*')
      sizes = sizes.filter((i) => i)

      console.log('🚀 ~ ProductProvider ~ globalSearch ~ sizes:', sizes)

      try {
        sizeIn = Number(String(sizes[0]).split('/')[0])
        sizeIn_2 = Number(String(sizes[0]).split('/')[1]) || undefined

        sizeOut = Number(String(sizes[1]).split('/')[0])
        sizeOut_2 = Number(String(sizes[1]).split('/')[1]) || undefined
      } catch (error) {
        console.error('error parse size: ', error)
        sizeIn = Number(sizes[0])
        sizeOut = Number(sizes[1])
      }

      // searchBySize = true

      if (sizes[2]) {
        try {
          sizeHeight = Number(String(sizes[2]).split('/')[0])
          sizeHeight_2 = Number(String(sizes[2]).split('/')[1]) || undefined
        } catch (error) {
          console.error('error parse height: ', error)
          sizeHeight = Number(sizes[2])
        }
      }

      // if (!searchWhere.AND) {
      //   searchWhere.AND = []
      // }

      searchWhere.AND = [
        {
          size_in: {
            gte: sizeIn - sizeTollerance,
            lte: sizeIn + sizeTollerance
          },
          size_in_2: sizeIn_2
            ? {
                gte: sizeIn_2 - sizeTollerance,
                lte: sizeIn_2 + sizeTollerance
              }
            : {},

          size_out: sizeOut
            ? {
                gte: sizeOut - sizeTollerance,
                lte: sizeOut + sizeTollerance
              }
            : {},
          size_out_2: sizeOut_2
            ? {
                gte: sizeOut_2 - sizeTollerance,
                lte: sizeOut_2 + sizeTollerance
              }
            : {},
          size_h: sizeHeight
            ? {
                gte: sizeHeight - sizeTollerance,
                lte: sizeHeight + sizeTollerance
              }
            : {},
          size_h_2: sizeHeight_2
            ? {
                gte: sizeHeight_2 - sizeTollerance,
                lte: sizeHeight_2 + sizeTollerance
              }
            : {}
        }
      ]
    } else {
      // searchWhere = {
      //   AND: searchValue?.map((val) => {
      //     return {
      //       OR: searchFields.map((field) => ({
      //         [field]: {
      //           contains: val
      //         }
      //       }))
      //     }
      //   })
      // }
    }

    // console.log('searchValue:', searchValue)

    if (enableFullTextSearch) {
      searchWhere.AND = [
        ...(searchWhere.AND || []),
        ...searchValue.map((val) => {
          return {
            OR: fulltextSearchFields.map((field) => ({
              [field]: {
                search: val
              }
            }))
          }
        })
      ]
    } else {
      searchWhere.AND = [
        ...(searchWhere.AND || []),
        ...searchValue.map((val) => {
          return {
            OR: searchFields.map((field) => ({
              [field]: {
                contains: val
              }
            }))
          }
        })
      ]
    }

    return searchWhere
  }

  async getSubCategories(identifier: number | string) {
    if (!identifier) throw new Error('ProductProvider. error: category identifier undefined')

    const category = await this.findCategoryByIdentifier(identifier)

    if (!category) return null //throw new Error('ProductProvider.findByCategoryIdOrUrl error: category not found')

    const subcategories = await this.catDB.findMany({
      where: {
        cat_rootcat: category.cat_id,
        cat_active: true
        // duplicate: false
      }
    })

    if (!subcategories.length) return null

    return subcategories
  }

  async findByCategoryIdOrUrl({ identifier, page = 1, limit = 100, sorting = [], search, filters }: FindByCategoryIdParams) {
    //console.time(`query in catalog: ${identifier}`)

    if (!identifier) throw new Error('ProductProvider.findByCategoryIdOrUrl error: category identifier undefined')

    const category = await this.findCategoryByIdentifier(identifier)

    if (!category) throw new Error('ProductProvider.findByCategoryIdOrUrl error: category not found')

    try {
      filters = JSON.parse(filters)
    } catch (error) {}

    let searchWhere: Prisma.productsWhereInput | undefined
    let filtersWhere: Prisma.productsWhereInput | undefined

    if (search) {
      searchWhere = this.makeSearchWhere({ filters, input: search })
    }

    if (filters) {
      filtersWhere = this.makeFiltersWhere(filters)
    }

    const orderBy = this.orderByFromArray(sorting) //this.makeOrderBy(sorting)

    const [categoryFilters, categoryColumns] = await Promise.all([
      $prisma.filters.findMany({
        where: {
          category_id: {
            in: [Number(category.cat_id), 9999]
          }
        },
        distinct: 'field',
        orderBy: {
          id: 'desc'
        }
      }),

      $prisma.cat_columns.findMany({
        where: {
          OR: [{ cat_id: Number(category.cat_id) }, { cat_id: 9999 }]
        },
        orderBy: {
          // ID: 'desc'
          sort: 'asc'
        },
        distinct: ['keyname']
      })
    ])
    // console.log('🚀 ~ ProductProvider ~ findByCategoryIdOrUrl ~ categoryFilters:', categoryFilters)

    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          AND: [
            {
              prod_cat: String(category.cat_id)
            },
            searchWhere ?? {},
            filtersWhere ?? {}
          ]
        },
        orderBy,
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(limit),
        page: Number(page)
      })

    //console.time('transformToAdonisModel')
    const products = await this.initAndTransformToAdonisModel(rawProducts) //this.transformToAdonisModel(rawProducts)
    //console.timeEnd('transformToAdonisModel')

    //console.time('Product.afterFetchHook')
    await Product.afterFetchHook(products)
    //console.timeEnd('Product.afterFetchHook')

    const res = {
      products: {
        data: products,
        meta
      },
      category: {
        columns: categoryColumns,
        filters: categoryFilters,
        ...category
      }
    }

    //console.timeEnd(`query in catalog: ${identifier}`)

    return res
  }

  async getProductsByIds({ ids, page = 1 }: { ids: number[]; page?: number }) {
    const [rawProducts, meta] = await this.db
      .paginate({
        where: {
          prod_id: {
            in: ids
          }
        },
        select: this.getPublicFieldsObject()
      })
      .withPages({
        includePageCount: true,
        limit: Number(200),
        page: Number(page)
      })

    const productsByCategory = rawProducts.reduce((acc, product) => {
      const catId = product.prod_cat
      if (!acc[catId]) {
        acc[catId] = []
      }
      acc[catId].push(product)
      return acc
    }, {})

    // const [tableFilters] = await Promise.all([
    //   $prisma.filters.findMany({
    //     where: {
    //       category_id: {
    //         in: rawProducts.map((i) => Number(i.prod_cat)).filter((i) => i)
    //       }
    //     }
    //   })
    // ])

    const categoriesData = await Promise.all(
      Object.keys(productsByCategory).map(async (catId) => {
        const [categoryColumns, categoryTitle] = await Promise.all([
          $prisma.cat_columns.findMany({
            where: {
              OR: [{ cat_id: Number(catId) }, { cat_id: 9999 }]
            },
            orderBy: {
              sort: 'asc'
            },
            distinct: ['keyname']
          }),
          await this.getCategoryNameById(Number(catId))
        ])

        const products = await this.initAndTransformToAdonisModel(productsByCategory[catId])
        await Product.afterFetchHook(products)

        return {
          categoryId: catId,
          categoryTitle: categoryTitle,
          columns: categoryColumns,
          products: products
        }
      })
    )

    return {
      categories: categoriesData,
      meta
      // filters: tableFilters
    }
  }

  private orderByFromArray(sortArray: Array<{ column: string; direction: 'asc' | 'desc'; order: number }>) {
    const orderBy = sortArray.map((item) => ({
      [item.column]: item.direction
    }))

    return orderBy
  }
  private makeOrderBy(initValue: any[]) {
    const sorting = [...initValue]
    const bySize = sorting?.some((i) => Object.keys(i).some((key) => key === 'prod_size')) || !sorting?.length

    if (bySize) {
      const bySizeIndex = sorting?.findIndex((i) => Object.keys(i).some((key) => key === 'prod_size')) ?? 0
      const sizeOrderType = sorting[bySizeIndex]?.['prod_size'] ?? 'asc'

      sorting.splice(
        bySizeIndex,
        1,
        ...[
          {
            size_in: sizeOrderType
          },
          {
            size_in_2: sizeOrderType
          },
          {
            size_out: sizeOrderType
          },
          {
            size_out_2: sizeOrderType
          },
          {
            size_h: sizeOrderType
          },
          {
            size_h_2: sizeOrderType
          }
        ]
      )
    }

    return sorting
  }
}

export const productProvider = new ProductProvider()
