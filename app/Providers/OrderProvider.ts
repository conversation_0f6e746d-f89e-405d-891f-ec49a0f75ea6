import { Snapshot } from 'App/Interfaces/orders/Snapshot'
import { $prisma } from 'App/Services/Prisma'

class OrderProvider {
  constructor()

  db = $prisma.orders
  snapshotDB = $prisma.orders_snapshots
  itemsDB = $prisma.order_items

  getSnapshotBody(snapshot: Snapshot) {
    try {
      snapshot.body = JSON.parse(snapshot.body)

      try {
        snapshot.body.order_coupons = JSON.parse(snapshot.body.order_coupons)
      } catch (error) {
        snapshot.body.order_coupons
      }

      return snapshot.body
    } catch (error) {
      console.error(`OrderProvider.ts > parseBody: ${error}`)

      return null
    }
  }

  async getUserOrders({ clientId, page = 1 }) {
    console.log('🚀 ~ OrderProvider ~ getUserOrders ~ clientId:', clientId)
    if (!clientId) {
      throw new Error('Client ID is required')
    }

    const data: SnapshotBodyInterface[] = []
    const [rawOrders, meta] = await this.db
      .paginate({
        where: {
          order_client: String(clientId)
        },
        orderBy: {
          order_id: 'desc'
        }
      })
      .withPages({
        includePageCount: true,
        limit: Number(10),
        page: Number(page)
      })

    await Promise.all(
      rawOrders.map(async (order) => {
        const snapshot = await this.snapshotDB.findFirst({
          where: {
            orderid: order.order_id
          },
          orderBy: {
            ID: 'desc'
          }
        })
        const body = this.getSnapshotBody(snapshot)

        if (body) {
          data.push(body)
        }
      })
    )

    return {
      data,
      meta
    }
  }
}

export const orderProvider = new OrderProvider()
