import { aiProvider } from 'App/Providers/AiProvider'
import { cartProvider } from 'App/Providers/CartProvider'
import { langProvider } from 'App/Providers/LangProvider'
import { serviceProvider } from 'App/Providers/ServiceProvider'
import { CreateRouterParams } from 'App/Services/tRPC'

import { z } from 'zod'

export const servicesRouter = ({ router, publicProcedure, authedProcedure }: CreateRouterParams) =>
  router({
    getFullDict: authedProcedure
      .input(
        z.object({
          dates: z.array(z.string()).min(2).max(2),
          clientId: z.number()
        })
      )
      .query(async ({ ctx, input }) => {}),
    orderList: authedProcedure
      .input(
        z.object({
          searchValue: z.string().optional()
        })
      )
      .query(async ({ ctx, input }) => {
        return await serviceProvider.ordersList(input)
      }),
    getHtmkChunk: publicProcedure.input(z.object({ id: z.number() })).query(async ({ ctx, input }) => {
      return await serviceProvider.getHtmlChunk({ id: input.id })
    }),
    getPage: publicProcedure.input(z.object({ id: z.any(), locale: z.string().optional() })).query(async ({ ctx, input }) => {
      return await serviceProvider.getPage({ id: input.id, locale: input.locale })
    }),
    calculateShipping: publicProcedure
      .input(
        z.object({
          type: z.enum(['standard', 'express']).or(z.string()).or(z.array(z.string())), // или z.union([z.literal('standard'), z.literal('express')])
          country: z.number().default(643),
          destinationIndex: z.number(),
          city: z.any().optional(),
          address: z.any().optional()
        })
      )
      .query(async ({ ctx, input }) => {
        // type = 'standard', country = 643, destinationIndex = 101000, city, address
        const sessionUser = ctx.sessionUser
        const cartId = await cartProvider.getCartId({ ctx: ctx.ctx, sessionUser })

        console.log('calc shipoing cartId: ', cartId)

        return await serviceProvider.calculateShipping({ ...input, cartId })
      }),
    getSettings: publicProcedure.input(z.object({ params: z.array(z.string()) })).query(async ({ ctx, input }) => {
      return await serviceProvider.getSettings({ params: input.params })
    }),

    // EMAIL РАССЫЛКА
    getEmailSenderData: authedProcedure.query(async ({ ctx, input }) => {
      return await serviceProvider.getEmailSenderData()
    }),

    aiChat: publicProcedure
      .input(
        z.object({
          message: z.string(),
          threadId: z.string().optional(),
          resourceId: z.string().optional(),
          conversationHistory: z.array(
            z.object({
              role: z.enum(['user', 'assistant', 'system']),
              content: z.string()
            })
          ).optional()
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await aiProvider.aiChat(input)
      }),

    // Оптимизированный AI чат с быстрым ответом
    aiChatOptimized: publicProcedure
      .input(
        z.object({
          message: z.string(),
          threadId: z.string().optional(),
          resourceId: z.string().optional(),
          conversationHistory: z.array(
            z.object({
              role: z.enum(['user', 'assistant', 'system']),
              content: z.string()
            })
          ).optional(),
          enableProductSearch: z.boolean().default(true),
          temperature: z.number().min(0).max(2).default(0.5),
          maxRetries: z.number().min(1).max(5).default(2),
          maxSteps: z.number().min(1).max(10).default(5),
          topP: z.number().min(0).max(1).default(1)
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await aiProvider.aiChatOptimized(input)
      }),

    // Получение истории чата
    getChatHistory: publicProcedure
      .input(z.object({ threadId: z.string() }))
      .query(async ({ ctx, input }) => {
        return await aiProvider.getThreadHistory(input.threadId)
      }),

    // Удаление истории чата
    deleteChatHistory: publicProcedure
      .input(z.object({ threadId: z.string() }))
      .mutation(async ({ ctx, input }) => {
        return await aiProvider.deleteThread(input.threadId)
      }),

    // Генерация нового threadId
    generateChatThread: publicProcedure
      .query(async ({ ctx, input }) => {
        return { threadId: aiProvider.generateThreadId() }
      }),

    // Получение списка тредов для resourceId
    getChatThreadsList: publicProcedure
      .input(z.object({ resourceId: z.string() }))
      .query(async ({ ctx, input }) => {
        return await aiProvider.getThreadsList(input.resourceId)
      }),

    // Обогащение ответа информацией о товарах
    enrichResponseWithProducts: publicProcedure
      .input(
        z.object({
          originalResponse: z.string(),
          products: z.array(z.any()),
          threadId: z.string(),
          resourceId: z.string(),
          userMessage: z.string(),
          temperature: z.number().min(0).max(2).default(0.3)
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await aiProvider.enrichResponseWithProducts(input)
      }),

    updateEmailSenderData: authedProcedure
      .input(
        z
          .object({
            message: z.string(),
            subject: z.string(),
            recipients: z.array(
              z.object({
                email: z.string(),
                note: z.string(),
                createdAt: z.string(),
                isActive: z.boolean()
              })
            ),
            scheduleTime: z.number().positive().optional(), // для интервалов в минутах
            scheduleType: z.enum(['interval', 'weekly']), // новое поле
            weeklySchedule: z.array(z.number().min(0).max(6)).optional(), // массив дней недели (0-6)
            enabled: z.boolean(),
            lastSendDate: z.string().optional() // добавляем для полноты
          })
          .refine(
            (data) => {
              // Валидация для интервального типа
              if (data.scheduleType === 'interval') {
                return data.scheduleTime !== undefined && data.scheduleTime > 0
              }
              return true
            },
            {
              message: 'scheduleTime обязателен и должен быть больше 0 для типа "interval"',
              path: ['scheduleTime']
            }
          )
          .refine(
            (data) => {
              // Валидация для еженедельного типа
              if (data.scheduleType === 'weekly') {
                return data.weeklySchedule !== undefined && data.weeklySchedule.length > 0 && data.weeklySchedule.every((day) => Number.isInteger(day) && day >= 0 && day <= 6)
              }
              return true
            },
            {
              message: 'weeklySchedule обязателен и должен содержать валидные дни недели (0-6) для типа "weekly"',
              path: ['weeklySchedule']
            }
          )
      )
      .mutation(async ({ ctx, input }) => {
        return await serviceProvider.updateEmailSenderData(input)
      }),

    sendBulkEmail: authedProcedure
      .input(
        z.object({
          batchSize: z.number().default(100)
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await serviceProvider.sendBulkEmail(input)
      }),

    getNextScheduledRun: authedProcedure.query(async ({ ctx, input }) => {
      return await serviceProvider.getNextScheduledRun()
    }),

    // Метод для отправки по расписанию
    sendScheduledEmail: authedProcedure
      .input(
        z.object({
          batchSize: z.number().positive().default(100)
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await serviceProvider.sendScheduledEmail(input)
      })
  })

// export type LangRouter = Awaited<ReturnType<typeof langRouter>>
