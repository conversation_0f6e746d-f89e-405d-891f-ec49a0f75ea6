import { ozonService } from 'App/Services/OzonService'
import { CreateRouterParams } from 'App/Services/tRPC'
import { z } from 'zod'

// Схемы валидации для входных данных
const SyncProductsSchema = z.object({
  productIds: z.array(z.number().positive()).min(1).max(100),
  batchSize: z.number().positive().default(10),
  uploadImages: z.boolean().default(true)
})

const UpdateStockSchema = z.object({
  productId: z.number().positive(),
  stock: z.number().min(0),
  offerId: z.string().optional()
})

const UpdatePriceSchema = z.object({
  productId: z.number().positive(),
  price: z.number().positive(),
  oldPrice: z.number().positive().optional(),
  offerId: z.string().optional(),
  additionalPrice: z.number().optional()
})

const GetProductStatusSchema = z.object({
  productId: z.number().positive().optional(),
  offerId: z.string().optional()
}).refine(data => data.productId || data.offerId, {
  message: "Either productId or offerId must be provided"
})

const OzonSettingsSchema = z.object({
  ozon_api_key: z.string().optional(),
  ozon_client_id: z.string().optional(),
  ozon_api_url: z.string().url().optional(),
  ozon_warehouse_id: z.number().positive().optional(),
  ozon_sync_enabled: z.boolean().optional(),
  ozon_auto_sync_interval: z.number().min(1).optional(),
  ozon_default_category_id: z.number().positive().optional(),
  ozon_image_upload_enabled: z.boolean().optional(),
  ozon_image_base_url: z.string().optional(),
  ozon_max_images_per_product: z.number().min(1).max(20).optional()
})

const SyncHistorySchema = z.object({
  productId: z.number().positive().optional(),
  limit: z.number().min(1).max(1000).default(100),
  offset: z.number().min(0).default(0)
})

export const ozonRouter = ({ router, authedProcedure }: CreateRouterParams) =>
  router({
    // Синхронизация товаров
    syncProducts: authedProcedure
      .input(SyncProductsSchema)
      .mutation(async ({ input }) => {
        return await ozonService.syncProducts(input.productIds, input.batchSize, input.uploadImages)
      }),

    // Получение статуса товара в Ozon
    getProductStatus: authedProcedure
      .input(GetProductStatusSchema)
      .query(async ({ input }) => {
        return await ozonService.getProductStatus(input.productId, input.offerId)
      }),

    // Обновление остатков товара
    updateProductStock: authedProcedure
      .input(UpdateStockSchema)
      .mutation(async ({ input }) => {
        return await ozonService.updateProductStock(input.productId, input.stock, input.offerId)
      }),

    // Обновление цены товара
    updateProductPrice: authedProcedure
      .input(UpdatePriceSchema)
      .mutation(async ({ input }) => {
        return await ozonService.updateProductPrice(input.productId, input.price, input.oldPrice, input.offerId)
      }),

    // Загрузка изображений товара
    uploadProductImages: authedProcedure
      .input(z.object({ productId: z.number().positive() }))
      .mutation(async ({ input }) => {
        return await ozonService.uploadProductImagesForRouter(input.productId)
      }),

    // Получение настроек Ozon
    getOzonSettings: authedProcedure
      .query(async () => {
        return await ozonService.getOzonSettingsForRouter()
      }),

    // Обновление настроек Ozon
    updateOzonSettings: authedProcedure
      .input(OzonSettingsSchema)
      .mutation(async ({ input }) => {
        return await ozonService.updateOzonSettingsForRouter(input)
      }),

    // Получение истории синхронизации
    getSyncHistory: authedProcedure
      .input(SyncHistorySchema)
      .query(async ({ input }) => {
        return await ozonService.getSyncHistoryForRouter(input.productId, input.limit, input.offset)
      }),

    // Проверка подключения к Ozon API
    testConnection: authedProcedure
      .query(async () => {
        return await ozonService.testConnection()
      }),

    // Получение статистики синхронизации
    getSyncStats: authedProcedure
      .query(async () => {
        return await ozonService.getSyncStats()
      })
  })
