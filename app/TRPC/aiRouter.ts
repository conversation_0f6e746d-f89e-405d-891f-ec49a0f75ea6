import { aiProvider } from 'App/Providers/AiProvider'
import { productProvider } from 'App/Providers/ProductProvider'
import { CreateRouterParams } from 'App/Services/tRPC'

import { z } from 'zod'

const MultiSearchQuerySchema = z.object({
  q: z.string(),
  filters: z.union([z.array(z.string()), z.string()]).optional(),
  sort: z.union([z.array(z.string()), z.string()]).optional()
})

export const MultiSearchSchema = z.object({
  queries: z.array(MultiSearchQuerySchema)
})

export const aiRouter = ({ router, publicProcedure, authedProcedure }: CreateRouterParams) =>
  router({
    aiChat: publicProcedure
      .input(
        z.object({
          message: z.string(),
          threadId: z.string().optional(),
          resourceId: z.string().optional(),
          conversationHistory: z
            .array(
              z.object({
                role: z.enum(['user', 'assistant', 'system']),
                content: z.string()
              })
            )
            .optional()
        })
      )
      .mutation(async ({ ctx, input }) => {
        return await aiProvider.aiChat(input)
      }),

    // Получение истории чата
    getChatHistory: publicProcedure.input(z.object({ threadId: z.string() })).query(async ({ ctx, input }) => {
      return await aiProvider.getThreadHistory(input.threadId)
    }),

    // Удаление истории чата
    deleteChatHistory: publicProcedure.input(z.object({ threadId: z.string() })).mutation(async ({ ctx, input }) => {
      return await aiProvider.deleteThread(input.threadId)
    }),

    // Генерация нового threadId
    generateChatThread: publicProcedure.query(async ({ ctx, input }) => {
      return { threadId: aiProvider.generateThreadId() }
    }),

    // Получение списка тредов для resourceId
    getChatThreadsList: publicProcedure.input(z.object({ resourceId: z.string() })).query(async ({ ctx, input }) => {
      return await aiProvider.getThreadsList(input.resourceId)
    }),

    searchProducts: publicProcedure.input(MultiSearchSchema).query(async ({ ctx, input }) => {
      return await productProvider.searchFromAiChat(input.queries)
    })
  })

// export type LangRouter = Awaited<ReturnType<typeof langRouter>>
