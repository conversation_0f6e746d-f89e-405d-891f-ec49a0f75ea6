import fs from 'fs'
import path from 'path'
import { exec } from 'child_process'
import { promisify } from 'util'
import Env from '@ioc:Adonis/Core/Env'

const execAsync = promisify(exec)

/**
 * Сервис для обработки вложений в письмах
 */
export default class EmailAttachmentService {
  private static readonly SUPPORTED_EXTENSIONS = ['.pdf', '.docx', '.xlsx', '.doc', '.xls']
  private static readonly UPLOAD_PATH = Env.get('UPLOAD_PATH')
  private static readonly TEMP_ATTACHMENTS_FOLDER = '_email_attachments'

  /**
   * Обработка всех вложений письма
   */
  public static async processAttachments(parsedEmail: any): Promise<{
    attachments: Array<{
      filename: string
      originalPath: string
      convertedPath?: string
      textContent?: string
      error?: string
    }>
    totalTextContent: string
    tempDir: string  // Добавляем путь к временной папке для последующего удаления
  }> {
    const attachments: any[] = []
    let totalTextContent = ''

    if (!parsedEmail.attachments || parsedEmail.attachments.length === 0) {
      return { attachments, totalTextContent, tempDir: '' }
    }

    // Создаем временную папку для вложений
    const tempDir = await this.createTempDirectory()

    for (const attachment of parsedEmail.attachments) {
      try {
        const result = await this.processAttachment(attachment, tempDir)
        attachments.push(result)
        
        if (result.textContent) {
          totalTextContent += `\n\n--- Содержимое файла ${result.filename} ---\n${result.textContent}\n`
        }
      } catch (error) {
        console.error(`Ошибка обработки вложения ${attachment.filename}:`, error)
        attachments.push({
          filename: attachment.filename || 'unknown',
          originalPath: '',
          error: error instanceof Error ? error.message : 'Unknown error'
        })
      }
    }

    return { attachments, totalTextContent, tempDir }
  }

  /**
   * Обработка одного вложения
   */
  private static async processAttachment(attachment: any, tempDir: string): Promise<{
    filename: string
    originalPath: string
    convertedPath?: string
    textContent?: string
    error?: string
  }> {
    const filename = attachment.filename || `attachment_${Date.now()}`
    const ext = path.extname(filename).toLowerCase()

    // Проверяем, поддерживается ли расширение
    if (!this.SUPPORTED_EXTENSIONS.includes(ext)) {
      return {
        filename,
        originalPath: '',
        error: `Неподдерживаемый тип файла: ${ext}`
      }
    }

    // Сохраняем вложение во временную папку
    const originalPath = path.join(tempDir, filename)
    fs.writeFileSync(originalPath, attachment.content)

    const result = {
      filename,
      originalPath
    }

    try {
      // Обрабатываем в зависимости от типа файла
      switch (ext) {
        case '.pdf':
          result.textContent = await this.extractTextFromPdf(originalPath)
          break
        case '.docx':
        case '.doc':
          result.textContent = await this.extractTextFromDoc(originalPath, tempDir)
          break
        case '.xlsx':
        case '.xls':
          result.textContent = await this.extractTextFromExcel(originalPath, tempDir)
          break
      }
    } catch (error) {
      result.error = error instanceof Error ? error.message : 'Unknown error'
    }

    return result
  }

  /**
   * Создание временной директории для вложений
   */
  private static async createTempDirectory(): Promise<string> {
    const uploadPath = path.resolve(this.UPLOAD_PATH)
    const tempDir = path.join(uploadPath, this.TEMP_ATTACHMENTS_FOLDER, `email_${Date.now()}`)
    
    if (!fs.existsSync(tempDir)) {
      fs.mkdirSync(tempDir, { recursive: true })
    }
    
    return tempDir
  }

  /**
   * Извлечение текста из PDF файла
   */
  private static async extractTextFromPdf(filePath: string): Promise<string> {
    try {
      // Проверяем наличие pdftotext
      try {
        await execAsync('which pdftotext')
      } catch {
        return 'PDF файл обнаружен, но pdftotext не установлен на сервере. Установите poppler-utils для извлечения текста из PDF.'
      }

      // Используем pdftotext для извлечения текста
      const { stdout } = await execAsync(`pdftotext "${filePath}" -`)
      return stdout.trim()
    } catch (error) {
      console.error('Ошибка извлечения текста из PDF:', error)
      return `Ошибка чтения PDF: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }

  /**
   * Извлечение текста из DOC/DOCX файла
   */
  private static async extractTextFromDoc(filePath: string, tempDir: string): Promise<string> {
    try {
      // Проверяем наличие LibreOffice
      try {
        await execAsync('which soffice')
      } catch {
        return 'DOC/DOCX файл обнаружен, но LibreOffice не установлен на сервере.'
      }

      // Сначала убиваем процессы soffice
      await this.killSofficeProcesses()

      // Конвертируем в текстовый файл с дополнительными параметрами для headless режима
      const textFileName = path.basename(filePath, path.extname(filePath)) + '.txt'
      const textFilePath = path.join(tempDir, textFileName)

      const command = `DISPLAY= soffice --norestore --headless --invisible --nodefault --nolockcheck --nologo --norestore --convert-to txt "${filePath}" --outdir "${tempDir}"`
      await execAsync(command, { timeout: 30000 }) // 30 секунд timeout

      // Убиваем процессы после конвертации
      await this.killSofficeProcesses()

      // Читаем получившийся текстовый файл
      if (fs.existsSync(textFilePath)) {
        const content = fs.readFileSync(textFilePath, 'utf-8')
        // Удаляем временный текстовый файл
        fs.unlinkSync(textFilePath)
        return content.trim()
      } else {
        return 'Не удалось извлечь текст из документа (файл не создан)'
      }
    } catch (error) {
      console.error('Ошибка извлечения текста из DOC/DOCX:', error)
      return `Ошибка чтения документа: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }

  /**
   * Извлечение текста из XLSX/XLS файла
   */
  private static async extractTextFromExcel(filePath: string, tempDir: string): Promise<string> {
    try {
      // Проверяем наличие LibreOffice
      try {
        await execAsync('which soffice')
      } catch {
        return 'XLSX/XLS файл обнаружен, но LibreOffice не установлен на сервере.'
      }

      // Сначала убиваем процессы soffice
      await this.killSofficeProcesses()

      // Конвертируем в CSV для извлечения данных с дополнительными параметрами
      const csvFileName = path.basename(filePath, path.extname(filePath)) + '.csv'
      const csvFilePath = path.join(tempDir, csvFileName)

      const command = `DISPLAY= soffice --norestore --headless --invisible --nodefault --nolockcheck --nologo --norestore --convert-to csv "${filePath}" --outdir "${tempDir}"`
      await execAsync(command, { timeout: 30000 }) // 30 секунд timeout

      // Убиваем процессы после конвертации
      await this.killSofficeProcesses()

      // Читаем получившийся CSV файл
      if (fs.existsSync(csvFilePath)) {
        const content = fs.readFileSync(csvFilePath, 'utf-8')
        // Удаляем временный CSV файл
        fs.unlinkSync(csvFilePath)

        // Форматируем CSV в более читаемый вид
        const lines = content.split('\n').filter(line => line.trim())
        return lines.join('\n').trim()
      } else {
        return 'Не удалось извлечь данные из таблицы (файл не создан)'
      }
    } catch (error) {
      console.error('Ошибка извлечения данных из XLSX/XLS:', error)
      return `Ошибка чтения таблицы: ${error instanceof Error ? error.message : 'Unknown error'}`
    }
  }

  /**
   * Завершение процессов soffice
   */
  private static async killSofficeProcesses(): Promise<void> {
    try {
      await execAsync('killall -9 soffice.bin')
    } catch (error) {
      // Игнорируем ошибку, если процессов для завершения не найдено
      if (!error.message.includes('no process found')) {
        console.warn('Предупреждение при завершении soffice процессов:', error.message)
      }
    }
  }

  /**
   * Немедленное удаление временной папки после обработки письма
   */
  public static async cleanupTempDir(tempDir: string): Promise<void> {
    try {
      if (tempDir && fs.existsSync(tempDir)) {
        fs.rmSync(tempDir, { recursive: true, force: true })
        console.log(`🧹 Удалена временная папка: ${path.basename(tempDir)}`)
      }
    } catch (error) {
      console.error('❌ Ошибка удаления временной папки:', error)
    }
  }

  /**
   * Очистка старых временных файлов (для экстренных случаев)
   */
  public static async cleanupTempFiles(olderThanHours: number = 24): Promise<void> {
    try {
      const uploadPath = path.resolve(this.UPLOAD_PATH)
      const tempAttachmentsPath = path.join(uploadPath, this.TEMP_ATTACHMENTS_FOLDER)
      
      if (!fs.existsSync(tempAttachmentsPath)) {
        return
      }

      const now = Date.now()
      const cutoffTime = now - (olderThanHours * 60 * 60 * 1000)

      const dirs = fs.readdirSync(tempAttachmentsPath)
      
      for (const dir of dirs) {
        const dirPath = path.join(tempAttachmentsPath, dir)
        const stats = fs.statSync(dirPath)
        
        if (stats.isDirectory() && stats.mtime.getTime() < cutoffTime) {
          // Удаляем старую папку с вложениями
          fs.rmSync(dirPath, { recursive: true, force: true })
          console.log(`Удалена старая папка с вложениями: ${dir}`)
        }
      }
    } catch (error) {
      console.error('Ошибка очистки временных файлов:', error)
    }
  }
}
