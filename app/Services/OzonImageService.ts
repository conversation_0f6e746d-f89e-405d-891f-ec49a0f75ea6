import * as fs from 'fs'
import * as path from 'path'
import Env from '@ioc:Adonis/Core/Env'
import { IOzonImageService } from 'App/Interfaces/ozon/OzonInterfaces'
import { OzonClient } from 'App/Clients/OzonClient'
import { ozonConfig } from 'App/Services/OzonConfig'

const UPLOAD_PATH = path.resolve(Env.get('UPLOAD_PATH'))

export class OzonImageService implements IOzonImageService {
  private ozonClient: OzonClient

  constructor() {
    this.ozonClient = new OzonClient()
  }

  /**
   * Получение локальных изображений товара
   */
  async getLocalImages(product: any): Promise<{
    mainImage?: string
    additionalImages: string[]
    imagePaths: string[]
  }> {
    const images: string[] = []
    const imagePaths: string[] = []
    let mainImage: string | undefined

    try {
      // Получаем настройки изображений
      const imageSettings = await ozonConfig.getImageSettings()
      if (!imageSettings.enabled) {
        return { additionalImages: [], imagePaths: [] }
      }

      // Основное изображение RTI
      if (product.prod_img) {
        const rtiImagePath = path.join(UPLOAD_PATH, 'original', `${product.prod_img}.jpg`)
        if (await this.checkImageExists(rtiImagePath)) {
          mainImage = `${product.prod_img}.jpg`
          images.push(mainImage)
          imagePaths.push(rtiImagePath)
        }
      }


      // Дополнительные изображения
      if (product.prod_images) {
        const additionalImageNames = product.prod_images
          .split(',')
          .filter((img: string) => img.trim())
          .slice(0, imageSettings.maxImages - 1) // Оставляем место для основного изображения

        for (const imageName of additionalImageNames) {
          const cleanImageName = imageName.trim()
          
          // Проверяем RTI версию
          const rtiPath = path.join(UPLOAD_PATH, 'original', cleanImageName)
          if (await this.checkImageExists(rtiPath)) {
            if (!images.includes(cleanImageName)) {
              images.push(cleanImageName)
              imagePaths.push(rtiPath)
            }
            continue
          }
        }
      }

      const additionalImages = images.filter(img => img !== mainImage)

      return {
        mainImage,
        additionalImages,
        imagePaths
      }

    } catch (error) {
      console.error('Error getting local images for product:', product.prod_id, error)
      return { additionalImages: [], imagePaths: [] }
    }
  }

  /**
   * Загрузка изображений в Ozon
   */
  async uploadToOzon(imagePaths: string[]): Promise<{
    success: boolean
    uploadedImages: { fileName: string, url: string }[]
    failed: { fileName: string, error: string }[]
  }> {
    const uploadedImages: { fileName: string, url: string }[] = []
    const failed: { fileName: string, error: string }[] = []

    if (imagePaths.length === 0) {
      return { success: true, uploadedImages, failed }
    }

    try {
      // Подготавливаем данные для загрузки
      const imageUploads = imagePaths.map(imagePath => ({
        file_name: path.basename(imagePath),
        url: this.getImageUrl(path.basename(imagePath), 'original') // Используем RTI по умолчанию
      }))

      // Загружаем изображения через Ozon API
      const response = await this.ozonClient.uploadImages(imageUploads)

      if (response.result?.pictures) {
        for (const picture of response.result.pictures) {
          if (picture.state === 'uploaded') {
            uploadedImages.push({
              fileName: picture.file_name,
              url: picture.url
            })
          } else {
            failed.push({
              fileName: picture.file_name,
              error: picture.error || 'Upload failed'
            })
          }
        }
      }

      return {
        success: failed.length === 0,
        uploadedImages,
        failed
      }

    } catch (error) {
      console.error('Error uploading images to Ozon:', error)
      
      // Добавляем все изображения в список неудачных
      imagePaths.forEach(imagePath => {
        failed.push({
          fileName: path.basename(imagePath),
          error: error.message || 'Upload failed'
        })
      })

      return {
        success: false,
        uploadedImages,
        failed
      }
    }
  }

  /**
   * Проверка существования файла изображения
   */
  async checkImageExists(imagePath: string): Promise<boolean> {
    try {
      await fs.promises.access(imagePath, fs.constants.F_OK)
      return true
    } catch {
      return false
    }
  }

  /**
   * Получение URL для доступа к изображению
   */
  getImageUrl(imageName: string): string {
    // Получаем базовый URL из настроек или используем значение по умолчанию
    const baseUrl = 'https://mirsalnikov.ru'
    return `${baseUrl}/${imageName}`
  }

  /**
   * Получение информации о загруженных изображениях из Ozon
   */
  async getUploadedImagesInfo(fileNames: string[]): Promise<{
    success: boolean
    images: { fileName: string, url: string, status: string }[]
    errors: string[]
  }> {
    if (fileNames.length === 0) {
      return { success: true, images: [], errors: [] }
    }

    try {
      const response = await this.ozonClient.getImagesInfo(fileNames)
      const images: { fileName: string, url: string, status: string }[] = []
      const errors: string[] = []

      if (response.result?.pictures) {
        for (const picture of response.result.pictures) {
          images.push({
            fileName: picture.file_name,
            url: picture.url,
            status: picture.state
          })

          if (picture.error) {
            errors.push(`${picture.file_name}: ${picture.error}`)
          }
        }
      }

      return {
        success: errors.length === 0,
        images,
        errors
      }

    } catch (error) {
      console.error('Error getting images info from Ozon:', error)
      return {
        success: false,
        images: [],
        errors: [error.message || 'Failed to get images info']
      }
    }
  }

  /**
   * Подготовка изображений для товара Ozon
   */
  async prepareProductImages(product: any): Promise<{
    success: boolean
    primaryImage?: string
    images: string[]
    errors: string[]
  }> {
    try {
      // Получаем локальные изображения
      const localImages = await this.getLocalImages(product)
      
      if (localImages.imagePaths.length === 0) {
        return {
          success: true,
          images: [],
          errors: ['No images found for product']
        }
      }

      // Загружаем изображения в Ozon
      const uploadResult = await this.uploadToOzon(localImages.imagePaths)
      
      if (!uploadResult.success) {
        return {
          success: false,
          images: [],
          errors: uploadResult.failed.map(f => f.error)
        }
      }

      // Формируем список URL изображений для Ozon
      const imageUrls = uploadResult.uploadedImages.map(img => img.url)
      const primaryImage = uploadResult.uploadedImages.find(img => 
        img.fileName === localImages.mainImage
      )?.url

      return {
        success: true,
        primaryImage,
        images: imageUrls,
        errors: uploadResult.failed.map(f => f.error)
      }

    } catch (error) {
      console.error('Error preparing product images:', error)
      return {
        success: false,
        images: [],
        errors: [error.message || 'Failed to prepare images']
      }
    }
  }

  /**
   * Очистка старых загруженных изображений
   */
  async cleanupOldImages(productId: number, currentImages: string[]): Promise<void> {
    // Здесь можно реализовать логику удаления старых изображений из Ozon
    // если они больше не используются товаром
    console.log(`Cleanup old images for product ${productId}:`, currentImages)
  }

  /**
   * Валидация изображений перед загрузкой
   */
  async validateImages(imagePaths: string[]): Promise<{
    valid: string[]
    invalid: { path: string, reason: string }[]
  }> {
    const valid: string[] = []
    const invalid: { path: string, reason: string }[] = []

    const imageSettings = await ozonConfig.getImageSettings()
    const maxFileSize = 10 * 1024 * 1024 // 10MB
    const allowedExtensions = ['.jpg', '.jpeg', '.png', '.gif']

    for (const imagePath of imagePaths) {
      try {
        // Проверяем существование файла
        if (!(await this.checkImageExists(imagePath))) {
          invalid.push({ path: imagePath, reason: 'File does not exist' })
          continue
        }

        // Проверяем расширение
        const ext = path.extname(imagePath).toLowerCase()
        if (!allowedExtensions.includes(ext)) {
          invalid.push({ path: imagePath, reason: 'Invalid file extension' })
          continue
        }

        // Проверяем размер файла
        const stats = await fs.promises.stat(imagePath)
        if (stats.size > maxFileSize) {
          invalid.push({ path: imagePath, reason: 'File too large' })
          continue
        }

        valid.push(imagePath)

      } catch (error) {
        invalid.push({ path: imagePath, reason: error.message })
      }
    }

    // Проверяем лимит количества изображений
    if (valid.length > imageSettings.maxImages) {
      const excess = valid.splice(imageSettings.maxImages)
      excess.forEach(path => {
        invalid.push({ path, reason: 'Exceeds maximum images limit' })
      })
    }

    return { valid, invalid }
  }
}
