import Mail from '@ioc:Adonis/Addons/Mail'
import Env from '@ioc:Adonis/Core/Env'
import { DateTime } from 'luxon'
import EmailLog from 'App/Models/EmailLog'
import Journal from 'App/Models/Journal'
import loadSettings from 'App/Helpers/loadSettings'
import {
  EmailSenderData,
  Recipient,
  EmailSendResult,
  EmailError,
  EmailCampaignConfig,
  EmailValidationResult,
  CampaignProgress,
  EmailServiceConfig,
  RetryConfig,
  FailedEmail
} from 'App/Interfaces/email/EmailInterfaces'
import View from '@ioc:Adonis/Core/View'

// Простая функция для генерации UUID
function generateUUID(): string {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
    const r = (Math.random() * 16) | 0
    const v = c === 'x' ? r : (r & 0x3) | 0x8
    return v.toString(16)
  })
}

export class EmailService {
  private defaultConfig: EmailCampaignConfig = {
    batchSize: 20,
    delayBetweenBatches: 15000, // 15 секунд
    delayBetweenEmails: 2000, // 2 секунды
    maxRetries: 3,
    retryDelay: 5000, // 5 секунд
    enableCheckpoints: true,
    validateEmails: true
  }

  private retryConfig: RetryConfig = {
    maxAttempts: 5,
    baseDelay: 2000,
    maxDelay: 60000,
    exponentialBackoff: true,
    temporaryErrorCodes: [451, 421, 'EENVELOPE', 'ETIMEDOUT', 'ECONNRESET']
  }

  private serviceConfig: EmailServiceConfig = {
    mailerName: 'robot_news',
    fromEmail: Env.get('EMAIL_NEWSROBOT_FROM'),
    fromName: Env.get('EMAIL_FROM_NAME'),
    defaultConfig: this.defaultConfig
  }

  private activeCampaigns: Map<string, CampaignProgress> = new Map()
  private shutdownRequested = false

  constructor(config?: Partial<EmailServiceConfig>) {
    if (config) {
      this.serviceConfig = { ...this.serviceConfig, ...config }
    }
  }

  /**
   * Получает данные настроек email рассылки
   */
  async getEmailSenderData(): Promise<EmailSenderData> {
    const res = await loadSettings(['emailsenderdata'])
    const emailsenderdata: EmailSenderData = res.emailsenderdata

    if (!emailsenderdata) {
      throw new Error('Настройки рассылки не найдены')
    }

    return emailsenderdata
  }

  /**
   * Обновляет данные настроек email рассылки
   */
  async updateEmailSenderData(payload: EmailSenderData): Promise<string> {
    // Валидация данных
    if (payload.scheduleType === 'weekly') {
      if (!payload.weeklySchedule || !this.validateWeeklySchedule(payload.weeklySchedule)) {
        throw new Error('Некорректное расписание дней недели')
      }
    }

    if (payload.scheduleType === 'interval') {
      if (!payload.scheduleTime || payload.scheduleTime <= 0) {
        throw new Error('Некорректный интервал отправки')
      }
    }

    const { $prisma } = await import('App/Services/Prisma')
    const res = await $prisma.settings.updateMany({
      where: { s_key: 'emailsenderdata' },
      data: { s_value: JSON.stringify(payload) }
    })

    return res.count > 0 ? 'success' : 'error'
  }

  /**
   * Валидирует email адрес
   */
  validateEmail(email: string): EmailValidationResult {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/

    if (!email || typeof email !== 'string') {
      return { isValid: false, error: 'Email не может быть пустым' }
    }

    if (!emailRegex.test(email)) {
      return { isValid: false, error: 'Некорректный формат email' }
    }

    // Дополнительные проверки
    if (email.length > 254) {
      return { isValid: false, error: 'Email слишком длинный' }
    }

    const [localPart, domain] = email.split('@')
    if (localPart.length > 64) {
      return { isValid: false, error: 'Локальная часть email слишком длинная' }
    }

    return { isValid: true }
  }

  /**
   * Отправляет одно email с повторными попытками
   */
  private async sendEmailWithRetry(
    recipient: Recipient,
    subject: string,
    message: string,
    campaignId: string,
    checkpointIndex: number,
    maxRetries = 3
  ): Promise<{ success: boolean; error?: string }> {
    // Создаем лог записи
    const emailLog = await EmailLog.createLog({
      campaignId,
      recipientEmail: recipient.email,
      recipientNote: recipient.note,
      subject,
      messagePreview: message.substring(0, 500),
      checkpointIndex,
      batchNumber: Math.floor(checkpointIndex / this.defaultConfig.batchSize)
    })

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        const tmpl = await View.renderRawSync(message, { ...recipient, name: recipient.note })
        await Mail.use(this.serviceConfig.mailerName).send((mail) => {
          mail.from(this.serviceConfig.fromEmail, this.serviceConfig.fromName).to(recipient.email).subject(subject).html(tmpl)
        })

        // Успешная отправка
        await emailLog.updateStatus('sent')
        return { success: true }
      } catch (error: any) {
        const isTemporaryError = this.isTemporaryError(error)

        if (isTemporaryError && attempt < maxRetries) {
          // Экспоненциальная задержка
          const delay = this.calculateRetryDelay(attempt)
          console.log(`Попытка ${attempt} неудачна для ${recipient.email}, повтор через ${delay}ms`)

          await emailLog.updateStatus('retry', `Попытка ${attempt}: ${error.message}`)
          await this.sleep(delay)
          continue
        }

        // Окончательная ошибка
        const errorMessage = `Окончательная ошибка после ${attempt} попыток: ${error.message}`
        console.error(`Ошибка отправки на ${recipient.email}:`, errorMessage)

        await emailLog.updateStatus('failed', errorMessage)
        return { success: false, error: errorMessage }
      }
    }

    return { success: false, error: 'Превышено количество попыток' }
  }

  /**
   * Проверяет, является ли ошибка временной
   */
  private isTemporaryError(error: any): boolean {
    return this.retryConfig.temporaryErrorCodes.some((code) => error.responseCode === code || error.code === code)
  }

  /**
   * Вычисляет задержку для повторной попытки
   */
  private calculateRetryDelay(attempt: number): number {
    if (!this.retryConfig.exponentialBackoff) {
      return this.retryConfig.baseDelay
    }

    const delay = Math.min(this.retryConfig.baseDelay * Math.pow(2, attempt - 1), this.retryConfig.maxDelay)

    // Добавляем случайный jitter для избежания thundering herd
    return delay + Math.random() * 1000
  }

  /**
   * Разбивает массив на батчи
   */
  private chunkArray<T>(array: T[], size: number): T[][] {
    const chunks: T[][] = []
    for (let i = 0; i < array.length; i += size) {
      chunks.push(array.slice(i, i + size))
    }
    return chunks
  }

  /**
   * Валидирует расписание дней недели
   */
  private validateWeeklySchedule(weeklySchedule: number[]): boolean {
    if (!Array.isArray(weeklySchedule)) return false
    if (weeklySchedule.length === 0) return false
    return weeklySchedule.every((day) => Number.isInteger(day) && day >= 0 && day <= 6)
  }

  /**
   * Утилита для задержки
   */
  private sleep(ms: number): Promise<void> {
    return new Promise((resolve) => setTimeout(resolve, ms))
  }

  /**
   * Проверяет, нужно ли остановить выполнение
   */
  private shouldStop(): boolean {
    return this.shutdownRequested
  }

  /**
   * Инициирует graceful shutdown
   */
  public requestShutdown(): void {
    this.shutdownRequested = true
    console.log('EmailService: Запрошена остановка сервиса')
  }

  /**
   * Основной метод массовой рассылки с checkpoint'ами
   */
  async sendBulkEmail(config?: Partial<EmailCampaignConfig>): Promise<EmailSendResult> {
    const campaignId = generateUUID()
    const finalConfig = { ...this.defaultConfig, ...config }

    console.log(`Начинаем кампанию ${campaignId} с конфигурацией:`, finalConfig)

    try {
      const data = await this.getEmailSenderData()
      const { subject, message } = data

      if (!data.enabled) {
        throw new Error('Рассылка отключена')
      }

      // Фильтруем активных получателей и валидируем email'ы
      const recipients = data.recipients.filter((recipient) => {
        if (!recipient.isActive) return false

        if (finalConfig.validateEmails) {
          const validation = this.validateEmail(recipient.email)
          if (!validation.isValid) {
            console.warn(`Пропускаем невалидный email ${recipient.email}: ${validation.error}`)
            return false
          }
        }

        return true
      })

      if (recipients.length === 0) {
        throw new Error('Нет активных получателей для рассылки')
      }

      // Проверяем, есть ли незавершенная кампания
      let startIndex = 0
      if (finalConfig.enableCheckpoints) {
        startIndex = await EmailLog.getLastCheckpoint(campaignId)
        if (startIndex > 0) {
          console.log(`Возобновляем рассылку с позиции ${startIndex}`)
        }
      }

      // Создаем прогресс кампании
      const progress: CampaignProgress = {
        campaignId,
        totalRecipients: recipients.length,
        processed: startIndex,
        sent: 0,
        failed: 0,
        pending: recipients.length - startIndex,
        currentBatch: Math.floor(startIndex / finalConfig.batchSize),
        lastCheckpoint: startIndex,
        startedAt: new Date(),
        status: 'running'
      }

      this.activeCampaigns.set(campaignId, progress)

      // Записываем начало кампании в журнал
      await Journal.createItem({
        entity: 'email_campaign',
        entity_id: campaignId,
        msg: `Начата email кампания. Получателей: ${recipients.length}, с позиции: ${startIndex}`
      })

      const remainingRecipients = recipients.slice(startIndex)
      const batches = this.chunkArray(remainingRecipients, finalConfig.batchSize)

      let sentCount = 0
      let failedCount = 0
      const errors: EmailError[] = []

      for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
        if (this.shouldStop()) {
          console.log('Остановка рассылки по запросу')
          progress.status = 'cancelled'
          break
        }

        const batch = batches[batchIndex]
        const currentBatchNumber = Math.floor(startIndex / finalConfig.batchSize) + batchIndex

        console.log(`Обрабатываем батч ${currentBatchNumber + 1}/${batches.length} (${batch.length} получателей)`)

        for (let emailIndex = 0; emailIndex < batch.length; emailIndex++) {
          if (this.shouldStop()) break

          const recipient = batch[emailIndex]
          const globalIndex = startIndex + batchIndex * finalConfig.batchSize + emailIndex

          const result = await this.sendEmailWithRetry(recipient, subject, message, campaignId, globalIndex, finalConfig.maxRetries)

          if (result.success) {
            sentCount++
            progress.sent++
          } else {
            failedCount++
            progress.failed++
            errors.push({
              recipientEmail: recipient.email,
              error: result.error || 'Неизвестная ошибка',
              timestamp: new Date(),
              isTemporary: false
            })
          }

          progress.processed++
          progress.pending--
          progress.lastCheckpoint = globalIndex

          // Обновляем прогресс в памяти
          this.activeCampaigns.set(campaignId, progress)

          // Задержка между письмами
          if (emailIndex < batch.length - 1) {
            await this.sleep(finalConfig.delayBetweenEmails)
          }
        }

        console.log(`Батч ${currentBatchNumber + 1} завершен. Отправлено: ${sentCount}, Ошибок: ${failedCount}`)

        // Задержка между батчами (кроме последнего)
        if (batchIndex < batches.length - 1 && !this.shouldStop()) {
          await this.sleep(finalConfig.delayBetweenBatches)
        }
      }

      // Завершаем кампанию
      progress.status = this.shouldStop() ? 'cancelled' : 'completed'
      this.activeCampaigns.set(campaignId, progress)

      // Записываем завершение в журнал
      await Journal.createItem({
        entity: 'email_campaign',
        entity_id: campaignId,
        msg: `Кампания завершена. Отправлено: ${sentCount}, Ошибок: ${failedCount}, Статус: ${progress.status}`
      })

      const result: EmailSendResult = {
        success: !this.shouldStop(),
        sentCount,
        failedCount,
        queuedForRetry: 0, // Будет вычислено отдельно
        campaignId,
        lastCheckpoint: progress.lastCheckpoint,
        errors
      }

      // Удаляем кампанию из активных через некоторое время
      setTimeout(() => {
        this.activeCampaigns.delete(campaignId)
      }, 300000) // 5 минут

      return result
    } catch (error) {
      console.error('Ошибка в sendBulkEmail:', error)

      // Записываем ошибку в журнал
      await Journal.createItem({
        entity: 'email_campaign',
        entity_id: campaignId,
        msg: `Ошибка кампании: ${error.message}`
      })

      throw error
    }
  }

  /**
   * Повторная отправка неудачных писем
   */
  async retryFailedEmails(campaignId?: string): Promise<EmailSendResult> {
    const failedEmails = await EmailLog.getFailedEmails(campaignId, this.retryConfig.maxAttempts)

    if (failedEmails.length === 0) {
      return {
        success: true,
        sentCount: 0,
        failedCount: 0,
        queuedForRetry: 0,
        campaignId: campaignId || 'retry-' + generateUUID(),
        lastCheckpoint: 0,
        errors: []
      }
    }

    console.log(`Повторная отправка ${failedEmails.length} неудачных писем`)

    let sentCount = 0
    let stillFailedCount = 0
    const errors: EmailError[] = []

    for (const emailLog of failedEmails) {
      if (this.shouldStop()) break

      try {
        await Mail.use(this.serviceConfig.mailerName).send((mail) => {
          mail
            .from(this.serviceConfig.fromEmail, this.serviceConfig.fromName)
            .to(emailLog.recipientEmail)
            .subject(emailLog.subject)
            .html(emailLog.messagePreview || '')
        })

        await emailLog.updateStatus('sent')
        sentCount++
      } catch (error: any) {
        await emailLog.updateStatus('failed', `Retry failed: ${error.message}`)
        stillFailedCount++

        errors.push({
          recipientEmail: emailLog.recipientEmail,
          error: error.message,
          timestamp: new Date(),
          isTemporary: this.isTemporaryError(error)
        })
      }

      // Задержка между повторными отправками
      await this.sleep(this.retryConfig.baseDelay)
    }

    await Journal.createItem({
      entity: 'email_retry',
      entity_id: campaignId || 'manual',
      msg: `Повторная отправка завершена. Успешно: ${sentCount}, Неудачно: ${stillFailedCount}`
    })

    return {
      success: true,
      sentCount,
      failedCount: stillFailedCount,
      queuedForRetry: stillFailedCount,
      campaignId: campaignId || 'retry-' + generateUUID(),
      lastCheckpoint: 0,
      errors
    }
  }

  /**
   * Проверяет, нужно ли запускать рассылку сейчас
   */
  private shouldSendNow(emailData: EmailSenderData): boolean {
    if (!emailData.enabled) {
      return false
    }

    const now = new Date()
    const lastSend = emailData.lastSendDate ? new Date(emailData.lastSendDate) : null

    if (emailData.scheduleType === 'interval') {
      if (!lastSend) return true
      const intervalMs = emailData.scheduleTime * 60 * 1000
      return now.getTime() - lastSend.getTime() >= intervalMs
    }

    if (emailData.scheduleType === 'weekly') {
      if (!emailData.weeklySchedule || emailData.weeklySchedule.length === 0) {
        return false
      }

      const today = now.getDay()
      const isScheduledDay = emailData.weeklySchedule.includes(today)

      if (!isScheduledDay) {
        return false
      }

      // Проверяем, не отправляли ли уже сегодня
      if (lastSend) {
        const lastSendDate = new Date(lastSend)
        const isToday = lastSendDate.toDateString() === now.toDateString()
        if (isToday) {
          return false
        }
      }

      return true
    }

    return false
  }

  /**
   * Вычисляет дату следующего запуска для еженедельного расписания
   */
  private calculateNextWeeklyRun(weeklySchedule: number[], timeOfDay: string = '09:00'): Date {
    if (!weeklySchedule || weeklySchedule.length === 0) {
      throw new Error('Расписание дней недели не может быть пустым')
    }

    const now = new Date()
    const [hours, minutes] = timeOfDay.split(':').map(Number)
    const sortedDays = [...weeklySchedule].sort((a, b) => a - b)

    // Проверяем, можем ли запустить сегодня
    const today = now.getDay()
    const todayScheduled = new Date(now)
    todayScheduled.setHours(hours, minutes, 0, 0)

    if (sortedDays.includes(today) && now < todayScheduled) {
      return todayScheduled
    }

    // Ищем следующий день из расписания
    let nextDay = -1

    for (const day of sortedDays) {
      if (day > today) {
        nextDay = day
        break
      }
    }

    if (nextDay === -1) {
      nextDay = sortedDays[0]
    }

    const nextRun = new Date(now)
    const daysUntilNext = nextDay > today ? nextDay - today : 7 - today + nextDay

    nextRun.setDate(now.getDate() + daysUntilNext)
    nextRun.setHours(hours, minutes, 0, 0)

    return nextRun
  }

  /**
   * Получает информацию о следующем запланированном запуске
   */
  async getNextScheduledRun(emailData?: EmailSenderData) {
    const data = emailData || (await this.getEmailSenderData())

    if (!data.enabled) {
      return {
        nextRun: null,
        message: 'Рассылка отключена'
      }
    }

    if (data.scheduleType === 'interval') {
      const lastSend = data.lastSendDate ? new Date(data.lastSendDate) : new Date()
      const nextRun = new Date(lastSend.getTime() + data.scheduleTime * 60 * 1000)

      return {
        nextRun,
        message: `Следующая отправка через ${data.scheduleTime} минут после последней`
      }
    }

    if (data.scheduleType === 'weekly') {
      if (!data.weeklySchedule || data.weeklySchedule.length === 0) {
        return {
          nextRun: null,
          message: 'Не выбраны дни недели для отправки'
        }
      }

      const nextRun = this.calculateNextWeeklyRun(data.weeklySchedule)
      const dayNames = ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота']
      const selectedDays = data.weeklySchedule.map((day) => dayNames[day]).join(', ')

      return {
        nextRun,
        message: `Следующая отправка: ${nextRun.toLocaleString('ru-RU')} (дни: ${selectedDays})`
      }
    }

    return {
      nextRun: null,
      message: 'Неизвестный тип расписания'
    }
  }

  /**
   * Отправка по расписанию
   */
  async sendScheduledEmail(config?: Partial<EmailCampaignConfig>): Promise<EmailSendResult> {
    const data = await this.getEmailSenderData()

    if (!this.shouldSendNow(data)) {
      const scheduleInfo = await this.getNextScheduledRun(data)
      throw new Error(`Рассылка не запланирована сейчас. ${scheduleInfo.message}`)
    }

    const result = await this.sendBulkEmail(config)

    // Обновляем время последней отправки
    await this.updateEmailSenderData({
      ...data,
      lastSendDate: new Date().toISOString()
    })

    return result
  }

  /**
   * Получает прогресс активной кампании
   */
  getCampaignProgress(campaignId: string): CampaignProgress | null {
    return this.activeCampaigns.get(campaignId) || null
  }

  /**
   * Получает список всех активных кампаний
   */
  getActiveCampaigns(): CampaignProgress[] {
    return Array.from(this.activeCampaigns.values())
  }

  /**
   * Получает статистику по кампании
   */
  async getCampaignStats(campaignId: string) {
    return await EmailLog.getCampaignStats(campaignId)
  }

  /**
   * Отменяет активную кампанию
   */
  cancelCampaign(campaignId: string): boolean {
    const campaign = this.activeCampaigns.get(campaignId)
    if (campaign) {
      campaign.status = 'cancelled'
      this.activeCampaigns.set(campaignId, campaign)
      return true
    }
    return false
  }

  /**
   * Очищает старые логи
   */
  async cleanupOldLogs(daysOld = 90): Promise<number> {
    return await EmailLog.cleanupOldLogs(daysOld)
  }
}

// Экспортируем singleton instance
export const emailService = new EmailService()
