import { TRPCError, initTRPC } from '@trpc/server'
import { resolveResponse } from '@trpc/server/http'
import { fetchRequestHandler } from '@trpc/server/adapters/fetch'
import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import { productRouter } from 'App/TRPC/productRouter'
import { langRouter } from 'App/TRPC/langRouter'
import { servicesRouter } from 'App/TRPC/servicesRouter'

import { AuthContract } from '@ioc:Adonis/Addons/Auth'
import User from 'App/Models/User'
import Client from 'App/Models/Client'
import { orderRouter } from 'App/TRPC/orderRouter'
import { aiRouter } from 'App/TRPC/aiRouter'
import { ozonRouter } from 'App/TRPC/ozonRouter'

export type CreateRouterParams = {
  router: typeof router
  publicProcedure: typeof publicProcedure
  authedProcedure: typeof authedProcedure
}

export const createContext = async (ctx: HttpContextContract) => {
  // //console.time('create context handle')

  let isAuth = false

  let sessionUser: User | Client | undefined

  try {
    sessionUser = await ctx.auth.use('api').authenticate()
    if (sessionUser?.user_id) {
      isAuth = true
    }
  } catch (error) {}

  try {
    sessionUser = await ctx.auth.authenticate()
    if (sessionUser?.client_id) {
      isAuth = true
    }
  } catch (error) {}

  // //console.timeEnd('create context handle')

  return {
    ctx,
    sessionUser,
    isAuth
  }
}

export type Context = Awaited<ReturnType<typeof createContext>>

export const t = initTRPC.context<Context>().create()

const isAuthenticated = t.middleware(async ({ctx, next }) => {
  // const isAuth = auth.isAuthenticated

const isAuth = ctx.isAuth

  if (!isAuth) {
    throw new TRPCError({
      code: 'UNAUTHORIZED',
      message: 'Not authenticated'
    })
  }

  return next(ctx)
})

export const middleware = t.middleware
export const mergeRouters = t.mergeRouters
export const router = t.router

export const publicProcedure = t.procedure
export const authedProcedure = t.procedure.use(isAuthenticated)

// export const appRouter = createRouter()

export function createRouter() {
  //TODO: rewrite router create method
  return router({
    products: productRouter({
      authedProcedure,
      publicProcedure,
      router
    }),
    langs: langRouter({
      authedProcedure,
      publicProcedure,
      router
    }),
    services: servicesRouter({
      authedProcedure,
      publicProcedure,
      router
    }),
    orders: orderRouter({
      authedProcedure,
      publicProcedure,
      router
    }),
    ai: aiRouter({
      authedProcedure,
      publicProcedure,
      router
    }),
    ozon: ozonRouter({
      authedProcedure,
      publicProcedure,
      router
    })
  })
}

// export const appRouter = createRouter()

export const handleHttpRequest = async (ctx: HttpContextContract) => {
  const { request, response } = ctx

  // console.log('trpc handleHttpRequest', ctx.auth.user)

  // try {
  //   const _user = await ctx.auth.authenticate()
  //   console.log("🚀 ~ handleHttpRequest ~ _user:", _user)
  // } catch (error) {
  //   console.log("🚀 ~ handleHttpRequest ~ error:", error)
  // }

  const result = await fetchRequestHandler({
    endpoint: '/trpc',
    req: new Request(request.completeUrl(true), {
      headers: request.headers(),
      body: request.method() !== 'GET' ? JSON.stringify(request.body()) : undefined,
      method: request.method()
    }),
    router: createRouter(),
    createContext: async () => {
      return await createContext(ctx)
    }
  })

  const responseText = await new Response(result.body).text()

  return response.status(result.status).header('content-type', 'application/json').send(responseText)
}

export const _handleHttpRequest = async (ctx: HttpContextContract) => {
  const { request, response } = ctx

  const { status, body, headers } = await fetchRequestHandler({
    endpoint: '/trpc',
    req: new Request(request.completeUrl(true), {
      headers: ctx.request.headers(),
      body: ctx.request.method() !== 'GET' && ctx.request.method() !== 'HEAD' ? ctx.request.body().value : void 0,
      method: ctx.request.method()
    }),
    createContext: async () => ctx,
    router: createRouter()
  })

  if (headers) {
    Object.keys(headers).forEach((key) => {
      const value = headers[key]
      if (value) response.header(key, value)
    })
  }

  response.status(status)
  response.send(body)

  // const url = new URL(request.completeUrl(true))
  // const path = url.pathname.slice('/trpc'.length + 1)

  // const { body, status, headers } = await resolveResponse({
  //   createContext: async () => ctx,
  //   router: createRouter(),
  //   // router: appRouter,
  //   path,
  //   req: {
  //     // ...request,
  //     url: request.completeUrl(true),
  //     query: request,
  //     method: request.method(),
  //     headers: new Headers(request.headers()),
  //     body: request.body().value
  //   }
  // })
  // if (headers) {
  //   Object.keys(headers).forEach((key) => {
  //     const value = headers[key]
  //     if (value) response.header(key, value)
  //   })
  // }

  // console.log('🚀 ~ handleHttpRequest ~ body:', body)
  // console.log('🚀 ~ handleHttpRequest ~ status:', status)

  // response.status(status)
  // response.send(body)
}

export type AppRouter = ReturnType<typeof createRouter>
