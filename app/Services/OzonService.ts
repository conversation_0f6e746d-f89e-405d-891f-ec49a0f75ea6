import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import loadSettings from 'App/Helpers/loadSettings'
import { $prisma } from 'App/Services/Prisma'

// Типы для Ozon API
interface OzonApiResponse<T = any> {
  result: T
  error?: {
    code: string
    message: string
    details?: any[]
  }
}

interface OzonSettings {
  ozon_api_key: string
  ozon_client_id: string
  ozon_api_url: string
  ozon_warehouse_id: number
  ozon_sync_enabled: boolean
  ozon_auto_sync_interval: number
  ozon_default_category_id: number
  ozon_image_upload_enabled: boolean
  ozon_image_base_url: string
  ozon_max_images_per_product: number
}

interface OzonProduct {
  offer_id: string
  name: string
  description?: string
  category_id: number
  price: string
  old_price?: string
  currency_code: string
  vat: string
  height?: number
  depth?: number
  width?: number
  dimension_unit?: string
  weight?: number
  weight_unit?: string
  images?: string[]
  primary_image?: string
  attributes: OzonProductAttribute[]
}

interface OzonProductAttribute {
  id: number
  values: { value: string }[]
}

interface OzonStock {
  offer_id: string
  stock: number
  warehouse_id: number
}

interface OzonPrice {
  offer_id: string
  price: string
  old_price?: string
  currency_code: string
}

interface OzonProductInfo {
  id: number
  name: string
  offer_id: string
  price: string
  stocks: any
  visible: boolean
  state: string
  status?: { state: string }
  errors?: { message: string }[]
}

// Rate Limiter
class RateLimiter {
  private requests: number[] = []
  private readonly maxRequests = 100
  private readonly timeWindow = 60000

  async canMakeRequest(): Promise<boolean> {
    const now = Date.now()
    this.requests = this.requests.filter(time => now - time < this.timeWindow)
    return this.requests.length < this.maxRequests
  }

  async waitForNextRequest(): Promise<void> {
    while (!(await this.canMakeRequest())) {
      await new Promise(resolve => setTimeout(resolve, 1000))
    }
  }

  recordRequest(): void {
    this.requests.push(Date.now())
  }
}

// Главный класс OzonService
export class OzonService {
  private axiosInstance: AxiosInstance
  private rateLimiter: RateLimiter
  private settingsCache: OzonSettings | null = null
  private cacheExpiry: number = 0
  private readonly CACHE_TTL = 60000

  // Список настроек Ozon
  private readonly OZON_SETTINGS_KEYS: (keyof OzonSettings)[] = [
    'ozon_api_key',
    'ozon_client_id',
    'ozon_api_url',
    'ozon_warehouse_id',
    'ozon_sync_enabled',
    'ozon_auto_sync_interval',
    'ozon_default_category_id',
    'ozon_image_upload_enabled',
    'ozon_image_base_url',
    'ozon_max_images_per_product'
  ]

  // Значения по умолчанию
  private readonly DEFAULT_SETTINGS: Partial<OzonSettings> = {
    ozon_api_url: 'https://api-seller.ozon.ru',
    ozon_warehouse_id: 0,
    ozon_sync_enabled: false,
    ozon_auto_sync_interval: 60,
    ozon_default_category_id: 0,
    ozon_image_upload_enabled: false,
    ozon_image_base_url: '',
    ozon_max_images_per_product: 10
  }

  constructor() {
    this.rateLimiter = new RateLimiter()

    // Создаем axios instance
    this.axiosInstance = axios.create({
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json'
      }
    })

    // Добавляем interceptor для заголовков
    this.axiosInstance.interceptors.request.use(async (config) => {
      await this.ensureSettings()

      if (this.settingsCache) {
        config.baseURL = this.settingsCache.ozon_api_url
        if (config.headers) {
          config.headers['Client-Id'] = this.settingsCache.ozon_client_id
          config.headers['Api-Key'] = this.settingsCache.ozon_api_key
        }
      }

      return config
    })
  }

  // === НАСТРОЙКИ ===

  private async ensureSettings(): Promise<void> {
    if (!this.settingsCache || Date.now() >= this.cacheExpiry) {
      await this.loadSettings()
    }
  }

  private async loadSettings(): Promise<void> {
    try {
      const settings = await loadSettings(this.OZON_SETTINGS_KEYS)

      this.settingsCache = {
        ...this.DEFAULT_SETTINGS,
        ...settings
      } as OzonSettings

      this.cacheExpiry = Date.now() + this.CACHE_TTL
    } catch (error) {
      console.error('Error loading Ozon settings:', error)
      this.settingsCache = this.DEFAULT_SETTINGS as OzonSettings
    }
  }

  async getSettings(): Promise<OzonSettings> {
    await this.ensureSettings()
    return this.settingsCache!
  }

  async updateSettings(settings: Partial<OzonSettings>): Promise<void> {
    const updatePromises = Object.entries(settings).map(async ([key, value]) => {
      const isJson = typeof value === 'object' && value !== null

      // Сначала пытаемся найти существующую настройку
      const existing = await $prisma.settings.findFirst({
        where: { s_key: key }
      })

      if (existing) {
        // Обновляем существующую
        await $prisma.settings.update({
          where: { s_id: existing.s_id },
          data: {
            s_value: isJson ? JSON.stringify(value) : String(value),
            json: isJson
          }
        })
      } else {
        // Создаем новую
        await $prisma.settings.create({
          data: {
            s_key: key,
            s_value: isJson ? JSON.stringify(value) : String(value),
            json: isJson,
            syst: false
          }
        })
      }
    })

    await Promise.all(updatePromises)
    this.clearCache()
  }

  async isEnabled(): Promise<boolean> {
    const settings = await this.getSettings()
    return settings.ozon_sync_enabled
  }

  private clearCache(): void {
    this.settingsCache = null
    this.cacheExpiry = 0
  }

  // === HTTP МЕТОДЫ ===

  private async makeRequest<T = any>(
    method: 'GET' | 'POST',
    endpoint: string,
    data?: any,
    params?: any,
    retryCount = 0
  ): Promise<OzonApiResponse<T>> {
    const maxRetries = 3

    try {
      await this.rateLimiter.waitForNextRequest()
      this.rateLimiter.recordRequest()

      const config: AxiosRequestConfig = {
        method,
        url: endpoint,
        data,
        params
      }

      const response: AxiosResponse<OzonApiResponse<T>> = await this.axiosInstance.request(config)
      return response.data

    } catch (error: any) {
      const shouldRetry = this.shouldRetryError(error)

      if (shouldRetry && retryCount < maxRetries) {
        const retryAfter = this.getRetryDelay(error)
        if (retryAfter) {
          await new Promise(resolve => setTimeout(resolve, retryAfter))
        }
        return this.makeRequest(method, endpoint, data, params, retryCount + 1)
      }

      throw new Error(`Ozon API Error: ${this.getErrorMessage(error)}`)
    }
  }

  private shouldRetryError(error: any): boolean {
    const status = error.response?.status
    return status === 429 || status >= 500 ||
           error.code === 'ECONNRESET' ||
           error.code === 'ETIMEDOUT'
  }

  private getRetryDelay(error: any): number {
    const status = error.response?.status
    if (status === 429) return 60000 // 1 минута
    if (status >= 500) return 5000   // 5 секунд
    if (error.code === 'ECONNRESET' || error.code === 'ETIMEDOUT') return 3000 // 3 секунды
    return 0
  }

  private getErrorMessage(error: any): string {
    if (error.response?.data?.error?.message) {
      return error.response.data.error.message
    }
    if (error.response?.data?.message) {
      return error.response.data.message
    }
    if (error.message) {
      return error.message
    }
    return 'Unknown error'
  }

  async get<T = any>(endpoint: string, params?: any): Promise<OzonApiResponse<T>> {
    return this.makeRequest('GET', endpoint, undefined, params)
  }

  async post<T = any>(endpoint: string, data?: any): Promise<OzonApiResponse<T>> {
    return this.makeRequest('POST', endpoint, data)
  }

  // === ОСНОВНЫЕ МЕТОДЫ API ===

  /**
   * Маппинг товара из БД в формат Ozon
   */
  private async mapProductToOzon(product: any): Promise<OzonProduct> {
    const settings = await this.getSettings()

    // Базовые атрибуты
    const attributes: OzonProductAttribute[] = []

    // Бренд
    if (product.prod_manuf) {
      attributes.push({
        id: 85, // ID атрибута "Бренд"
        values: [{ value: 'SNF' }] // Фиксированный бренд
      })
    }

    // Модель
    if (product.prod_model) {
      attributes.push({
        id: 9048, // ID атрибута "Модель"
        values: [{ value: String(product.prod_model) }]
      })
    }

    // Материал
    if (product.prod_material) {
      attributes.push({
        id: 10096, // ID атрибута "Материал"
        values: [{ value: String(product.prod_material) }]
      })
    }

    const ozonProduct: OzonProduct = {
      offer_id: `product_${product.prod_id}`,
      name: this.truncateString(`${product.prod_purpose} ${product.prod_sku}/${product.prod_sku}, размер: ${product.prod_size}, тип: ${product.prod_type}`, 500),
      description: this.truncateString(`${product.prod_uses}, ${product.prod_note}`, 4000),
      category_id: parseInt(product.prod_cat) || settings.ozon_default_category_id,
      price: String(Number(product.prod_price) + 500), // Добавляем наценку
      currency_code: 'RUB',
      vat: '0', // НДС 0%
      height: product.size_h || undefined,
      width: product.size_out || undefined,
      depth: product.size_in || undefined,
      dimension_unit: 'mm',
      weight: this.parseWeight(product.prod_weight),
      weight_unit: 'g',
      images: [], // Пока без изображений
      attributes
    }

    return ozonProduct
  }

  // === ОСНОВНЫЕ МЕТОДЫ ДЛЯ РАБОТЫ С ТОВАРАМИ ===

  /**
   * Создание товара в Ozon
   */
  async createProduct(productData: OzonProduct): Promise<{
    success: boolean
    productId?: number
    error?: string
  }> {
    try {
      const response = await this.post('/v2/product/import', {
        items: [productData]
      })

      if (response.result) {
        return {
          success: true,
          productId: response.result.task_id
        }
      }

      return {
        success: false,
        error: 'No result in response'
      }

    } catch (error: any) {
      return {
        success: false,
        error: error.message || 'Failed to create product'
      }
    }
  }

  /**
   * Получение информации о товаре
   */
  async getProductInfo(offerId: string): Promise<OzonProductInfo | null> {
    try {
      const response = await this.post('/v2/product/info', {
        offer_id: [offerId]
      })

      if (response.result?.items && response.result.items.length > 0) {
        return response.result.items[0]
      }

      return null

    } catch (error) {
      console.error(`Error getting product info for ${offerId}:`, error)
      return null
    }
  }

  /**
   * Обновление остатков
   */
  async updateStocks(stocks: OzonStock[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }> {
    try {
      const response = await this.post('/v1/product/info/stocks', { stocks })

      return {
        success: !!response.result,
        updated: stocks.length,
        failed: []
      }

    } catch (error: any) {
      return {
        success: false,
        updated: 0,
        failed: stocks.map(stock => ({
          offerId: stock.offer_id,
          error: error.message || 'Failed to update stock'
        }))
      }
    }
  }

  /**
   * Обновление цен
   */
  async updatePrices(prices: OzonPrice[]): Promise<{
    success: boolean
    updated: number
    failed: { offerId: string, error: string }[]
  }> {
    try {
      const response = await this.post('/v1/product/info/prices', { prices })

      return {
        success: !!response.result,
        updated: prices.length,
        failed: []
      }

    } catch (error: any) {
      return {
        success: false,
        updated: 0,
        failed: prices.map(price => ({
          offerId: price.offer_id,
          error: error.message || 'Failed to update price'
        }))
      }
    }
  }

  // === МЕТОДЫ ДЛЯ tRPC РОУТЕРА ===

  /**
   * Синхронизация товаров
   */
  async syncProducts(productIds: number[], batchSize = 10, uploadImages = true): Promise<{
    success: boolean
    total: number
    synced: number
    failed: number
    results: any
    message: string
  }> {
    // Проверяем, включена ли синхронизация
    const isEnabled = await this.isEnabled()
    if (!isEnabled) {
      throw new Error('Ozon synchronization is disabled. Please enable it in settings.')
    }

    const success: number[] = []
    const failed: { productId: number, error: string }[] = []

    // Получаем товары из БД
    const products = await $prisma.products.findMany({
      where: {
        prod_id: {
          in: productIds
        }
      }
    })

    if (products.length === 0) {
      throw new Error('No products found')
    }

    // Синхронизируем каждый товар
    for (const product of products) {
      try {
        console.log(`Syncing product ${product.prod_id} (${product.prod_sku})`)

        // Маппим товар в формат Ozon
        const ozonProduct = await this.mapProductToOzon(product)

        // Проверяем, существует ли товар в Ozon
        const existingProduct = await this.getProductInfo(product.prod_sku!)

        if (existingProduct) {
          // Обновляем существующий товар
          await this.createProduct(ozonProduct) // Используем тот же метод для обновления
        } else {
          // Создаем новый товар
          await this.createProduct(ozonProduct)
        }

        // Обновляем остатки
        const settings = await this.getSettings()
        await this.updateStocks([{
          offer_id: product.prod_sku!,
          stock: product.prod_count,
          warehouse_id: settings.ozon_warehouse_id
        }])

        success.push(product.prod_id)

      } catch (error: any) {
        console.error(`Error syncing product ${product.prod_id}:`, error)
        failed.push({
          productId: product.prod_id,
          error: error.message || 'Unknown error'
        })
      }
    }

    return {
      success: failed.length === 0,
      total: productIds.length,
      synced: success.length,
      failed: failed.length,
      results: { success, failed },
      message: `Synced ${success.length} of ${productIds.length} products`
    }
  }

  /**
   * Получение статуса товара
   */
  async getProductStatus(productId?: number, offerId?: string): Promise<{
    offerId: string
    status: string
    errors: string[]
    isActive: boolean
    productInfo: any
  }> {
    let finalOfferId = offerId

    // Если передан productId, получаем offerId из БД
    if (productId && !finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    if (!finalOfferId) {
      throw new Error('No offer ID available')
    }

    try {
      const productInfo = await this.getProductInfo(finalOfferId)

      if (!productInfo) {
        return {
          offerId: finalOfferId,
          status: 'not_found',
          errors: ['Product not found'],
          isActive: false,
          productInfo: null
        }
      }

      return {
        offerId: finalOfferId,
        status: productInfo.status?.state || 'unknown',
        errors: productInfo.errors?.map(e => e.message) || [],
        isActive: productInfo.visible,
        productInfo: {
          id: productInfo.id,
          name: productInfo.name,
          price: productInfo.price,
          stocks: productInfo.stocks,
          visible: productInfo.visible,
          state: productInfo.state
        }
      }

    } catch (error: any) {
      return {
        offerId: finalOfferId,
        status: 'error',
        errors: [error.message || 'Failed to get status'],
        isActive: false,
        productInfo: null
      }
    }
  }

  /**
   * Обновление остатков товара
   */
  async updateProductStock(productId: number, stock: number, offerId?: string): Promise<{
    success: boolean
    offerId: string
    stock: number
    message: string
  }> {
    let finalOfferId = offerId

    // Получаем offerId из БД если не передан
    if (!finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    const settings = await this.getSettings()

    const result = await this.updateStocks([{
      offer_id: finalOfferId,
      stock: stock,
      warehouse_id: settings.ozon_warehouse_id
    }])

    console.log(`Stock update for product ${productId}: ${result.success ? 'success' : 'failed'}`)

    return {
      success: result.success,
      offerId: finalOfferId,
      stock: stock,
      message: result.success ? 'Stock updated successfully' : 'Failed to update stock'
    }
  }

  /**
   * Обновление цены товара
   */
  async updateProductPrice(productId: number, price: number, oldPrice?: number, offerId?: string, additionalPrice = 500): Promise<{
    success: boolean
    offerId: string
    price: number
    oldPrice?: number
    message: string
  }> {
    let finalOfferId = offerId

    // Получаем offerId из БД если не передан
    if (!finalOfferId) {
      const product = await $prisma.products.findUnique({
        where: { prod_id: productId },
        select: { prod_sku: true }
      })

      if (!product?.prod_sku) {
        throw new Error('Product not found or has no SKU')
      }

      finalOfferId = product.prod_sku
    }

    const result = await this.updatePrices([
      {
        offer_id: finalOfferId,
        price: String(price + additionalPrice),
        old_price: oldPrice ? String(oldPrice) : undefined,
        currency_code: 'RUB'
      }
    ])

    console.log(`Price update for product ${productId}: ${result.success ? 'success' : 'failed'}`)

    return {
      success: result.success,
      offerId: finalOfferId,
      price: price + additionalPrice,
      oldPrice: oldPrice,
      message: result.success ? 'Price updated successfully' : 'Failed to update price'
    }
  }

  /**
   * Загрузка изображений товара
   */
  async uploadProductImagesForRouter(productId: number): Promise<{
    success: boolean
    uploadedImages: string[]
    failed: { fileName: string, error: string }[]
    message: string
  }> {
    // Пока возвращаем заглушку
    return {
      success: true,
      uploadedImages: [],
      failed: [],
      message: 'Image upload not implemented yet'
    }
  }

  /**
   * Получение настроек Ozon
   */
  async getOzonSettingsForRouter(): Promise<{
    settings: OzonSettings
    validation: any
    stats: any
    isConfigured: boolean
    isEnabled: boolean
  }> {
    const settings = await this.getSettings()

    // Простая валидация
    const errors: string[] = []
    if (!settings.ozon_api_key) errors.push('API key not set')
    if (!settings.ozon_client_id) errors.push('Client ID not set')
    if (!settings.ozon_warehouse_id) errors.push('Warehouse ID not set')

    const validation = {
      isValid: errors.length === 0,
      errors
    }

    const stats = {
      totalSettings: this.OZON_SETTINGS_KEYS.length,
      configuredSettings: Object.values(settings).filter(v => v !== null && v !== undefined && v !== '').length,
      missingSettings: validation.errors
    }

    return {
      settings,
      validation,
      stats,
      isConfigured: validation.isValid,
      isEnabled: settings.ozon_sync_enabled
    }
  }

  /**
   * Обновление настроек Ozon
   */
  async updateOzonSettingsForRouter(settings: Partial<OzonSettings>): Promise<{
    success: boolean
    message: string
  }> {
    await this.updateSettings(settings)

    return {
      success: true,
      message: 'Settings updated successfully'
    }
  }

  /**
   * Получение истории синхронизации
   */
  async getSyncHistoryForRouter(productId?: number, limit = 100, offset = 0): Promise<{
    history: any[]
    total: number
    hasMore: boolean
  }> {
    // Пока возвращаем пустую историю
    return {
      history: [],
      total: 0,
      hasMore: false
    }
  }

  /**
   * Проверка подключения к Ozon API
   */
  async testConnection(): Promise<{
    success: boolean
    settings: any
    message: string
  }> {
    try {
      // Простая проверка - пытаемся получить дерево категорий
      await this.get('/v2/category/tree', { language: 'DEFAULT' })
      const settings = await this.getSettings()

      return {
        success: true,
        settings: {
          hasApiKey: !!settings.ozon_api_key,
          hasClientId: !!settings.ozon_client_id,
          apiUrl: settings.ozon_api_url,
          warehouseId: settings.ozon_warehouse_id
        },
        message: 'Connection successful'
      }

    } catch (error: any) {
      return {
        success: false,
        settings: null,
        message: `Connection test failed: ${error.message}`
      }
    }
  }

  /**
   * Получение статистики синхронизации
   */
  async getSyncStats(): Promise<{
    totalProducts: number
    syncedProducts: number
    failedProducts: number
    lastSyncTime: Date | null
    averageSyncTime: number
    errorRate: number
    recentErrors: string[]
  }> {
    // Пока возвращаем заглушку
    return {
      totalProducts: 0,
      syncedProducts: 0,
      failedProducts: 0,
      lastSyncTime: null,
      averageSyncTime: 0,
      errorRate: 0,
      recentErrors: []
    }
  }

  // === УТИЛИТЫ ===

  private truncateString(str: string, maxLength: number): string {
    if (!str) return ''
    if (str.length <= maxLength) return str
    return str.substring(0, maxLength - 3) + '...'
  }

  private parseWeight(weight: string | number): number {
    if (typeof weight === 'number') return weight
    if (typeof weight === 'string') {
      const parsed = parseFloat(weight.replace(/[^\d.,]/g, '').replace(',', '.'))
      return isNaN(parsed) ? 0 : Math.round(parsed * 1000) // Конвертируем в граммы
    }
    return 0
  }
}

// Экспортируем singleton instance
export const ozonService = new OzonService()
