import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Products extends BaseSchema {
  protected tableName = 'products'

  public async up () {
    this.schema.alterTable(this.tableName, (table) => {
      table.decimal('size_in')
      table.decimal('size_in_2')

      table.decimal('size_out')
      table.decimal('size_out_2')

      table.decimal('size_h')
      table.decimal('size_h_2')

    })
  }

  public async down () {
    this.schema.alterTable(this.tableName, (table) => {
    })
  }
}
