import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class Blockstables extends BaseSchema {
  protected tableName = 'locks'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')

      /**
       * Uses timestamptz for PostgreSQL and DATETIME2 for MSSQL
       */
      table.timestamp('created_at', { useTz: true })
      table.timestamp('updated_at', { useTz: true })

      table.string('entity')
      table.integer('entity_id')

      table.string('user_name')
      table.integer('user_id')

      table.boolean('active').defaultTo(true)
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
