import BaseSchema from '@ioc:Adonis/Lucid/Schema'

export default class CartItems extends BaseSchema {
  protected tableName = 'cart_items'

  public async up () {
    this.schema.createTable(this.tableName, (table) => {
      table.increments('id')
      table.timestamps(true)
      table.integer('cart_id')
      table.integer('prod_id')
      table.integer('qty')
    })
  }

  public async down () {
    this.schema.dropTable(this.tableName)
  }
}
