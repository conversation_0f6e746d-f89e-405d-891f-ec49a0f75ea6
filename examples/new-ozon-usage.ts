/**
 * Примеры использования нового упрощенного OzonService
 * 
 * После рефакторинга вся функциональность объединена в один сервис,
 * что делает использование более простым и понятным.
 */

import { ozonService } from 'App/Services/OzonService'

// ===== БАЗОВЫЕ ОПЕРАЦИИ =====

/**
 * Пример 1: Настройка интеграции с Ozon
 */
async function setupOzonIntegration() {
  console.log('🔧 Настройка интеграции с Ozon...')

  // Обновляем основные настройки
  await ozonService.updateSettings({
    ozon_api_key: 'your-api-key',
    ozon_client_id: 'your-client-id',
    ozon_warehouse_id: 12345,
    ozon_sync_enabled: true,
    ozon_auto_sync_interval: 30,
    ozon_default_category_id: 17029016,
    ozon_image_upload_enabled: true,
    ozon_max_images_per_product: 10
  })

  // Проверяем подключение
  const connectionTest = await ozonService.testConnection()
  if (connectionTest.success) {
    console.log('✅ Подключение к Ozon API успешно!')
  } else {
    console.error('❌ Ошибка подключения:', connectionTest.message)
  }
}

/**
 * Пример 2: Синхронизация товаров
 */
async function syncProductsExample() {
  console.log('🔄 Синхронизация товаров с Ozon...')

  // Синхронизируем конкретные товары
  const productIds = [1001, 1002, 1003, 1004, 1005]
  
  const result = await ozonService.syncProducts(productIds, 10, true)
  
  console.log(`📊 Результат синхронизации:`)
  console.log(`- Всего товаров: ${result.total}`)
  console.log(`- Успешно: ${result.synced}`)
  console.log(`- Ошибок: ${result.failed}`)
  console.log(`- Сообщение: ${result.message}`)

  if (result.failed > 0) {
    console.log('❌ Товары с ошибками:', result.results.failed)
  }
}

/**
 * Пример 3: Обновление остатков
 */
async function updateStockExample() {
  console.log('📦 Обновление остатков товаров...')

  const updates = [
    { productId: 1001, stock: 50 },
    { productId: 1002, stock: 25 },
    { productId: 1003, stock: 0 }  // Товар закончился
  ]

  for (const update of updates) {
    try {
      const result = await ozonService.updateProductStock(
        update.productId, 
        update.stock
      )
      
      if (result.success) {
        console.log(`✅ Остатки товара ${update.productId} обновлены: ${update.stock}`)
      } else {
        console.log(`❌ Ошибка обновления остатков товара ${update.productId}`)
      }
    } catch (error) {
      console.error(`💥 Критическая ошибка для товара ${update.productId}:`, error.message)
    }
  }
}

/**
 * Пример 4: Обновление цен
 */
async function updatePricesExample() {
  console.log('💰 Обновление цен товаров...')

  const priceUpdates = [
    { productId: 1001, price: 1500, oldPrice: 2000 }, // Со скидкой
    { productId: 1002, price: 800 },                  // Без скидки
    { productId: 1003, price: 2500, oldPrice: 3000 }  // Со скидкой
  ]

  for (const update of priceUpdates) {
    try {
      const result = await ozonService.updateProductPrice(
        update.productId,
        update.price,
        update.oldPrice
      )
      
      if (result.success) {
        const priceInfo = update.oldPrice 
          ? `${result.price} руб. (было ${update.oldPrice} руб.)`
          : `${result.price} руб.`
        console.log(`✅ Цена товара ${update.productId} обновлена: ${priceInfo}`)
      } else {
        console.log(`❌ Ошибка обновления цены товара ${update.productId}`)
      }
    } catch (error) {
      console.error(`💥 Критическая ошибка для товара ${update.productId}:`, error.message)
    }
  }
}

/**
 * Пример 5: Получение статуса товаров
 */
async function checkProductStatusExample() {
  console.log('🔍 Проверка статуса товаров в Ozon...')

  const productIds = [1001, 1002, 1003]

  for (const productId of productIds) {
    try {
      const status = await ozonService.getProductStatus(productId)
      
      console.log(`📋 Товар ${productId}:`)
      console.log(`  - Offer ID: ${status.offerId}`)
      console.log(`  - Статус: ${status.status}`)
      console.log(`  - Активен: ${status.isActive ? 'Да' : 'Нет'}`)
      
      if (status.errors.length > 0) {
        console.log(`  - Ошибки: ${status.errors.join(', ')}`)
      }
      
      if (status.productInfo) {
        console.log(`  - Цена в Ozon: ${status.productInfo.price} руб.`)
        console.log(`  - Видимость: ${status.productInfo.visible ? 'Видим' : 'Скрыт'}`)
      }
    } catch (error) {
      console.error(`💥 Ошибка получения статуса товара ${productId}:`, error.message)
    }
  }
}

// ===== АДМИНИСТРАТИВНЫЕ ОПЕРАЦИИ =====

/**
 * Пример 6: Мониторинг и статистика
 */
async function monitoringExample() {
  console.log('📊 Получение статистики синхронизации...')

  // Получаем текущие настройки
  const settings = await ozonService.getOzonSettingsForRouter()
  console.log('⚙️ Статус интеграции:')
  console.log(`  - Настроена: ${settings.isConfigured ? 'Да' : 'Нет'}`)
  console.log(`  - Включена: ${settings.isEnabled ? 'Да' : 'Нет'}`)
  
  if (!settings.validation.isValid) {
    console.log('  - Ошибки настроек:', settings.validation.errors)
  }

  // Получаем статистику
  const stats = await ozonService.getSyncStats()
  console.log('📈 Статистика:')
  console.log(`  - Всего товаров: ${stats.totalProducts}`)
  console.log(`  - Синхронизировано: ${stats.syncedProducts}`)
  console.log(`  - Ошибок: ${stats.failedProducts}`)
  console.log(`  - Последняя синхронизация: ${stats.lastSyncTime || 'Никогда'}`)
}

/**
 * Пример 7: Массовые операции
 */
async function bulkOperationsExample() {
  console.log('🚀 Массовые операции с товарами...')

  // Получаем список товаров для синхронизации (пример)
  const { $prisma } = await import('App/Services/Prisma')
  
  const products = await $prisma.products.findMany({
    where: {
      prod_count: { gt: 0 },        // Только товары в наличии
      prod_price: { gt: 0 },        // С ценой больше 0
      prod_sku: { not: null }       // С артикулом
    },
    take: 50,                       // Берем первые 50
    select: {
      prod_id: true,
      prod_sku: true,
      prod_count: true,
      prod_price: true
    }
  })

  console.log(`📦 Найдено ${products.length} товаров для обработки`)

  // Синхронизируем товары батчами по 10
  const batchSize = 10
  const productIds = products.map(p => p.prod_id)
  
  for (let i = 0; i < productIds.length; i += batchSize) {
    const batch = productIds.slice(i, i + batchSize)
    console.log(`🔄 Обрабатываем батч ${Math.floor(i / batchSize) + 1}...`)
    
    try {
      const result = await ozonService.syncProducts(batch, batchSize, true)
      console.log(`  ✅ Батч завершен: ${result.synced}/${result.total} успешно`)
      
      // Небольшая пауза между батчами
      await new Promise(resolve => setTimeout(resolve, 2000))
    } catch (error) {
      console.error(`  ❌ Ошибка в батче:`, error.message)
    }
  }
}

// ===== ЗАПУСК ПРИМЕРОВ =====

/**
 * Главная функция для демонстрации всех возможностей
 */
async function runAllExamples() {
  console.log('🎯 Демонстрация нового OzonService\n')

  try {
    // Раскомментируйте нужные примеры:
    
    // await setupOzonIntegration()
    // await syncProductsExample()
    // await updateStockExample()
    // await updatePricesExample()
    // await checkProductStatusExample()
    await monitoringExample()
    // await bulkOperationsExample()

    console.log('\n🎉 Все примеры выполнены успешно!')
  } catch (error) {
    console.error('\n💥 Критическая ошибка:', error.message)
  }
}

// Экспортируем функции для использования
export {
  setupOzonIntegration,
  syncProductsExample,
  updateStockExample,
  updatePricesExample,
  checkProductStatusExample,
  monitoringExample,
  bulkOperationsExample,
  runAllExamples
}

// Если файл запущен напрямую
if (require.main === module) {
  runAllExamples()
}
