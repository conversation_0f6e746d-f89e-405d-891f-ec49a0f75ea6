import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import { SMSruClass } from "App/Helpers/SMSru"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"


const SMSru = new SMSruClass()

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
    
    const { clientIDM } = request.qs()
    const ip = request.ip()

    return await SMSru.restorePassword(clientIDM, ip)    
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/sms/restorepwd'
}

export default plugin