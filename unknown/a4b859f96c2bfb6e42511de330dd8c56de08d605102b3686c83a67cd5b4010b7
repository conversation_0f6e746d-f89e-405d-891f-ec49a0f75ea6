import Database from '@ioc:Adonis/Lucid/Database'

interface Settings {
  [key: string]: any
}

export default async function <T extends { [key: string]: any }>(paramskey?: Array<keyof T>, syst = undefined): Promise<T> {
  let settings: Settings = {}
  let defaultParams = [
    'currency',
    'stripekeyclient',
    'paypalkeyclient',
    'DISCOUNT_START_SUM',
    'google_analytics_key',
    'rumisota.retail',
    'google_analytics_key_rti',
    'pochta.standard.maxValue',
  ]

  const res = await Database.from('settings')
    .select('s_value as value', 's_key as key', 'json')
    .where((query) => {
      if (paramskey?.length) paramskey.map((key) => query.orWhere('s_key', key))
      else defaultParams.map((key) => query.orWhere('s_key', key))
    })
    .if(typeof syst !== 'undefined', (query) => {
      query.andWhere('syst', false)
    })
  // .if(syst, query => {
  //     query.andWhere('syst', !!syst)
  // })

  res.map((param) => {
    if (param.json) {
      try {
        param.value = JSON.parse(param.value)
        settings[param.key] = param.value
      } catch (error) {
        console.log('Error parse param: ' + param.key + ', getClientSettings: ' + error)
      }
    } else {
      if (!isNaN(Number(param.value))) param.value = Number(param.value)
      settings[param.key] = param.value
    }
  })

  return settings
}
