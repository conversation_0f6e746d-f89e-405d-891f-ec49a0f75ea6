function _getRange(inVal: number, outVal?: number, heightVal?: any, tollerance:number = 0.3, withReverse?: boolean) {

    const fxx = (n) => Number((n).toFixed(1))
    const push = (arr, ni) => arr.indexOf(fxx(ni)) == -1 ? arr.push(fxx(ni)) : ''
    

    tollerance > 1 ? tollerance = 1 : false

    inVal = Number(inVal)
    
    if (outVal) outVal = Number(outVal) 
    if (heightVal) heightVal = Number(heightVal)

    let in_sizes:Array<number> = []
    let out_sizes: Array<any> = []
    let heightVal_sizes: Array<number> = []

    let i = 0
    while (i < tollerance) {
        i = fxx(i + 0.1)

        //in_sizes.push(...[inVal, fxx(inVal + i), fxx(inVal - i)])
        push(in_sizes, inVal)
        push(in_sizes, (inVal + i))
        push(in_sizes, (inVal - i))
        
        if (outVal) {
            //out_sizes.push(...[outVal, fxx(outVal + i), fxx(outVal - i)])
            push(out_sizes, outVal)
            push(out_sizes, (outVal + i))
            push(out_sizes, (outVal - i))
        }
        else out_sizes.push('%')
        
        if (heightVal) {
            //heightVal_sizes.push(...[heightVal, fxx(heightVal + i), fxx(heightVal - i)])
            push(heightVal_sizes, heightVal)
            push(heightVal_sizes, (heightVal + i))
            push(heightVal_sizes, (heightVal - i))
        }
    }

    in_sizes.sort((a, b) => a - b)
    out_sizes.sort((a, b) => a - b)
    heightVal_sizes.sort((a, b) => a - b)

    let resultArray: number[][] = []

    if (heightVal) {
        in_sizes.forEach(item => out_sizes.forEach(subItem => heightVal_sizes.forEach(subzeroItem => resultArray.push([item, subItem, subzeroItem]))))
        if (withReverse) {
            out_sizes.forEach(item => in_sizes.forEach(subItem => heightVal_sizes.forEach(subzeroItem => resultArray.push([item, subItem, subzeroItem]))))
        }
/*         heightVal_sizes.forEach(item => in_sizes.forEach(subItem => out_sizes.forEach(subzeroItem => resultArray.push([item, subItem, subzeroItem]))))
        if (withReverse) {
            heightVal_sizes.forEach(item => out_sizes.forEach(subItem => in_sizes.forEach(subzeroItem => resultArray.push([item, subItem, subzeroItem]))))
        } */
    } else {
        in_sizes.forEach(item => out_sizes.forEach(subItem => resultArray.push([item, subItem])))
        if (withReverse) {
            out_sizes.forEach(item => in_sizes.forEach(subItem => resultArray.push([item, subItem])))
        }
    }

    return resultArray
}

export const getRange = _getRange