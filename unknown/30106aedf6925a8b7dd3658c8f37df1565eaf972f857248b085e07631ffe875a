import { HttpContextContract } from '@ioc:Adonis/Core/HttpContext'
import dynPluginInterface from 'App/Interfaces/plugins/dynPluginInterface'

import Database from '@ioc:Adonis/Lucid/Database'
import Order from 'App/Models/Order'

const AlCB = async (httpCtx: HttpContextContract) => {
  const { id } = httpCtx.request.qs()

  const order = await Order.findOrFail(id)

  return await order.getPrice()
}

const plugin: dynPluginInterface = {
  httpmethods: ['GET'],
  cb: async (httpCtx: HttpContextContract) => {
    return await AlCB(httpCtx)
  },
  route: '/orderprice/'
}

export default plugin
