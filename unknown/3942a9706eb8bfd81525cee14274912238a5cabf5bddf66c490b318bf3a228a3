import { isSize } from 'App/Helpers/isSize'

export const transliterate = (text: string) => {
  if (!text || typeof text !== 'string') {
    return false
  }

  const translitMap = {
    'А': 'A',
    'В': 'B',
    'Е': 'E',
    'К': 'K',
    'М': 'M',
    'Н': 'H',
    'О': 'O',
    'Р': 'P',
    'С': 'C',
    'Т': 'T',
    'У': 'Y',
    'Х': 'X',
    'а': 'a',
    'е': 'e',
    'о': 'o',
    'р': 'p',
    'с': 'c',
    'х': 'x'
  }

  // Проходим по каждому символу строки и заменяем, если символ найден в словаре
  return String(text)
    .split('')
    .map((char) => translitMap[char] || char)
    .join('')
}

// const text = 'АКК49058'
// console.log(transliterate(text))

function _searchValueHandler({ value, productDBfields, sizeField }, filters) {
  let sizeIn: number = 0.0
  let sizeOut: number = 0.0
  let sizeHeight: number = 0.0
  let sizeTollerance: number = 0.3

  let sizeIn_2: number | undefined = undefined,
    sizeOut_2: number | undefined = undefined,
    sizeHeight_2: number | undefined = undefined

  const sizeregexp = /(\*|x|х|X|Х|\s|на|НА|-|:)/gm
  const cutwhitespace = /\s*/gm
  const dotregexp = /\,{1,}/gm

  value = String(value).replace(dotregexp, '.')

  let globalSizeTolerance: number = 0

  let searchBySize: boolean = false

  let sizeMatches = value.split(' ').filter((i) => isSize(i))[0]
  // console.log('🚀 ~ _searchValueHandler ~ sizeMatches:', sizeMatches)

  let _value = value
    .split(' ')
    .filter((i) => !isSize(i))
    .join(' ')

  // _value = transliterate(_value)

  value = value.split(' ').filter((i) => !isSize(i))[0] || ''

  if (sizeMatches) {
    sizeMatches = String(sizeMatches).replace(sizeregexp, '*')
    // console.log('🚀 ~ _searchValueHandler ~ sizeMatches:', sizeMatches)

    value = value.replace(sizeregexp, '*')

    let sizes: string[] = sizeMatches.split('*')
    sizes = sizes.filter((i) => i)
    // console.log('🚀 ~ _searchValueHandler ~ sizes:', sizes)

    try {
      sizeIn = Number(String(sizes[0]).split('/')[0])
      sizeIn_2 = Number(String(sizes[0]).split('/')[1]) || undefined

      sizeOut = Number(String(sizes[1]).split('/')[0])
      sizeOut_2 = Number(String(sizes[1]).split('/')[1]) || undefined
    } catch (error) {
      console.error('error parse size: ', error)
      sizeIn = Number(sizes[0])
      sizeOut = Number(sizes[1])
    }

    searchBySize = true
    productDBfields.splice(productDBfields.indexOf(sizeField), 1)

    if (sizes[2]) {
      try {
        sizeHeight = Number(String(sizes[2]).split('/')[0])
        sizeHeight_2 = Number(String(sizes[2]).split('/')[1]) || undefined
      } catch (error) {
        console.error('error parse height: ', error)
        sizeHeight = Number(sizes[2])
      }
    }

    value = value.replace(cutwhitespace, '')
  }

  if (filters?.size?.tollerance) {
    globalSizeTolerance = filters.size.tollerance
  }

  //   if (filters['size'] && (Object.keys(filters['size']).length < 2 || !filters['size'].in)) {
  //     delete filters['size']
  //   }

  if ((filters?.size?.in || filters?.size?.out) && !searchBySize) {
    searchBySize = true

    sizeIn = filters['size'].in
    sizeIn_2 = filters['size'].in_2

    sizeOut = filters['size'].out
    sizeOut_2 = filters['size'].out_2

    sizeHeight = filters['size'].h
    sizeHeight_2 = filters['size'].h_2

    sizeTollerance = filters['size'].tollerance

    if (sizeTollerance > 1) sizeTollerance = 1

    // delete filters['size']
  }

  delete filters?.['size']

  return {
    searchBySize,
    sizeIn,
    sizeIn_2,
    sizeOut,
    sizeOut_2,
    sizeHeight,
    sizeHeight_2,
    sizeTollerance: globalSizeTolerance || sizeTollerance,
    _value
  }
}

export const searchValueHandler = _searchValueHandler
