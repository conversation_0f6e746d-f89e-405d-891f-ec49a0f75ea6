import Product                from 'App/Models/Product'
import Category               from 'App/Models/Category'
import Database               from '@ioc:Adonis/Lucid/Database'
import { BaseModel, column }  from '@ioc:Adonis/Lucid/Orm'

interface Stngs {
  fields?: string[],
  category: string,
  searchvalue?: string,
}

const idField = 'category_id'
const defId = 9999

const DEFAULT_FIELDS = ['prod_manuf', 'prod_model', 'prod_type', 'prod_material']


export default class Filter extends BaseModel {
  @column({ isPrimary: true })
  public id: number

  @column()
  public category_id: number

  @column()
  public field: string

  @column()
  public title: string

  static async getPreData(categoryid, limit = 50) {

    let _filter = new Filter()
    let filters

    let _mc = categoryid.split(',').length > 1

    if (_mc) {
      filters = await _filter.getListByIds(categoryid.split(','), true)
    } else {
      filters = await _filter.getList(categoryid)
    }

    let fields = filters.map(i => i.field).filter(i => Product.$hasColumn(i))

    fields = [...new Set(fields)]
    let data = {}

    await Promise.all(fields.map(
      async (col) => {
        const [res] =  await Database.rawQuery(
          `SELECT DISTINCT ?? as value, count(??) as cnt
          FROM products WHERE prod_cat ${_mc ? 'IN' : '='} (?)
          GROUP BY (??) 
          ORDER BY cnt desc, ?? 
          ASC LIMIT ?`, [col, col, _mc ? categoryid.split(',') : categoryid, col, col, Number(limit)]
        )

        data[col] = res.map(i => i.value)
      }
    ))

    return data
    
  }

  async getList(id) {
    let filter:any = await Filter.query().where(idField, id)

    if (!filter.length) {
      filter = await Filter.query().where(idField, defId)
    }

    return filter
  }

  async getListByIds(ids: number[], full = false) {
    let filters: any[] = await Filter.query().whereIn(idField, ids)

    return full ? filters : filters.filter((v, i, a) => a.findIndex(t => (t.field === v.field)) === i)
  }

  async getData(request?, stngs?: Stngs) {

    const categoryField = 'prod_cat'
    const filterReg = /\d{4}-\d{4}/gm
    
    let filtersFields: string[]
    let categoryId: string 
    let morecategories: string[]
    let searchvalue: string
    let querycolumn: string = ''

    if (stngs) {
        filtersFields   = stngs.fields || DEFAULT_FIELDS
        categoryId      = stngs.category

        morecategories  = []

        searchvalue     = stngs.searchvalue || ''
    } else {
        filtersFields  = request.requestBody.fields || DEFAULT_FIELDS
        categoryId     = request.requestBody.category || ''
        
        morecategories = []
        
        searchvalue    = request.requestBody.searchvalue || ''
        querycolumn    = request.requestBody.column || ''
    }

    //searchvalue = searchvalue.replace(/\s*/gm, '')
    searchvalue = searchvalue.trim()
    querycolumn = querycolumn.replace(/\s*/gm, '')

    try {
      morecategories = JSON.parse(categoryId)
    } catch (error) { }

    const getDistinct = async (column: string) => {
      let filterdata = await Database.query().distinct(column).from('products').where((builder) => {
        if (categoryId) {
          if (morecategories.length > 0) builder.whereIn(categoryField, morecategories)
          else builder.where(categoryField, categoryId)
        }
        if (searchvalue) builder.where(column, 'like', `%${searchvalue}%`)
      })
      return filterdata
    }

    let data: any

    if (querycolumn) {
      data = await getDistinct(querycolumn)
    } else {
      data = await Promise.all(filtersFields.map(getDistinct))
    }

    data = data.flat()

    let results: string[] = data.reduce((accumulator, currentValue) => {
      let currentItem = currentValue[Object.keys(currentValue)[0]]
      let currentKey = Object.keys(currentValue)[0]

      if (currentItem) {
        if (!accumulator[currentKey]) accumulator[currentKey] = [currentItem]
        else accumulator[currentKey].push(currentItem)
      }

      return accumulator
    }, {})

    if (results['prod_type']) {
      results['prod_type'] = results['prod_type'].filter(x => x.search(filterReg) == -1)
    }

    return querycolumn ? results[querycolumn] : results
  }

}
