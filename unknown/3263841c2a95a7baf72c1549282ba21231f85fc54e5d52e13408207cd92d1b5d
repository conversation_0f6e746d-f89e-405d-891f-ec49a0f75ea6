import Product from 'App/Models/Product'
const XLSX = require('xlsx')

export class PriceList {

    catalog: Array<Product> = []

    constructor() {

    }

    async loadProducts(limit?) {
        this.catalog = await Product
                                    .query()
                                    .select('*')
                                    //.preload('category')
                                    .where(query => {
                                        query.where('prod_count', '>', 0)
                                        query.orWhere('prod_group_count', '>', 0)
                                    })
                                    .andWhere('prod_price', '>', 0)
                                    .if(limit, query => query.limit(limit))
    }

    async make(limit?) {

        await this.loadProducts(limit)

        let pricelist = this.catalog.map((item: Product) => {

            return {
                'Артикул': item.prod_sku,
                'Код': item.prod_analogsku,
                'Цена': item.prod_price,
                'Цена Опт': item.whosaleprice,
                'Наличие': item.prod_count, //Number(item.prod_count) > 100 ? '>100' : item.prod_count,
                'Бренд': item.prod_manuf,
                'Тип': item.prod_type,
                'Размер': item.prod_size,
                'Материал': item.prod_material,
                'Назначение': item.prod_purpose,
                // 'Категория': item.category.cat_title,
            }
        })

        let wb = XLSX.utils.book_new()
        let ws = XLSX.utils.json_to_sheet(pricelist)
        XLSX.utils.book_append_sheet(wb, ws, 'pricelist')

        return XLSX.write(wb, { bookType: "xlsx", type: 'base64' })
        //XLSX.writeFile(wb, 'crosses3.xlsx')
    }

}