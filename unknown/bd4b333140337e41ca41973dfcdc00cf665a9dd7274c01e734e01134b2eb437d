import Database from '@ioc:Adonis/Lucid/Database'

export class AppSetting {
  constructor() {}

  async getJSON({ keys = [], all = false }) {
    let settings = {}
    let defaultParams = ['currency', 'stripekeyclient', 'paypalkeyclient', 'DISCOUNT_START_SUM', 'google_analytics_key', 'rumisota.retail', 'google_analytics_key_rti']

    const res = await Database.from('settings')
      .select('s_value as value', 's_key as key', 'json')
      .if(!all, (builder) => {
        builder.where((query) => {
          if (keys?.length) keys.map((key) => query.orWhere('s_key', key))
          else defaultParams.map((key) => query.orWhere('s_key', key))
        })
      })
      
    res.map((param) => {
      if (param.json) {
        try {
          param.value = JSON.parse(param.value)
          settings[param.key] = param.value
        } catch (error) {
          console.log('Error parse param: ' + param.key + ', AppSetting.getJSON: ' + error)
        }
      } else {
        if (!isNaN(Number(param.value))) param.value = Number(param.value)
        settings[param.key] = param.value
      }
    })

    return settings
  }
}
