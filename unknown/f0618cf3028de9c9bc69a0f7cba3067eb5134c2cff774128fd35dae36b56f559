import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import { SMSruClass } from "App/Helpers/SMSru"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"


const SMSru = new SMSruClass()

const AlCB = async ({ request, params, response, auth, session }: HttpContextContract) => {
    const ip = request.ip()    
    return await SMSru.balance(ip)
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/sms/balance'
}

export default plugin