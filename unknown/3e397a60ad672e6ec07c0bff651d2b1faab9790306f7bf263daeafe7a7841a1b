import { DateTime } from 'luxon'
import { BaseModel, column } from '@ioc:Adonis/Lucid/Orm'

export default class SmsAuth extends BaseModel {
  public static table = 'sms_auth'

  @column({ isPrimary: true })
  public id: number

  // @column.dateTime({ autoCreate: true })
  // public createdAt: DateTime

  // @column.dateTime({ autoCreate: true, autoUpdate: true })
  // public updatedAt: DateTime

  @column()
  auth_code: number

  @column.dateTime({ autoCreate: true })
  auth_time: DateTime

  @column()
  auth_login?: string
}
