const _parseQS = (pvalue) => {
    if (pvalue) {
        try {
            let fromBase64 = Buffer.from(pvalue, 'base64').toString()
            //console.log('fromBase64: ', fromBase64)
            return JSON.parse('{"' + decodeURI(fromBase64).replace(/"/g, '\\"').replace(/&/g, '","').replace(/=/g, '":"') + '"}') //Object.fromEntries(new URLSearchParams(fromBase64))
        } catch (error) {
            console.warn('parseQS.ts > error parsed query from base64: ', error)
            return pvalue
        }
    } else {
        return pvalue
    }
}

export const parseQS = _parseQS