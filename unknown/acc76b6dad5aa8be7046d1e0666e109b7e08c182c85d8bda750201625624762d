import Database from '@ioc:Adonis/Lucid/Database'
import Product from 'App/Models/Product'
import { isSize } from 'App/Helpers/isSize'

export const splashSplitter = (str) => {
  str = String(str)

  if (str && str.split('/').length > 1) {
    return str.split('/')
  } else {
    return [str, null]
  }
}

export const splitProductSize = async (product: Product | any, { debug = false } = {}) => {
  if (!isSize(product.prod_size)) {
    return false
  }

  let [size_in, size_out, size_h] = String(product.prod_size).split('*')

  let size_in_2, size_out_2, size_h_2, sp_res

  sp_res = splashSplitter(size_in)

  size_in = sp_res[0] || 0
  size_in_2 = sp_res[1] || 0

  sp_res = splashSplitter(size_out)

  size_out = sp_res[0] || 0
  size_out_2 = sp_res[1] || 0

  if (size_h) {
    sp_res = splashSplitter(size_h)

    if (sp_res) {
      size_h = sp_res[0] || 0
      size_h_2 = sp_res[1] || 0
    }
  } else {
    size_h = 0
    size_h_2 = 0
  }

  const res = await Database.from('products')
    .where('prod_id', product.prod_id)
    .update({
      size_in,
      size_in_2,

      size_out,
      size_out_2,

      size_h,
      size_h_2
    })
    .debug(false)
}

export const fillSizes = async (limit = 100) => {
  try {
    const products = await Database.from('products')
      .where((query) => {
        query.whereNull('size_in')
        query.orWhereNull('size_out')
        query.orWhere('size_in', 0)
        query.orWhere('size_out', 0)
      })
      .andWhereRaw('LENGTH(prod_size) > 3')
      //   .andWhereNotIn('prod_cat',[5, 2,3,16,17,18,20,22,23])
      .andWhere('prod_cat', 4)
      .orderBy('size_in', 'asc')
      .limit(limit)

    await Promise.all(
      products.map(async (product) => {
        if (product && product.prod_size) {
          await splitProductSize(product)
        }
      })
    )

    return products
    console.log('fillSizes done')
  } catch (error) {
    console.error(error)
  }
}
