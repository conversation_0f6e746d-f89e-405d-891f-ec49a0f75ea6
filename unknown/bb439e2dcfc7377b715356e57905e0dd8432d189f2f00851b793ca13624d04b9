import { HttpContextContract } from "@ioc:Adonis/Core/HttpContext"
import dynPluginInterface from "App/Interfaces/plugins/dynPluginInterface"
import { AliexpressOrders, aliOrderPrefix, AliOrders, getOrdersPayload } from "App/Plugins/AliexpressOrders"


// TODO: move create order method to Order model
import OrdersController from 'App/Controllers/Http/OrdersController'
import { rtiOrder } from "App/Interfaces/orders/RtiOrder"
import Database from "@ioc:Adonis/Lucid/Database"
const ordersController = new OrdersController()

const AlCB = async (httpCtx: HttpContextContract) => {
   
    
    const AI = new AliexpressOrders()
    await AI.init()

    const payload: getOrdersPayload = {
        page: 1,
        page_size: 50,
        delivery_statuses: ['Init'],
    }

    const data: AliOrders = await AI.getOrders({toRTI: false, payload})
    const orderIds = data.orders.map(order => order.id)

    for (const orderId of orderIds) {
        const rtiOrder = await Database.from('orders')
                                                .select('order_tracknumber')
                                                .where('order_desc', 'like', `${aliOrderPrefix}${orderId}%`)
                                                .andWhere('order_tracknumber', '!=', '')
                                                .first()
        
        if (rtiOrder) {
            console.log('current rti order: ', {rtiOrder, orderId});
            
            const res = await AI.sendTrack({orderId, tracking_number: rtiOrder.order_tracknumber})
            console.log('send track res: ', res)
        } else {
            console.error('AliSendTracks error: rti order not found:', orderId)
        }
    }

   
}

const plugin: dynPluginInterface = {
    httpmethods: ['GET'],
    cb: async (httpCtx: HttpContextContract) => {
        return await AlCB(httpCtx)
    },
    route: '/aliexpress/sendtracks'
}

export default plugin