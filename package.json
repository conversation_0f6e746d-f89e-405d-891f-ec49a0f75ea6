{"name": "rti-api_upd", "version": "1.0.0", "private": true, "scripts": {"build": "node ace build --production", "start": "node server.js", "dev": "node ace serve --watch"}, "devDependencies": {"@adonisjs/assembler": "^3.0.6", "@types/uuid": "^10.0.0", "adonis-preset-ts": "^1.1.0", "pino-pretty": "^4.3.0", "prisma": "^6.4.1", "typescript": "^5.6.2", "youch": "^2.1.1", "youch-terminal": "^1.0.1"}, "dependencies": {"@adonisjs/ally": "^4.1.1", "@adonisjs/auth": "8.0.9", "@adonisjs/core": "^5.3.2", "@adonisjs/lucid": "16.3.2", "@adonisjs/mail": "^7.2.1", "@adonisjs/repl": "^3.1.6", "@adonisjs/session": "^6.0.6", "@adonisjs/shield": "^7.0.4", "@adonisjs/view": "^6.1.0", "@cull/imap": "^0.3.1", "@pdf-lib/fontkit": "^1.1.1", "@prisma/client": "^6.4.1", "@trpc/client": "^11.2.0", "@trpc/server": "^11.2.0", "@types/html-minifier": "^4.0.2", "@types/imagemagick": "^0.0.35", "@types/imap-simple": "^4.2.6", "@types/imapflow": "^1.0.13", "@types/mailparser": "^3.4.0", "@types/socket.io": "^3.0.2", "axios": "^0.26.1", "crypto-js": "^4.0.0", "cyrillic-to-translit": "^1.0.1", "dayjs": "^1.11.10", "docxtemplater": "^3.37.7", "easy-template-x": "^2.1.0", "exceljs": "^4.3.0", "got": "^11.8.1", "grammy": "^1.17.2", "html-minifier": "^4.0.0", "i": "^0.3.7", "i18next": "^20.3.2", "imagemagick": "^0.1.3", "imagemagick-dynamic-watermark": "^3.1.0", "imap": "^0.8.19", "imap-simple": "^5.1.0", "imapflow": "^1.0.119", "isbot": "^3.0.23", "luxon": "^2.0.2", "mailparser": "^3.6.5", "meilisearch": "^0.51.0", "mysql": "^2.18.1", "node-fetch": "^3.2.2", "npm": "^10.5.0", "pdf-lib": "^1.17.1", "pdf.js-extract": "^0.2.1", "phc-argon2": "^1.0.11", "phc-bcrypt": "^1.0.4", "pizzip": "^3.1.4", "prisma-extension-pagination": "^0.7.5", "proxy-addr": "^2.0.6", "reflect-metadata": "^0.1.13", "sanitize-html": "^2.14.0", "socket.io": "^4.4.0", "stripe": "^8.149.0", "toad-scheduler": "^3.0.0", "uuid": "^11.1.0", "videoshow": "^0.1.12", "xlsx": "^0.16.9", "xlsx-template": "^1.3.0", "xml-js": "^1.6.11", "zadarma": "^1.1.2", "zod": "^3.22.4"}}