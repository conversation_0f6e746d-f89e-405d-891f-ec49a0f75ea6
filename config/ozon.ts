/**
 * Конфигурация для интеграции с Ozon Seller API
 * 
 * Этот файл содержит настройки по умолчанию и документацию
 * для всех параметров интеграции с Ozon.
 */

import Env from '@ioc:Adonis/Core/Env'

export interface OzonConfig {
  // Основные настройки API
  api: {
    baseUrl: string
    timeout: number
    retryAttempts: number
    retryDelay: number
  }

  // Rate limiting
  rateLimit: {
    maxRequests: number
    timeWindow: number
  }

  // Настройки по умолчанию
  defaults: {
    warehouseId: number
    categoryId: number
    syncEnabled: boolean
    autoSyncInterval: number
    imageUploadEnabled: boolean
    maxImagesPerProduct: number
    additionalPrice: number
    vat: string
    currencyCode: string
  }

  // Настройки синхронизации
  sync: {
    batchSize: number
    maxConcurrentRequests: number
    pauseBetweenBatches: number
  }

  // Настройки кэширования
  cache: {
    settingsTtl: number
    productInfoTtl: number
  }
}

const ozonConfig: OzonConfig = {
  // Основные настройки API
  api: {
    baseUrl: Env.get('OZON_API_URL', 'https://api-seller.ozon.ru'),
    timeout: 30000, // 30 секунд
    retryAttempts: 3,
    retryDelay: 5000 // 5 секунд
  },

  // Rate limiting (согласно документации Ozon)
  rateLimit: {
    maxRequests: 100, // максимум запросов
    timeWindow: 60000 // за 60 секунд
  },

  // Настройки по умолчанию
  defaults: {
    warehouseId: 0,
    categoryId: 17029016, // Категория по умолчанию
    syncEnabled: false,
    autoSyncInterval: 60, // минуты
    imageUploadEnabled: false,
    maxImagesPerProduct: 10,
    additionalPrice: 500, // наценка в рублях
    vat: '0', // НДС 0%
    currencyCode: 'RUB'
  },

  // Настройки синхронизации
  sync: {
    batchSize: 10, // товаров в одном батче
    maxConcurrentRequests: 3, // одновременных запросов
    pauseBetweenBatches: 2000 // пауза между батчами в мс
  },

  // Настройки кэширования
  cache: {
    settingsTtl: 60000, // 1 минута
    productInfoTtl: 300000 // 5 минут
  }
}

export default ozonConfig

/**
 * Список всех настроек Ozon, которые хранятся в БД
 */
export const OZON_SETTINGS_KEYS = [
  'ozon_api_key',
  'ozon_client_id',
  'ozon_api_url',
  'ozon_warehouse_id',
  'ozon_sync_enabled',
  'ozon_auto_sync_interval',
  'ozon_default_category_id',
  'ozon_image_upload_enabled',
  'ozon_image_base_url',
  'ozon_max_images_per_product'
] as const

/**
 * Типы для настроек Ozon
 */
export type OzonSettingsKey = typeof OZON_SETTINGS_KEYS[number]

/**
 * Интерфейс для настроек Ozon из БД
 */
export interface OzonSettings {
  ozon_api_key: string
  ozon_client_id: string
  ozon_api_url: string
  ozon_warehouse_id: number
  ozon_sync_enabled: boolean
  ozon_auto_sync_interval: number
  ozon_default_category_id: number
  ozon_image_upload_enabled: boolean
  ozon_image_base_url: string
  ozon_max_images_per_product: number
}

/**
 * Валидация настроек Ozon
 */
export function validateOzonSettings(settings: Partial<OzonSettings>): {
  isValid: boolean
  errors: string[]
} {
  const errors: string[] = []

  // Обязательные настройки
  if (!settings.ozon_api_key || settings.ozon_api_key.length < 10) {
    errors.push('API ключ должен содержать минимум 10 символов')
  }

  if (!settings.ozon_client_id || settings.ozon_client_id.length < 5) {
    errors.push('Client ID должен содержать минимум 5 символов')
  }

  if (!settings.ozon_warehouse_id || settings.ozon_warehouse_id <= 0) {
    errors.push('ID склада должен быть положительным числом')
  }

  // Дополнительные проверки
  if (settings.ozon_auto_sync_interval && settings.ozon_auto_sync_interval < 1) {
    errors.push('Интервал автосинхронизации должен быть минимум 1 минута')
  }

  if (settings.ozon_max_images_per_product && settings.ozon_max_images_per_product > 15) {
    errors.push('Максимум изображений на товар не может превышать 15')
  }

  if (settings.ozon_default_category_id && settings.ozon_default_category_id <= 0) {
    errors.push('ID категории по умолчанию должен быть положительным числом')
  }

  return {
    isValid: errors.length === 0,
    errors
  }
}

/**
 * Получение настроек по умолчанию
 */
export function getDefaultOzonSettings(): Partial<OzonSettings> {
  return {
    ozon_api_url: ozonConfig.api.baseUrl,
    ozon_warehouse_id: ozonConfig.defaults.warehouseId,
    ozon_sync_enabled: ozonConfig.defaults.syncEnabled,
    ozon_auto_sync_interval: ozonConfig.defaults.autoSyncInterval,
    ozon_default_category_id: ozonConfig.defaults.categoryId,
    ozon_image_upload_enabled: ozonConfig.defaults.imageUploadEnabled,
    ozon_image_base_url: '',
    ozon_max_images_per_product: ozonConfig.defaults.maxImagesPerProduct
  }
}

/**
 * Проверка обязательных настроек
 */
export function checkRequiredSettings(settings: Partial<OzonSettings>): {
  isComplete: boolean
  missing: string[]
} {
  const required: (keyof OzonSettings)[] = [
    'ozon_api_key',
    'ozon_client_id',
    'ozon_warehouse_id'
  ]

  const missing = required.filter(key => 
    !settings[key] || 
    (typeof settings[key] === 'string' && settings[key] === '') ||
    (typeof settings[key] === 'number' && settings[key] <= 0)
  )

  return {
    isComplete: missing.length === 0,
    missing
  }
}

/**
 * Маппинг ошибок API Ozon
 */
export const OZON_ERROR_CODES = {
  // Ошибки аутентификации
  'INVALID_API_KEY': 'Неверный API ключ',
  'INVALID_CLIENT_ID': 'Неверный Client ID',
  'ACCESS_DENIED': 'Доступ запрещен',
  
  // Ошибки товаров
  'PRODUCT_NOT_FOUND': 'Товар не найден',
  'INVALID_OFFER_ID': 'Неверный артикул товара',
  'CATEGORY_NOT_FOUND': 'Категория не найдена',
  'INVALID_PRICE': 'Неверная цена',
  'INVALID_STOCK': 'Неверное количество остатков',
  
  // Ошибки лимитов
  'RATE_LIMIT_EXCEEDED': 'Превышен лимит запросов',
  'QUOTA_EXCEEDED': 'Превышена квота',
  
  // Системные ошибки
  'INTERNAL_ERROR': 'Внутренняя ошибка сервера',
  'SERVICE_UNAVAILABLE': 'Сервис недоступен',
  'TIMEOUT': 'Превышено время ожидания'
} as const

/**
 * Получение человекочитаемого описания ошибки
 */
export function getErrorDescription(errorCode: string): string {
  return OZON_ERROR_CODES[errorCode] || `Неизвестная ошибка: ${errorCode}`
}

/**
 * Статусы товаров в Ozon
 */
export const OZON_PRODUCT_STATES = {
  'imported': 'Импортирован',
  'processed': 'Обработан',
  'moderating': 'На модерации',
  'declined': 'Отклонен',
  'ready_to_supply': 'Готов к поставке',
  'validation_state_unknown': 'Статус неизвестен',
  'archived': 'В архиве',
  'failed_moderation': 'Не прошел модерацию',
  'failed_validation': 'Не прошел валидацию',
  'state_failed': 'Ошибка обработки'
} as const

/**
 * Получение описания статуса товара
 */
export function getProductStateDescription(state: string): string {
  return OZON_PRODUCT_STATES[state] || `Неизвестный статус: ${state}`
}
