# Миграционный гайд: Переход на новый OzonService

## 🎯 Обзор изменений

После рефакторинга интеграция с Ozon стала значительно проще. Все компоненты объединены в один сервис, что упрощает использование и поддержку.

## 📋 Что изменилось

### Старая архитектура (ДО рефакторинга)
```typescript
// Множество импортов
import { OzonClient } from 'App/Clients/OzonClient'
import { ozonConfig } from 'App/Services/OzonConfig'
import { ozonProvider } from 'App/Providers/OzonProvider'
import { OzonService } from 'App/Services/OzonService'

// Сложная инициализация
const client = new OzonClient()
const service = new OzonService()
await client.initialize()
```

### Новая архитектура (ПОСЛЕ рефакторинга)
```typescript
// Один импорт
import { ozonService } from 'App/Services/OzonService'

// Готов к использованию
const settings = await ozonService.getSettings()
```

## 🔄 Миграция кода

### 1. Обновление импортов

**Было:**
```typescript
import { OzonClient } from 'App/Clients/OzonClient'
import { ozonConfig } from 'App/Services/OzonConfig'
import { ozonProvider } from 'App/Providers/OzonProvider'
import { OzonService } from 'App/Services/OzonService'
```

**Стало:**
```typescript
import { ozonService } from 'App/Services/OzonService'
```

### 2. Работа с настройками

**Было:**
```typescript
// Получение настроек
const settings = await ozonConfig.getSettings()

// Обновление настроек
await ozonConfig.updateSettings({ ozon_sync_enabled: true })

// Проверка включена ли синхронизация
const isEnabled = await ozonConfig.isEnabled()
```

**Стало:**
```typescript
// Получение настроек
const settings = await ozonService.getSettings()

// Обновление настроек
await ozonService.updateSettings({ ozon_sync_enabled: true })

// Проверка включена ли синхронизация
const isEnabled = await ozonService.isEnabled()
```

### 3. Синхронизация товаров

**Было:**
```typescript
// Через провайдер
const result = await ozonProvider.syncProducts([1, 2, 3])

// Или через сервис
const service = new OzonService()
const result = await service.syncProducts([1, 2, 3])
```

**Стало:**
```typescript
// Единый способ
const result = await ozonService.syncProducts([1, 2, 3])
```

### 4. Обновление остатков и цен

**Было:**
```typescript
// Остатки
await ozonProvider.updateStocks([{
  offer_id: 'SKU123',
  stock: 50,
  warehouse_id: 12345
}])

// Цены
await ozonProvider.updatePrices([{
  offer_id: 'SKU123',
  price: '1500',
  currency_code: 'RUB'
}])
```

**Стало:**
```typescript
// Остатки (упрощенный API)
await ozonService.updateProductStock(productId, 50)

// Цены (упрощенный API)
await ozonService.updateProductPrice(productId, 1500, 2000)
```

### 5. Проверка подключения

**Было:**
```typescript
const client = new OzonClient()
const isHealthy = await client.healthCheck()
```

**Стало:**
```typescript
const test = await ozonService.testConnection()
const isHealthy = test.success
```

### 6. Получение информации о товаре

**Было:**
```typescript
const info = await ozonProvider.getProductInfo('SKU123')
```

**Стало:**
```typescript
const info = await ozonService.getProductInfo('SKU123')
```

## 🔧 Обновление существующего кода

### Пример 1: Контроллер синхронизации

**Было:**
```typescript
import { OzonService } from 'App/Services/OzonService'
import { ozonConfig } from 'App/Services/OzonConfig'

export default class OzonController {
  private ozonService: OzonService

  constructor() {
    this.ozonService = new OzonService()
  }

  async sync({ request, response }) {
    const isEnabled = await ozonConfig.isEnabled()
    if (!isEnabled) {
      return response.badRequest('Ozon sync is disabled')
    }

    const { productIds } = request.body()
    const result = await this.ozonService.syncProducts(productIds)
    
    return response.json(result)
  }
}
```

**Стало:**
```typescript
import { ozonService } from 'App/Services/OzonService'

export default class OzonController {
  async sync({ request, response }) {
    const isEnabled = await ozonService.isEnabled()
    if (!isEnabled) {
      return response.badRequest('Ozon sync is disabled')
    }

    const { productIds } = request.body()
    const result = await ozonService.syncProducts(productIds)
    
    return response.json(result)
  }
}
```

### Пример 2: Фоновая задача

**Было:**
```typescript
import { ozonProvider } from 'App/Providers/OzonProvider'
import { ozonConfig } from 'App/Services/OzonConfig'

export default class SyncProductsJob {
  async handle() {
    const settings = await ozonConfig.getSettings()
    if (!settings.ozon_sync_enabled) return

    const products = await this.getProductsToSync()
    
    for (const product of products) {
      await ozonProvider.syncProduct(product.id)
    }
  }
}
```

**Стало:**
```typescript
import { ozonService } from 'App/Services/OzonService'

export default class SyncProductsJob {
  async handle() {
    const isEnabled = await ozonService.isEnabled()
    if (!isEnabled) return

    const products = await this.getProductsToSync()
    const productIds = products.map(p => p.id)
    
    await ozonService.syncProducts(productIds)
  }
}
```

## ⚠️ Важные изменения

### 1. Удаленные классы и методы
Следующие классы больше не доступны:
- `OzonClient` - функциональность встроена в `OzonService`
- `OzonConfig` - методы перенесены в `OzonService`
- `OzonProvider` - методы перенесены в `OzonService`
- `OzonValidator` - валидация упрощена
- `OzonLogger` - логирование упрощено

### 2. Изменения в API методах

**Упрощенные методы:**
- `updateProductStock(productId, stock)` вместо `updateStocks([...])`
- `updateProductPrice(productId, price, oldPrice?)` вместо `updatePrices([...])`
- `getProductStatus(productId)` вместо отдельных вызовов

**Новые методы:**
- `testConnection()` - проверка подключения к API
- `getSyncStats()` - статистика синхронизации
- `getOzonSettingsForRouter()` - настройки для tRPC

### 3. Изменения в настройках
Все настройки остались прежними, изменился только способ доступа к ним.

## 🧪 Тестирование после миграции

### 1. Проверка компиляции
```bash
npm run build
```

### 2. Проверка основных функций
```typescript
import { ozonService } from 'App/Services/OzonService'

// Тест настроек
const settings = await ozonService.getSettings()
console.log('Settings loaded:', !!settings)

// Тест подключения
const test = await ozonService.testConnection()
console.log('Connection test:', test.success)
```

### 3. Проверка tRPC роутера
```typescript
// Все существующие tRPC методы должны работать без изменений
await trpc.ozon.getOzonSettings.query()
await trpc.ozon.testConnection.query()
```

## 🚀 Преимущества новой архитектуры

1. **Простота использования** - один импорт вместо нескольких
2. **Лучшая производительность** - меньше накладных расходов
3. **Упрощенное тестирование** - один класс для тестирования
4. **Легче поддержка** - вся логика в одном месте
5. **Меньше ошибок** - меньше точек отказа

## 📞 Поддержка

Если у вас возникли проблемы с миграцией:

1. Проверьте, что все импорты обновлены
2. Убедитесь, что используете новые методы API
3. Проверьте логи на наличие ошибок
4. Протестируйте основные функции

Все tRPC методы остались без изменений, поэтому фронтенд код обновлять не нужно.
