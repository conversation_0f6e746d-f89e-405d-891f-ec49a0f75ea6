  async federated_globalSearchMeili(
    { searchvalue = '', filters, limit = 100, page = 1, sorting = [], groupByRoot = true }: GlobalSearchParams & { groupByRoot?: boolean },
    request: HttpContextContract['request']
  ) {
    searchvalue = decodeURIComponent(searchvalue)

    if (request) {
      Statistic.write({
        request,
        query: searchvalue,
        count: 1,
        manual: 0
      })
    }

    const timestamp = Date.now()
    //console.time(`globalSearchMeili: ${searchvalue} | ${timestamp}`)

    // Используем объединённый метод для обработки фильтров
    const { filter } = this.buildMeiliFilter(filters)
    const { filter: forSizeExactFilter } = this.buildMeiliFilter(filters)

    // Используем метод для построения условий сортировки
    const sort = this.buildMeiliSortConditions(sorting)

    // Добавляем условия для размеров, если они присутствуют
    const sizeData = this.extractSizeData(searchvalue)
    if (sizeData) {
      filter.push(...this.getMeiliSizeFilters(sizeData))
      forSizeExactFilter.push(...this.getMeiliSizeFiltersExact(sizeData))
      sort.push(...this.buildMeiliSortConditions([{ column: 'prod_size', direction: 'asc' }]))
      sort.push('cat_search_sort:asc')
    } else {
      sort.unshift('cat_search_sort:asc')
    }

    // Обработка поискового запроса через вынесенный метод
    const _value = this.processSearchQuery(searchvalue)

    // Формируем итоговый массив сортировок
    const finalSort = [...sort]

    // Добавляем сортировку по наличию в конец
    finalSort.unshift('inStock:desc')

    const productsResults = await this.meiliDB.multiSearch({
      federation: {
        offset: limit * (page - 1),
        limit
      },
      queries: [
        {
          indexUid: 'products',
          q: _value,
          offset: limit * (page - 1),
          limit,
          filter: sizeData ? forSizeExactFilter : filter,
          attributesToRetrieve: defaultAttributesToRetrieve,
          attributesToCrop: ['prod_note', 'prod_uses'],
          cropLength: 100,
          sort: finalSort,
          matchingStrategy: 'frequency',
          rankingScoreThreshold: 0.2
        },
        {
          indexUid: 'products',
          q: _value,
          offset: limit * (page - 1),
          limit,
          filter,
          attributesToRetrieve: defaultAttributesToRetrieve,
          attributesToCrop: ['prod_note', 'prod_uses'],
          cropLength: 100,
          sort: finalSort,
          matchingStrategy: 'frequency'
          // rankingScoreThreshold: 0.2
        }
      ]
    })

    // const [productsResults] = await Promise.all([
    //   this.meiliDB.index('products').search(_value, {
    //     offset: limit * (page - 1),
    //     limit,
    //     filter,
    //     attributesToRetrieve: defaultAttributesToRetrieve,
    //     attributesToCrop: ['prod_note', 'prod_uses'],
    //     cropLength: 100,
    //     sort: finalSort,
    //     matchingStrategy: 'frequency'
    //     // rankingScoreThreshold: 0.2
    //   })
    // ])

    // Сначала заменяем аналоги, сохраняя исходный порядок
    const productsWithReplacedAnalogs = this.replaceAnalogSkuWithFirstAnalog(productsResults.hits, _value)

    // Используем группировку по корневым категориям, сохраняя исходный порядок товаров
    // Получаем не только сгруппированные товары, но и порядок категорий
    const { productsByCategory, categoryOrder } = await this.groupProductsByRootCategory(productsWithReplacedAnalogs, groupByRoot)

    const categoriesData = await Promise.all(
      categoryOrder.map(async (rootCatId) => {
        const meiliData = await this.fetchMeiliCategoryData(rootCatId)
        const products = await this.initAndTransformToAdonisModel(productsByCategory[rootCatId], { cropFields: true })
        return {
          categoryId: rootCatId,
          categoryTitle: meiliData.category.cat_title,
          columns: meiliData.columns,
          products: products,
          filters: meiliData.filters
        }
      })
    )

    const totalCount = productsResults.totalHits || productsResults.estimatedTotalHits

    const res = {
      categories: categoriesData,
      meta: {
        totalCount: totalCount,
        lastPage: Math.ceil(totalCount / limit),
        currentPage: page,
        perPage: limit,
        isFirstPage: page,
        isLastPage: page === Math.ceil(totalCount / limit),
        previousPage: page - 1,
        nextPage: page + 1,
        pageCount: Math.ceil(totalCount / limit)
      }
    }

    //console.timeEnd(`globalSearchMeili: ${searchvalue} | ${timestamp}`)
    return res
  }