# План интеграции с Ozon Seller API

## Обзор задачи
Разработка интеграции с Ozon Seller API для загрузки и синхронизации товаров по схеме realFBS.


-- Основные настройки API
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_api_key', 'your_api_key_here', false, false),
('ozon_client_id', 'your_client_id_here', false, false),
('ozon_api_url', 'https://api-seller.ozon.ru', false, false),
('ozon_warehouse_id', '123456', false, false);

-- Настройки синхронизации
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_sync_enabled', 'true', false, false),
('ozon_auto_sync_interval', '60', false, false),
('ozon_default_category_id', '17029016', false, false);

-- Настройки изображений
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_image_upload_enabled', 'true', false, false),
('ozon_image_base_url', 'https://your-domain.com', false, false),
('ozon_max_images_per_product', '10', false, false);


## Архитектура решения

### 1. Файловая структура
```
app/
├── Clients/
│   └── OzonClient.ts              # HTTP клиент для Ozon API
├── Interfaces/
│   └── ozon/
│       ├── OzonInterfaces.ts      # Интерфейсы для API
│       └── OzonTypes.ts           # Типы данных
├── Providers/
│   └── OzonProvider.ts            # Провайдер для работы с Ozon
├── Services/
│   └── OzonService.ts             # Бизнес-логика синхронизации
└── TRPC/
    └── ozonRouter.ts              # tRPC роутер для фронтенда
```

### 2. Конфигурация через loadSettings
**Настройки в БД (таблица settings):**
- `ozon_api_key` - API ключ Ozon
- `ozon_client_id` - Client ID Ozon
- `ozon_api_url` - URL API (по умолчанию: https://api-seller.ozon.ru)
- `ozon_warehouse_id` - ID склада для realFBS
- `ozon_sync_enabled` - включена ли синхронизация (boolean)
- `ozon_auto_sync_interval` - интервал автосинхронизации (минуты)
- `ozon_default_category_id` - категория по умолчанию
- `ozon_image_upload_enabled` - включена ли загрузка изображений (boolean)
- `ozon_image_base_url` - базовый URL для доступа к изображениям
- `ozon_max_images_per_product` - максимальное количество изображений на товар

**Преимущества использования loadSettings:**
- Настройки меняются на лету без перезапуска
- Централизованное управление конфигурацией
- Возможность JSON настроек для сложных объектов
- Интеграция с существующей системой настроек

### 3. Система загрузки изображений в Ozon

**Текущая структура хранения изображений:**
```
UPLOAD_PATH/
├── rti/           # Изображения для RTI
├── rumi/          # Изображения для RUMI
├── original/      # Оригинальные изображения
└── video/         # Видео файлы
```

**Поля изображений в products:**
- `prod_img` - основное изображение RTI
- `prod_img_rumi` - основное изображение RUMI
- `prod_images` - дополнительные изображения (через запятую)

**Алгоритм загрузки изображений в Ozon:**
1. **Получение локальных изображений** - из UPLOAD_PATH/{rti|rumi}/
2. **Загрузка в Ozon** - через API метод для загрузки изображений
3. **Получение URL** - Ozon возвращает URL загруженных изображений
4. **Привязка к товару** - использование URL в данных товара

**Методы Ozon API для изображений:**
- `POST /v1/product/pictures/upload` - загрузка изображения
- `POST /v1/product/pictures/info` - информация о загруженных изображениях
- `POST /v1/product/pictures/import` - массовая загрузка изображений

## Компоненты системы

### OzonClient (app/Clients/OzonClient.ts)
**Назначение:** HTTP клиент для взаимодействия с Ozon Seller API

**Основные методы:**
- `post(endpoint, data)` - POST запрос
- `get(endpoint, params)` - GET запрос
- `handleAuth()` - добавление заголовков аутентификации
- `handleErrors()` - обработка ошибок API

**Особенности:**
- Автоматическое добавление API-ключа и Client-Id в заголовки
- Rate limiting для соблюдения лимитов API
- Retry логика при временных ошибках

### OzonProvider (app/Providers/OzonProvider.ts)
**Назначение:** Провайдер для работы с Ozon API

**Основные методы:**
- `syncProducts(productIds: number[])` - синхронизация товаров
- `createProduct(productData)` - создание товара в Ozon
- `updateProduct(productId, productData)` - обновление товара
- `updateStocks(productId, stock)` - обновление остатков
- `getProductInfo(productId)` - получение информации о товаре
- `getProductStatus(productId)` - статус товара в Ozon

### OzonService (app/Services/OzonService.ts)
**Назначение:** Бизнес-логика и маппинг данных

**Основные методы:**
- `mapProductToOzon(product)` - маппинг из БД в формат Ozon
- `mapOzonToProduct(ozonData)` - маппинг из Ozon в формат БД
- `validateProductData(data)` - валидация данных
- `logSyncOperation(productId, action, status)` - логирование
- `uploadProductImages(product)` - загрузка изображений в Ozon
- `getImageUrls(product)` - получение URL изображений для Ozon

**Маппинг полей:**
```typescript
products.prod_sku -> ozon.offer_id
products.prod_note -> ozon.description
products.prod_price -> ozon.price
products.prod_count -> ozon.stocks
products.prod_images -> ozon.images (после загрузки в Ozon)
products.prod_manuf -> ozon.brand
products.prod_cat -> ozon.category_id
products.prod_size -> ozon.dimensions
products.prod_weight -> ozon.weight
```

**Обработка изображений:**
```typescript
// Алгоритм загрузки изображений
1. Получить настройки через loadSettings(['ozon_image_upload_enabled', 'ozon_image_base_url'])
2. Собрать список локальных изображений из prod_img, prod_img_rumi, prod_images
3. Загрузить изображения в Ozon через API
4. Получить URL загруженных изображений
5. Использовать URL в данных товара для Ozon
```

### OzonRouter (app/TRPC/ozonRouter.ts)
**Назначение:** tRPC роутер для интеграции с фронтендом

**Эндпоинты:**
- `syncProducts` - синхронизация выбранных товаров
- `getProductStatus` - получение статуса товара в Ozon
- `updateProductStock` - обновление остатков товара
- `getSyncHistory` - история синхронизации
- `getOzonSettings` - получение настроек интеграции
- `updateOzonSettings` - обновление настроек

## Предполагаемые методы Ozon Seller API

### Основные эндпоинты:
1. `POST /v2/product/import` - импорт товаров
2. `POST /v1/product/info` - получение информации о товарах
3. `POST /v1/product/info/stocks` - обновление остатков
4. `POST /v2/products/stocks` - массовое обновление остатков
5. `POST /v1/product/info/prices` - обновление цен
6. `GET /v2/category/tree` - получение дерева категорий

### Схема работы realFBS:
- Товары хранятся на складе продавца
- Ozon забирает товары после продажи
- Обязательные поля: offer_id, price, stocks, warehouse_id
- Необходимо поддерживать актуальные остатки

## Этапы реализации

### Этап 1: Базовая инфраструктура
1. Создать интерфейсы и типы данных
2. Реализовать OzonClient с аутентификацией через loadSettings
3. Настроить динамическую конфигурацию

### Этап 2: Система загрузки изображений
1. Реализовать методы загрузки изображений в Ozon API
2. Создать сервис для работы с локальными изображениями
3. Интегрировать с существующей структурой UPLOAD_PATH

### Этап 3: Провайдер данных
1. Создать OzonProvider с основными методами
2. Реализовать маппинг данных включая изображения
3. Добавить обработку ошибок

### Этап 4: Бизнес-логика
1. Создать OzonService с поддержкой изображений
2. Реализовать валидацию данных
3. Добавить логирование операций

### Этап 5: tRPC интеграция
1. Создать ozonRouter с методами для изображений
2. Добавить валидацию с Zod
3. Интегрировать в основной роутер

### Этап 6: Тестирование
1. Unit тесты для основных методов
2. Тесты загрузки изображений
3. Интеграционные тесты с API
4. Документация по использованию

## Использование

### Синхронизация товаров:
```typescript
// Через tRPC
const result = await trpc.ozon.syncProducts.mutate({
  productIds: [1, 2, 3, 4, 5]
});

// Напрямую через провайдер
const result = await ozonProvider.syncProducts([1, 2, 3, 4, 5]);
```

### Обновление остатков:
```typescript
await trpc.ozon.updateProductStock.mutate({
  productId: 1,
  stock: 10
});
```

## Безопасность и производительность

### Безопасность:
- API ключи хранятся в переменных окружения
- Валидация всех входящих данных
- Логирование всех операций

### Производительность:
- Батчевая обработка товаров
- Rate limiting для соблюдения лимитов API
- Кэширование настроек
- Асинхронная обработка

## Мониторинг и логирование

### Логирование:
- Все операции синхронизации
- Ошибки API и их причины
- Статистика успешных/неуспешных операций

### Мониторинг:
- Статус последней синхронизации
- Количество синхронизированных товаров
- Ошибки и их частота

---

**Готов к согласованию и началу реализации!**
