{"typescript": true, "commands": ["./commands", "@adonisjs/core/build/commands", "@adonisjs/repl/build/commands", "@adonisjs/lucid/build/commands", "@adonisjs/mail/build/commands"], "exceptionHandlerNamespace": "App/Exceptions/Handler", "aliases": {"App": "app", "Config": "config", "Database": "database", "Contracts": "contracts"}, "preloads": ["./start/routes", "./start/kernel", "./start/view", "./start/i18n", "./start/init", {"file": "./start/events", "environment": ["web"]}], "providers": ["./providers/AppProvider", "@adonisjs/core", "@adonisjs/session", "@adonisjs/view", "@adonisjs/lucid", "@adonisjs/auth", "@adonisjs/mail"], "metaFiles": [{"pattern": "public/**", "reloadServer": false}, {"pattern": "resources/views/**/*.edge", "reloadServer": false}], "aceProviders": ["@adonisjs/repl"]}