{"dump:rcfile": {"settings": {}, "commandPath": "@adonisjs/core/build/commands/DumpRc", "commandName": "dump:rcfile", "description": "Dump contents of .adonisrc.json file along with defaults", "args": [], "flags": []}, "list:routes": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/core/build/commands/ListRoutes", "commandName": "list:routes", "description": "List application routes", "args": [], "flags": [{"name": "json", "propertyName": "json", "type": "boolean", "description": "Output as JSON"}]}, "generate:key": {"settings": {}, "commandPath": "@adonisjs/core/build/commands/GenerateKey", "commandName": "generate:key", "description": "Generate a new APP_KEY secret", "args": [], "flags": []}, "repl": {"settings": {"loadApp": true, "environment": "repl", "stayAlive": true}, "commandPath": "@adonisjs/repl/build/commands/AdonisRepl", "commandName": "repl", "description": "Start a new REPL session", "args": [], "flags": []}, "db:seed": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/DbSeed", "commandName": "db:seed", "description": "Execute database seeder files", "args": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection for the seeders", "alias": "c"}, {"name": "interactive", "propertyName": "interactive", "type": "boolean", "description": "Run seeders in interactive mode", "alias": "i"}, {"name": "files", "propertyName": "files", "type": "array", "description": "Define a custom set of seeders files names to run", "alias": "f"}]}, "make:model": {"settings": {}, "commandPath": "@adonisjs/lucid/build/commands/MakeModel", "commandName": "make:model", "description": "Make a new Lucid model", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the model class"}], "flags": [{"name": "migration", "propertyName": "migration", "type": "boolean", "alias": "m", "description": "Generate the migration for the model"}, {"name": "controller", "propertyName": "controller", "type": "boolean", "alias": "c", "description": "Generate the controller for the model"}]}, "make:migration": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/MakeMigration", "commandName": "make:migration", "description": "Make a new migration file", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the migration file"}], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection for the migration"}, {"name": "folder", "propertyName": "folder", "type": "string", "description": "Pre-select a migration directory"}, {"name": "create", "propertyName": "create", "type": "string", "description": "Define the table name for creating a new table"}, {"name": "table", "propertyName": "table", "type": "string", "description": "Define the table name for altering an existing table"}]}, "make:seeder": {"settings": {}, "commandPath": "@adonisjs/lucid/build/commands/MakeSeeder", "commandName": "make:seeder", "description": "Make a new Seeder file", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the seeder class"}], "flags": []}, "migration:run": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Run", "commandName": "migration:run", "description": "Run pending migrations", "args": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explicitly force to run migrations in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Print SQL queries, instead of running the migrations"}]}, "migration:rollback": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Rollback", "commandName": "migration:rollback", "description": "Rollback migrations to a given batch number", "args": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}, {"name": "force", "propertyName": "force", "type": "boolean", "description": "Explictly force to run migrations in production"}, {"name": "dry-run", "propertyName": "dryRun", "type": "boolean", "description": "Print SQL queries, instead of running the migrations"}, {"name": "batch", "propertyName": "batch", "type": "number", "description": "Define custom batch number for rollback. Use 0 to rollback to initial state"}]}, "migration:status": {"settings": {"loadApp": true}, "commandPath": "@adonisjs/lucid/build/commands/Migration/Status", "commandName": "migration:status", "description": "Check migrations current status.", "args": [], "flags": [{"name": "connection", "propertyName": "connection", "type": "string", "description": "Define a custom database connection", "alias": "c"}]}, "make:mailer": {"settings": {}, "commandPath": "@adonisjs/mail/build/commands/MakeMailer", "commandName": "make:mailer", "description": "Make a new mailer class", "args": [{"type": "string", "propertyName": "name", "name": "name", "required": true, "description": "Name of the mailer class"}], "flags": []}}