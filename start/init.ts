// import { fillSizes } from "App/Helpers/updateProductSizes"
// import Client from "App/Models/Client"
// import User from "App/Models/User"
// import GenerateDynamicPluginsRoutes from "App/Plugins/GenerateDynamicPluginsRoutes"

import TelegramServie from 'App/Plugins/Telegram'

import Order from 'App/Models/Order'

// import Product from 'App/Models/Product'
// import User from 'App/Models/User'
// import OrderSnapshot from 'App/Models/OrderSnapshot'
// import Database from '@ioc:Adonis/Lucid/Database'

// import { gracefulShutdown, scheduleJob } from 'node-schedule'
// import StatisticsController from 'App/Controllers/Http/StatisticsController'
// import HttpContext from '@ioc:Adonis/Core/HttpContext'
// import Request from '@ioc:Adonis/Core/Request'
import Env from '@ioc:Adonis/Core/Env'
import got from 'got'
import Application from '@ioc:Adonis/Core/Application'

import { ToadScheduler, SimpleIntervalJob, AsyncTask } from 'toad-scheduler'
import Statistic from 'App/Models/Statistic'

import * as path from 'path'
import * as fs from 'fs'
import { checkAndCreatePathToDir } from 'App/Helpers/checkPathToDir'
import { MeiliSearchPlugin } from 'App/Plugins/MeiliSearch'
import { EmailMonitorService } from 'App/Services/EmailProcessingService'

const UPLOAD_PATH = path.resolve(Env.get('UPLOAD_PATH'))

console.log('API INIT NODE_APP_INSTANCE: ', process.env.NODE_APP_INSTANCE)


async function checkEmails() {
  try {
    const emailMonitorService = new EmailMonitorService()
    await emailMonitorService.start()
  } catch (error) {
    console.error('❌ Ошибка при инициализации Email Monitor:', error)
  }
}


async function checkOrders() {
  await Order.checkList({ isRumi: false, toEmail: true })
  await Order.checkList({ isRumi: true, toEmail: true })
}

async function makeReports_2() {
  // console.log('start makeReports_2:', new Date().toLocaleString())
  try {
    await Statistic.salesByFilters({
      oldPrice: false,
      calcGroupSum: false,
      dateRange: ['2017-12-31T23:00:00.000Z', '2048-07-30T22:00:00.000Z'],
      groupby: ['prod_manuf', 'order_year', 'order_month']
    })

    await Statistic.salesByFilters({
      oldPrice: false,
      calcGroupSum: false,
      dateRange: ['2017-12-31T23:00:00.000Z', '2048-07-30T22:00:00.000Z'],
      groupby: ['cat_title', 'order_year', 'order_month']
    })

    await Statistic.salesByFilters({
      oldPrice: false,
      calcGroupSum: false,
      dateRange: ['2017-12-31T23:00:00.000Z', '2048-07-30T22:00:00.000Z'],
      groupby: ['order_shipping', 'order_year', 'order_month']
    })
  } catch (error) {
    console.error('make reports error:', error)
  }
}
async function makeReports_1() {
  // console.log('start makeReports_1:', new Date().toLocaleString())
  try {
    await Statistic.statList({
      limit: 80,
      searchvalue: '',
      isNoStock: true
    })

    await Statistic.statsByOrders({
      groupby: ['shop', 'year', 'month'],
      sumby: 'orderSum'
    })

    await Statistic.statsByOrders({
      groupby: ['order_shipping', 'year', 'month'],
      shippingtypes: ['Почта РФ', 'Курьер'],
      sumby: 'order_shippingprice'
    })
  } catch (error) {
    console.error('make reports error:', error)
  }
}

if (Application.inProduction && (process.env.NODE_APP_INSTANCE == '0' || !process.env.NODE_APP_INSTANCE)) {
  const scheduler = new ToadScheduler()

  const checkEmails_task = new AsyncTask(
    'checkEmails',
    () => {
      // console.log('start report task1...\n current jobs: ', scheduler.getAllJobs())
      return checkEmails()
    },
    (err: Error) => {
      console.error('scheduler error: checkEmails: ', err)
    }
  )

  const check_orders_task = new AsyncTask(
    'check_orders',
    () => {
      const d = new Date()
      const h = d.getHours()
      const m = d.getMinutes()
      if (h == 10 && m <= 59) {
        // console.log('now h 10, start checkOrders...')
        return checkOrders()
      } else {
        return new Promise((resolve, reject) => resolve(true))
      }
    },
    (err: Error) => {
      console.error('scheduler error: check_orders: ', err)
    }
  )

  const make_reports_task_1 = new AsyncTask(
    'make_reports_1',
    () => {
      // console.log('start report task1...\n current jobs: ', scheduler.getAllJobs())
      return makeReports_1()
    },
    (err: Error) => {
      console.error('scheduler error: make_reports: ', err)
    }
  )

  const make_reports_task_2 = new AsyncTask(
    'make_reports_2',
    () => {
      // console.log('start report task2...\n current jobs: ', scheduler.getAllJobs())
      return makeReports_2()
    },
    (err: Error) => {
      console.error('scheduler error: make_reports: ', err)
    }
  )

  // Добавляем новую задачу для MeiliSearch
  const meili_sync_task = new AsyncTask(
    'meili_sync',
    () => {
      const d = new Date()
      const h = d.getHours()
      const m = d.getMinutes()

      if (h === 2 && m <= 5) {
        console.log('Starting MeiliSearch sync...')
        const meili = new MeiliSearchPlugin({})
        return meili.syncAllByPrisma()
      }
      return Promise.resolve(true)
    },
    (err: Error) => {
      console.error('scheduler error: meili_sync:', err)
    }
  )
  const check_emails_job = new SimpleIntervalJob({ minutes: 30, runImmediately: true }, checkEmails_task)

  const check_orders_job = new SimpleIntervalJob({ hours: 1, runImmediately: false }, check_orders_task)
  const make_reports_job_1 = new SimpleIntervalJob({ minutes: 18, runImmediately: false }, make_reports_task_1)
  const make_reports_job_2 = new SimpleIntervalJob({ minutes: 25, runImmediately: true }, make_reports_task_2)
  const meili_sync_job = new SimpleIntervalJob({ hours: 1, runImmediately: false }, meili_sync_task)

  // scheduler.addSimpleIntervalJob(make_reports_job_1)
  // scheduler.addSimpleIntervalJob(make_reports_job_2)

  scheduler.addSimpleIntervalJob(check_emails_job)
  scheduler.addSimpleIntervalJob(check_orders_job)
  scheduler.addSimpleIntervalJob(meili_sync_job)

  // check workdirs

  // checkAndCreatePathToDir()
}

const folders = ['video', 'video/rti', 'video/rumi', 'docs/specs/temp', '_tempdocs']

folders.forEach((folder) => {
  const folderPath = `${UPLOAD_PATH}/${folder}`
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true })
    console.log(`Папка ${folderPath} успешно создана.`)
  }
})

