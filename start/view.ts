import { viewLoader } from 'App/Helpers/viewLoader'
import TemplateView from 'App/Models/TemplateView'
import View from '@ioc:Adonis/Core/View'
import loadSettings from 'App/Helpers/loadSettings'
;(async function load() {
  const { senders } = await loadSettings(['senders'])

  View.global('senders', senders)

  const dbviews = await TemplateView.query().select('name')
  const views = dbviews.map((view) => view.name)
  await Promise.all(views.map(viewLoader))
})()
