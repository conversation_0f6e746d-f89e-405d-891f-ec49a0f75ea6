import Application from '@ioc:Adonis/Core/Application'
import Env from '@ioc:Adonis/Core/Env'
import { reverseObject } from 'App/Helpers/reverseObject'
import LangDict from 'App/Models/LangDict'
import i18next from 'i18next'

async function getResources() {
  const dicts = [
    //format
    'en',
    'pl',
    // 'de',
    'ru',
    'es',
    // 'it',
    'fr',
    'ar'
  ]
  let resources = {}

  await Promise.all(
    dicts.map(async (dict) => {
      try {
        let translation = await LangDict.getDict(dict)
        resources[dict] = { translation }
      } catch (error) {
        //console.error('getResources: ', error)
      }
    })
  )

  return resources
}

async function init() {
  const resources = await getResources()
  await i18next.init({
    lng: 'en',
    debug: false, //!Application.inProduction,
    resources
  })
}

init()

setInterval(() => {
  init()
}, Number(Env.get('RELOAD_LANGS_INTERVAL', 900000)))
