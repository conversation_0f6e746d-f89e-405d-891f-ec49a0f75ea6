# Руководство по использованию интеграции с Ozon Seller API

## Обзор

Интеграция с Ozon Seller API позволяет автоматически синхронизировать товары из внутренней базы данных с маркетплейсом Ozon по схеме realFBS.

## Настройка

### 1. Настройки в базе данных

Все настройки хранятся в таблице `settings` и загружаются динамически через функцию `loadSettings`:

```sql
-- Основные настройки API
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_api_key', 'c13fe670-c07e-4836-b631-4ada8009d967', false, false),
('ozon_client_id', '3054932', false, false),
('ozon_api_url', 'https://api-seller.ozon.ru', false, false),
('ozon_warehouse_id', '1', false, false);

-- Настройки синхронизации
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_sync_enabled', 'true', false, false),
('ozon_auto_sync_interval', '60', false, false),
('ozon_default_category_id', '17029016', false, false);

-- Настройки изображений
INSERT INTO settings (s_key, s_value, json, syst) VALUES 
('ozon_image_upload_enabled', 'true', false, false),
('ozon_image_base_url', 'https://mirsalnikov.ru/data/original', false, false),
('ozon_max_images_per_product', '10', false, false);
```

### 2. Получение API ключей Ozon

1. Войдите в личный кабинет Ozon Seller
2. Перейдите в раздел "Настройки" → "API ключи"
3. Создайте новый ключ с типом "Товары"
4. Скопируйте API-ключ и Client-Id

## Использование через tRPC

### Синхронизация товаров

```typescript
// Синхронизация массива товаров
const result = await trpc.ozon.syncProducts.mutate({
  productIds: [1, 2, 3, 4, 5],
  batchSize: 10,
  uploadImages: true
});

console.log(`Синхронизировано: ${result.synced} из ${result.total}`);
```

### Обновление остатков

```typescript
// Обновление остатков товара
const result = await trpc.ozon.updateProductStock.mutate({
  productId: 123,
  stock: 50
});

if (result.success) {
  console.log('Остатки обновлены');
}
```

### Обновление цен

```typescript
// Обновление цены товара
const result = await trpc.ozon.updateProductPrice.mutate({
  productId: 123,
  price: 1500,
  oldPrice: 2000 // Опционально для показа скидки
});
```

### Получение статуса товара

```typescript
// Получение статуса товара в Ozon
const status = await trpc.ozon.getProductStatus.query({
  productId: 123
});

console.log('Статус:', status.status);
console.log('Активен:', status.isActive);
console.log('Ошибки:', status.errors);
```

### Загрузка изображений

```typescript
// Загрузка изображений товара в Ozon
const result = await trpc.ozon.uploadProductImages.mutate({
  productId: 123
});

console.log('Загружено изображений:', result.uploadedImages.length);
```

### Управление настройками

```typescript
// Получение настроек
const settings = await trpc.ozon.getOzonSettings.query();

// Обновление настроек
await trpc.ozon.updateOzonSettings.mutate({
  ozon_sync_enabled: true,
  ozon_auto_sync_interval: 30,
  ozon_image_upload_enabled: true
});
```

### Проверка подключения

```typescript
// Тест подключения к Ozon API
const test = await trpc.ozon.testConnection.query();

if (test.success) {
  console.log('Подключение успешно');
} else {
  console.error('Ошибка подключения:', test.message);
}
```

## Использование напрямую через провайдеры

### OzonProvider

```typescript
import { ozonProvider } from 'App/Providers/OzonProvider';

// Синхронизация товаров
const result = await ozonProvider.syncProducts([1, 2, 3]);

// Обновление остатков
await ozonProvider.updateStocks([{
  offer_id: 'SKU123',
  stock: 10,
  warehouse_id: 123456
}]);

// Получение информации о товаре
const productInfo = await ozonProvider.getProductInfo('SKU123');
```

### OzonService

```typescript
import { ozonService } from 'App/Services/OzonService';

// Маппинг товара в формат Ozon
const product = await prisma.products.findUnique({ where: { prod_id: 123 } });
const ozonProduct = await ozonService.mapProductToOzon(product);

// Валидация данных
const validation = await ozonService.validateProductData(ozonProduct);
if (!validation.isValid) {
  console.error('Ошибки валидации:', validation.errors);
}

// Загрузка изображений
const imageResult = await ozonService.uploadProductImages(product);
```

## Структура данных

### Маппинг полей товара

| Поле БД | Поле Ozon | Описание |
|---------|-----------|----------|
| `prod_sku` | `offer_id` | Уникальный идентификатор товара |
| `prod_note` | `name`, `description` | Название и описание |
| `prod_price` | `price` | Цена товара |
| `prod_count` | `stocks` | Остатки на складе |
| `prod_images` | `images` | Изображения товара |
| `prod_manuf` | `attributes[brand]` | Бренд товара |
| `prod_cat` | `category_id` | Категория товара |
| `prod_weight` | `weight` | Вес товара |

### Изображения товаров

Система автоматически обрабатывает изображения из папок:
- `UPLOAD_PATH/rti/` - основные изображения RTI
- `UPLOAD_PATH/rumi/` - изображения RUMI

Поддерживаемые поля:
- `prod_img` - основное изображение RTI
- `prod_img_rumi` - основное изображение RUMI  
- `prod_images` - дополнительные изображения (через запятую)

## Мониторинг и логирование

### История синхронизации

```typescript
// Получение истории синхронизации
const history = await trpc.ozon.getSyncHistory.query({
  productId: 123, // Опционально
  limit: 50
});
```

### Статистика

```typescript
// Получение статистики синхронизации
const stats = await trpc.ozon.getSyncStats.query();

console.log('Всего товаров:', stats.totalProducts);
console.log('Синхронизировано:', stats.syncedProducts);
console.log('Ошибок:', stats.failedProducts);
```

## Обработка ошибок

### Типичные ошибки и решения

1. **"Ozon synchronization is disabled"**
   - Включите синхронизацию: `ozon_sync_enabled = true`

2. **"Product not found or has no SKU"**
   - Убедитесь, что у товара заполнено поле `prod_sku`

3. **"API key is invalid"**
   - Проверьте правильность API ключа и Client ID

4. **"Category not found"**
   - Установите корректный `ozon_default_category_id`

5. **"Image upload failed"**
   - Проверьте существование файлов изображений
   - Убедитесь, что `ozon_image_upload_enabled = true`

### Retry логика

Система автоматически повторяет запросы при временных ошибках:
- Rate limit (429) - повтор через 1 минуту
- Серверные ошибки (5xx) - повтор через 5 секунд
- Сетевые ошибки - повтор через 3 секунды

## Лимиты и ограничения

### API лимиты Ozon
- 100 запросов в минуту
- Максимум 100 товаров в одном запросе импорта
- Максимум 10 изображений на товар
- Максимальный размер изображения: 10MB

### Настройки системы
- `ozon_max_images_per_product` - лимит изображений (по умолчанию 10)
- `batchSize` в синхронизации - размер пакета (рекомендуется 10)

## Безопасность

1. **API ключи** хранятся в базе данных, не в коде
2. **Валидация** всех входящих данных через Zod схемы
3. **Логирование** всех операций для аудита
4. **Rate limiting** для соблюдения лимитов API

## Производительность

### Рекомендации по оптимизации

1. **Батчевая обработка** - синхронизируйте товары пакетами по 10-20 штук
2. **Кэширование настроек** - настройки кэшируются на 1 минуту
3. **Асинхронная обработка** - используйте фоновые задачи для больших объемов
4. **Мониторинг** - отслеживайте статистику и ошибки

### Пример массовой синхронизации

```typescript
// Синхронизация большого количества товаров
const allProductIds = [1, 2, 3, /* ... */ 1000];
const batchSize = 10;

for (let i = 0; i < allProductIds.length; i += batchSize) {
  const batch = allProductIds.slice(i, i + batchSize);
  
  try {
    const result = await trpc.ozon.syncProducts.mutate({
      productIds: batch,
      batchSize: batchSize
    });
    
    console.log(`Batch ${i/batchSize + 1}: ${result.synced}/${result.total} synced`);
    
    // Пауза между пакетами для соблюдения rate limit
    await new Promise(resolve => setTimeout(resolve, 1000));
    
  } catch (error) {
    console.error(`Batch ${i/batchSize + 1} failed:`, error.message);
  }
}
```

## Поддержка

При возникновении проблем:

1. Проверьте логи в консоли
2. Используйте `testConnection` для проверки API
3. Проверьте настройки через `getOzonSettings`
4. Изучите историю синхронизации через `getSyncHistory`
